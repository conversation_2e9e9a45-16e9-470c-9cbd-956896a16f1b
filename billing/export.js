const mdb = require("../mdb.js");
const ExcelJS = require('exceljs');

const BILLINGMONTH=202508;

let workbook = new ExcelJS.Workbook();
let sheets={};

let clientCodes=[];

mdb.connect().then(async ()=>{
    let records = await mdb.client().prod.collection("jira_billing").find({"month":BILLINGMONTH}).toArray();

    for (let record of records){
        if(!record.client){ record.client = "MISSING"; }
        if(typeof sheets[record.client]=="undefined"){ 
            sheets[record.client]={
                "records":[],
                "worksheet":workbook.addWorksheet(record.client)}; 
        }
        sheets[record.client].records.push(record);
    }

    Object.keys(sheets).forEach(clientCode=>{
        sheets[clientCode].worksheet.addRows(sheets[clientCode].records);
        console.log(sheets[clientCode].records, clientCode);
    });
    
    workbook.xlsx.writeFile("./SAMPLE.xlsx");
});