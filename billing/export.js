const mdb = require("../mdb.js");
const ExcelJS = require('exceljs');

const BILLINGMONTH=202508;

let workbook = new ExcelJS.Workbook();
let sheets={};

let clientCodes=[];

mdb.connect().then(async ()=>{
    let records = await mdb.client().prod.collection("jira_billing").find({"month":BILLINGMONTH}).toArray();
    console.log(`Found ${records.length} total records for month ${BILLINGMONTH}`);

    for (let record of records){
        if(!record.client){ record.client = "MISSING"; }
        if(typeof sheets[record.client]=="undefined"){ 
            sheets[record.client]={
                "records":[],
                "worksheet":workbook.addWorksheet(record.client)}; 
        }
        delete record._id;
        sheets[record.client].records.push(record);
    }

    for (const clientCode of Object.keys(sheets)) {
        console.log(`Processing client: ${clientCode} with ${sheets[clientCode].records.length} records`);
        const sampleRecord = sheets[clientCode].records[0];
        console.log('Sample record keys:', Object.keys(sampleRecord || {}));

        // Define columns based on the first record
        const worksheet = sheets[clientCode].worksheet;
        const columns = Object.keys(sampleRecord).map(key => ({
            header: key,
            key: key,
            width: 15
        }));
        worksheet.columns = columns;

        // Now add the data
        worksheet.addRows(sheets[clientCode].records);
        console.log(`Worksheet ${clientCode} now has ${worksheet.rowCount} rows`);
    }

    await workbook.xlsx.writeFile("./SAMPLE.xlsx");
    console.log("Excel file written successfully!");
});