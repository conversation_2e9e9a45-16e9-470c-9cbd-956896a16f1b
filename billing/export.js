const mdb = require("../mdb.js");
const ExcelJS = require('exceljs');
const BILLINGMONTH=202508;

const common={
      objsToArray:function(objs){
    let output=[];

    if(objs && objs.length>0){      
      output.push(Object.keys(objs[0])); // Header

      objs.forEach(obj=>{
        let row=[];
        for(let i=0; i<output[0].length; i++){
          if(obj[output[0][i]] && typeof obj[output[0][i]]==="object" && obj[output[0][i]].length>0){
            obj[output[0][i]] = obj[output[0][i]].join(",");
          }
          row.push(obj[output[0][i]]);
        }
        output.push(row);
      });
    }
    
    return output;

  }
}

let workbook = new ExcelJS.Workbook();
let sheets={};

let clientCodes=[];

mdb.connect().then(async ()=>{
    let records = await mdb.client().prod.collection("jira_billing").find({"month":BILLINGMONTH}).toArray();

    for (let record of records){
        if(!record.client){ record.client = "MISSING"; }
        if(typeof sheets[record.client]=="undefined"){
            sheets[record.client]={
                "records":[],
                "worksheet":workbook.addWorksheet(record.client)};
        }

        sheets[record.client].worksheet.columns = [            
            { header: 'Board', key: 'board', width: 10 },
            { header: 'Parent Item', key: 'parent_item', width: 50 },
            { header: 'Item', key: 'item', width: 50 },     
            { header: 'Description', key: 'description', width: 50 },            
            { header: 'Client', key: 'client', width: 10 },
            { header: 'Rate Code', key: 'rate_code', width: 10 },
            { header: 'Rate', key: 'rate', width: 10, style: { numFmt: '$#,##0.00' } },                   
            { header: 'Hours', key: 'hours', width: 10 },
            { header: 'Expense', key: 'expense', width: 10, style: { numFmt: '$#,##0.00' } },
            { header: 'Expense Cat', key: 'expense_cat', width: 15 },
            { header: 'Shipping', key: 'shipping', width: 10, style: { numFmt: '$#,##0.00' } },
            { header: 'Total Billable', key: 'total', width: 10, style: { numFmt: '$#,##0.00' }  },
            { header: 'Budget Src', key: 'budget_src', width: 15 },
            { header: 'Event Code', key: 'event_code', width: 15 },
            { header: 'Event Jira Code', key: 'event_jira_code', width: 15 },
            { header: 'Parent Key', key: 'parent_key', width: 15 },
            { header: 'Issue Key', key: 'issue_key', width: 15 },
            { header: 'Labels', key: 'labels', width: 50 }
        ];

        delete record._id;
        record.hours = parseFloat(((record.minutes || 0) / 60).toFixed(2));
        record.total = parseFloat((record.hours * record.rate + (record.expense || 0) + (record.shipping || 0)).toFixed(2));
        sheets[record.client].records.push(record);
    }

    Object.keys(sheets).forEach(clientCode=>{
        sheets[clientCode].worksheet.addRows((sheets[clientCode].records));
        console.log(sheets[clientCode].records, clientCode);
    });

    workbook.xlsx.writeFile("./SAMPLE.xlsx");
});