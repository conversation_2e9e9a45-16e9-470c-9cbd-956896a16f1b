const fs = require('fs');
const csv = require("fast-csv");
const mdb = require("../mdb.js");
const BILLINGMONTH=202508;

const CLIENTCODES=["LUSA-FS","LUSA-R", "SPC", "UNC", "GCC", "BMG", "RICHS", "PERRYS"];
const RATECODES={
    "STRATEGY":{"amt":175},
    "DEV":{"amt":150},
    "CONTENT":{"amt":125},
    "DATAMGT":{"amt":115},
    "ACCTMGT":{"amt":115},
    "PRODMGT":{"amt":115},
    "SMS":{"amt":100},
    "FULFILL":{"amt":80},
    "TRAVEL":{"amt":80},
    "NOBILL":{"amt":0}
}

let records=[]


if(!BILLINGMONTH){
    process.exit();
}


async function processFile(file){
    return new Promise(async (resolve, reject)=>{
            let records = [];
            fs.createReadStream(`./files/${file}`)
                .pipe(csv.parse({ headers: true }))
                .on('error', error => {
                    console.error(error);
                    reject(error);
                })
                .on('data', row => records.push(row))
                .on('end', rowCount => { 
                    resolve(records);
                });
    });
}

async function getFiles(){
    return new Promise(async (resolve, reject)=>{
        fs.readdir("./files", (err, files) => {
            if (err) {
                console.log("Error reading directory:", err);
                resolve([]);
            } else {
                resolve(files);
            }
        });
    });
}



mdb.connect().then(async ()=>{

    let csvFiles = await getFiles();

    for(let file of csvFiles){
        let processedOn = new Date();
        processFile(file).then(async jiraItems=>{
            let dataToInsert=[];
            for(let item of jiraItems){

                let record ={
                    "month":BILLINGMONTH,
                    "board":item["Project Key"],
                    "issue_key":item["Issue Key"],
                    "item":item["Issue Summary"],
                    "parent_key":item["Parent Key"],
                    "parent_item":item["Parent Summary"],
                    "description":item["Comment"],
                    "labels":item["Labels"],
                    "client":null,
                    "rate_code":null,
                    "rate":0,
                    "minutes":item["Time Spent"]?parseFloat(item["Time Spent"]):0,                    
                    "expense":item["Expense"]?parseFloat(item["Expense"]):0,
                    "expense_cat":item["Expense Category"],
                    "shipping":item["Shipping Expense"]?parseFloat(item["Shipping Expense"]):0,
                    "budget_src":item["Client Budget Source"],
                    "event_code":item["Event Code"],
                    "event_jira_code":item["Event Jira Code"],
                    "imported":processedOn                
                };

                item["Labels"].split(",").map(l=>l.trim().toUpperCase()).forEach(label=>{
                    if(CLIENTCODES.includes(label)){
                        record.client=label;
                    }
                    if(Object.keys(RATECODES).includes(label)){
                        record.rate_code=label;
                        record.rate=RATECODES[label].amt;
                    }
                });

                dataToInsert.push(record);                
            }
            await mdb.client().prod.collection("jira_billing").insertMany(dataToInsert); 
            console.log("Inserted "+dataToInsert.length+" records from "+file);
        });
    }
});

