<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<styleSheet
        xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="x14ac"
        xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac">
    <% if (numFmts.length) { %><numFmts count="<%=numFmts.length%>">
        <%numFmts.forEach(function(nf) {%><%=nf.xml%>
        <%});%>
    </numFmts>
    <% } %>
    <fonts count="<%=fonts.length%>" x14ac:knownFonts="1">
        <%fonts.forEach(function(font) {%><%=font.xml%>
        <%})%>
    </fonts>
    <fills count="<%=fills.length%>">
        <%fills.forEach(function(fill) {%><%=fill.xml%>
        <%});%>
    </fills>
    <borders count="<%=borders.length%>">
        <%borders.forEach(function(border) {%><%=border.xml%>
        <%});%>
    </borders>
    <cellStyleXfs count="1">
        <xf numFmtId="0" fontId="0" fillId="0" borderId="0"/>
    </cellStyleXfs>
    <cellXfs count="<%=styles.length%>">
        <%styles.forEach(function(style) {%><%=style.xml%>
        <%});%>
    </cellXfs>
    <cellStyles count="1">
        <cellStyle name="Normal" xfId="0" builtinId="0"/>
    </cellStyles>
    <dxfs count="0"/>
    <tableStyles count="0" defaultTableStyle="TableStyleMedium2" defaultPivotStyle="PivotStyleLight16"/>
    <extLst>
        <ext uri="{EB79DEF2-80B8-43e5-95BD-54CBDDF9020C}" xmlns:x14="http://schemas.microsoft.com/office/spreadsheetml/2009/9/main">
            <x14:slicerStyles defaultSlicerStyle="SlicerStyleLight1"/>
        </ext>
    </extLst>
</styleSheet>