{"name": "exceljs", "version": "4.4.0", "description": "Excel Workbook Manager - Read and Write xlsx and csv Files.", "private": false, "license": "MIT", "author": {"name": "<PERSON><PERSON> Roche", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/exceljs/exceljs.git"}, "engines": {"node": ">=8.3.0"}, "main": "./excel.js", "browser": "./dist/exceljs.min.js", "types": "./index.d.ts", "files": ["dist", "lib", "excel.js", "LICENSE", "README.md", "README_zh.md", "index.ts", "index.d.ts"], "scripts": {"test": "npm run test:full", "test:es5": "export EXCEL_BUILD=es5 && npm run test:full", "test:full": "npm run build && npm run test:unit && npm run test:integration && npm run test:end-to-end && npm run test:jasmine", "test:version": "npm run build && npm run test:unit && npm run test:integration && npm run test:end-to-end && npm run test:browser && npm run test:dist", "test:all": "npm run test:native && npm run test:es5", "test:native": "npm run test:full", "test:unit": "mocha --require spec/config/setup --require spec/config/setup-unit spec/unit --recursive", "test:integration": "mocha --require spec/config/setup spec/integration --recursive", "test:end-to-end": "mocha --require spec/config/setup spec/end-to-end --recursive", "test:browser": "if [ ! -f .disable-test-browser ]; then npm run build && npm run test:jasmine; fi", "test:jasmine": "grunt jasmine", "test:unit:es5": "export EXCEL_BUILD=es5 && npm run test:unit", "test:integration:es5": "export EXCEL_BUILD=es5 && npm run test:integration", "test:end-to-end:es5": "export EXCEL_BUILD=es5 && npm run test:end-to-end", "test:dist": "mocha --require spec/config/setup spec/dist --recursive", "test:manual": "node spec/manual/app.js", "test:typescript": "mocha -r ts-node/register spec/typescript/**/*.spec.ts", "clean-build": "npm run clean && npm run build", "lint": "eslint --format node_modules/eslint-friendly-formatter .", "lint:fix": "prettier-eslint --write $(pwd)'/**/*.js'", "lint:staged": "lint-staged", "clean": "rm -rf build/ && rm -rf dist", "benchmark": "node --expose-gc benchmark", "benchmark:debug": "node --expose-gc --inspect-brk --trace-deopt benchmark", "build": "grunt build", "install-build": "npm install && grunt build", "preversion": "npm run clean && npm run build && npm run test:version", "postversion": "git push --no-verify && git push --tags --no-verify"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["prettier-eslint --write", "eslint --format node_modules/eslint-friendly-formatter", "git add"]}, "keywords": ["xlsx", "json", "csv", "excel", "font", "border", "fill", "number", "format", "number format", "alignment", "office", "spreadsheet", "workbook", "defined names", "data validations", "rich text", "in-cell format", "outlineLevel", "views", "frozen", "split", "pageSetup"], "dependencies": {"archiver": "^5.0.0", "dayjs": "^1.8.34", "fast-csv": "^4.3.1", "jszip": "^3.10.1", "readable-stream": "^3.6.0", "saxes": "^5.0.1", "tmp": "^0.2.0", "unzipper": "^0.10.11", "uuid": "^8.3.0"}, "devDependencies": {"@babel/cli": "^7.10.5", "@babel/core": "^7.11.4", "@babel/preset-env": "^7.11.0", "@types/chai": "^4.2.12", "@types/mocha": "^8.0.3", "@types/node": "^14.11.2", "babelify": "^10.0.0", "browserify": "^16.5.2", "chai": "^4.2.0", "chai-datetime": "^1.7.0", "chai-xml": "^0.3.2", "core-js": "^3.6.5", "dirty-chai": "^2.0.1", "eslint": "^6.5.1", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-prettier": "^6.12.0", "eslint-friendly-formatter": "^4.0.1", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "express": "^4.16.4", "got": "^9.0.0", "grunt": "^1.3.0", "grunt-babel": "^8.0.0", "grunt-browserify": "^5.3.0", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-jasmine": "^2.2.0", "grunt-contrib-watch": "^1.1.0", "grunt-exorcise": "^2.1.1", "grunt-terser": "^1.0.0", "husky": "^4.3.0", "lint-staged": "^10.2.13", "mocha": "^7.2.0", "prettier-eslint": "^11.0.0", "prettier-eslint-cli": "^5.0.0", "regenerator-runtime": "^0.13.7", "sax": "^1.2.4", "ts-node": "^8.10.2", "typescript": "^3.9.7"}}