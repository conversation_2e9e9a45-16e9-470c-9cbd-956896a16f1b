{"version": 3, "file": "stuttered-pipe.js", "names": ["events", "require", "StutteredPipe", "EventEmitter", "constructor", "readable", "writable", "options", "bufSize", "autoPause", "paused", "eod", "scheduled", "on", "end", "resume", "_schedule", "pause", "clearImmediate", "setImmediate", "data", "read", "length", "write", "module", "exports"], "sources": ["../../../lib/utils/stuttered-pipe.js"], "sourcesContent": ["const events = require('events');\n\n// =============================================================================\n// StutteredPipe - Used to slow down streaming so GC can get a look in\nclass StutteredPipe extends events.EventEmitter {\n  constructor(readable, writable, options) {\n    super();\n\n    options = options || {};\n\n    this.readable = readable;\n    this.writable = writable;\n    this.bufSize = options.bufSize || 16384;\n    this.autoPause = options.autoPause || false;\n\n    this.paused = false;\n    this.eod = false;\n    this.scheduled = null;\n\n    readable.on('end', () => {\n      this.eod = true;\n      writable.end();\n    });\n\n    // need to have some way to communicate speed of stream\n    // back from the consumer\n    readable.on('readable', () => {\n      if (!this.paused) {\n        this.resume();\n      }\n    });\n    this._schedule();\n  }\n\n  pause() {\n    this.paused = true;\n  }\n\n  resume() {\n    if (!this.eod) {\n      if (this.scheduled !== null) {\n        clearImmediate(this.scheduled);\n      }\n      this._schedule();\n    }\n  }\n\n  _schedule() {\n    this.scheduled = setImmediate(() => {\n      this.scheduled = null;\n      if (!this.eod && !this.paused) {\n        const data = this.readable.read(this.bufSize);\n        if (data && data.length) {\n          this.writable.write(data);\n\n          if (!this.paused && !this.autoPause) {\n            this._schedule();\n          }\n        } else if (!this.paused) {\n          this._schedule();\n        }\n      }\n    });\n  }\n}\n\nmodule.exports = StutteredPipe;\n"], "mappings": ";;AAAA,MAAMA,MAAM,GAAGC,OAAO,CAAC,QAAQ,CAAC;;AAEhC;AACA;AACA,MAAMC,aAAa,SAASF,MAAM,CAACG,YAAY,CAAC;EAC9CC,WAAWA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACvC,KAAK,CAAC,CAAC;IAEPA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IAEvB,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,OAAO,GAAGD,OAAO,CAACC,OAAO,IAAI,KAAK;IACvC,IAAI,CAACC,SAAS,GAAGF,OAAO,CAACE,SAAS,IAAI,KAAK;IAE3C,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,GAAG,GAAG,KAAK;IAChB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErBP,QAAQ,CAACQ,EAAE,CAAC,KAAK,EAAE,MAAM;MACvB,IAAI,CAACF,GAAG,GAAG,IAAI;MACfL,QAAQ,CAACQ,GAAG,CAAC,CAAC;IAChB,CAAC,CAAC;;IAEF;IACA;IACAT,QAAQ,CAACQ,EAAE,CAAC,UAAU,EAAE,MAAM;MAC5B,IAAI,CAAC,IAAI,CAACH,MAAM,EAAE;QAChB,IAAI,CAACK,MAAM,CAAC,CAAC;MACf;IACF,CAAC,CAAC;IACF,IAAI,CAACC,SAAS,CAAC,CAAC;EAClB;EAEAC,KAAKA,CAAA,EAAG;IACN,IAAI,CAACP,MAAM,GAAG,IAAI;EACpB;EAEAK,MAAMA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACJ,GAAG,EAAE;MACb,IAAI,IAAI,CAACC,SAAS,KAAK,IAAI,EAAE;QAC3BM,cAAc,CAAC,IAAI,CAACN,SAAS,CAAC;MAChC;MACA,IAAI,CAACI,SAAS,CAAC,CAAC;IAClB;EACF;EAEAA,SAASA,CAAA,EAAG;IACV,IAAI,CAACJ,SAAS,GAAGO,YAAY,CAAC,MAAM;MAClC,IAAI,CAACP,SAAS,GAAG,IAAI;MACrB,IAAI,CAAC,IAAI,CAACD,GAAG,IAAI,CAAC,IAAI,CAACD,MAAM,EAAE;QAC7B,MAAMU,IAAI,GAAG,IAAI,CAACf,QAAQ,CAACgB,IAAI,CAAC,IAAI,CAACb,OAAO,CAAC;QAC7C,IAAIY,IAAI,IAAIA,IAAI,CAACE,MAAM,EAAE;UACvB,IAAI,CAAChB,QAAQ,CAACiB,KAAK,CAACH,IAAI,CAAC;UAEzB,IAAI,CAAC,IAAI,CAACV,MAAM,IAAI,CAAC,IAAI,CAACD,SAAS,EAAE;YACnC,IAAI,CAACO,SAAS,CAAC,CAAC;UAClB;QACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAACN,MAAM,EAAE;UACvB,IAAI,CAACM,SAAS,CAAC,CAAC;QAClB;MACF;IACF,CAAC,CAAC;EACJ;AACF;AAEAQ,MAAM,CAACC,OAAO,GAAGvB,aAAa"}