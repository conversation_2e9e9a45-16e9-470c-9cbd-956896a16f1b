{"version": 3, "file": "stream-base64.js", "names": ["Stream", "require", "StreamBase64", "Duplex", "constructor", "pipes", "write", "cork", "uncork", "end", "read", "setEncoding", "encoding", "pause", "resume", "isPaused", "pipe", "destination", "push", "unpipe", "filter", "unshift", "Error", "wrap", "module", "exports"], "sources": ["../../../lib/utils/stream-base64.js"], "sourcesContent": ["const Stream = require('readable-stream');\n\n// =============================================================================\n// StreamBase64 - A utility to convert to/from base64 stream\n// Note: does not buffer data, must be piped\nclass StreamBase64 extends Stream.Duplex {\n  constructor() {\n    super();\n\n    // consuming pipe streams go here\n    this.pipes = [];\n  }\n\n  // writable\n  // event drain - if write returns false (which it won't), indicates when safe to write again.\n  // finish - end() has been called\n  // pipe(src) - pipe() has been called on readable\n  // unpipe(src) - unpipe() has been called on readable\n  // error - duh\n\n  write(/* data, encoding */) {\n    return true;\n  }\n\n  cork() {}\n\n  uncork() {}\n\n  end(/* chunk, encoding, callback */) {}\n\n  // readable\n  // event readable - some data is now available\n  // event data - switch to flowing mode - feeds chunks to handler\n  // event end - no more data\n  // event close - optional, indicates upstream close\n  // event error - duh\n  read(/* size */) {}\n\n  setEncoding(encoding) {\n    // causes stream.read or stream.on('data) to return strings of encoding instead of <PERSON>uff<PERSON> objects\n    this.encoding = encoding;\n  }\n\n  pause() {}\n\n  resume() {}\n\n  isPaused() {}\n\n  pipe(destination) {\n    // add destination to pipe list & write current buffer\n    this.pipes.push(destination);\n  }\n\n  unpipe(destination) {\n    // remove destination from pipe list\n    this.pipes = this.pipes.filter(pipe => pipe !== destination);\n  }\n\n  unshift(/* chunk */) {\n    // some numpty has read some data that's not for them and they want to put it back!\n    // Might implement this some day\n    throw new Error('Not Implemented');\n  }\n\n  wrap(/* stream */) {\n    // not implemented\n    throw new Error('Not Implemented');\n  }\n}\n\nmodule.exports = StreamBase64;\n"], "mappings": ";;AAAA,MAAMA,MAAM,GAAGC,OAAO,CAAC,iBAAiB,CAAC;;AAEzC;AACA;AACA;AACA,MAAMC,YAAY,SAASF,MAAM,CAACG,MAAM,CAAC;EACvCC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;;IAEP;IACA,IAAI,CAACC,KAAK,GAAG,EAAE;EACjB;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEAC,KAAKA,CAAA,CAAC;EAAA,EAAsB;IAC1B,OAAO,IAAI;EACb;EAEAC,IAAIA,CAAA,EAAG,CAAC;EAERC,MAAMA,CAAA,EAAG,CAAC;EAEVC,GAAGA,CAAA,CAAC,iCAAiC,CAAC;;EAEtC;EACA;EACA;EACA;EACA;EACA;EACAC,IAAIA,CAAA,CAAC,YAAY,CAAC;EAElBC,WAAWA,CAACC,QAAQ,EAAE;IACpB;IACA,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC1B;EAEAC,KAAKA,CAAA,EAAG,CAAC;EAETC,MAAMA,CAAA,EAAG,CAAC;EAEVC,QAAQA,CAAA,EAAG,CAAC;EAEZC,IAAIA,CAACC,WAAW,EAAE;IAChB;IACA,IAAI,CAACZ,KAAK,CAACa,IAAI,CAACD,WAAW,CAAC;EAC9B;EAEAE,MAAMA,CAACF,WAAW,EAAE;IAClB;IACA,IAAI,CAACZ,KAAK,GAAG,IAAI,CAACA,KAAK,CAACe,MAAM,CAACJ,IAAI,IAAIA,IAAI,KAAKC,WAAW,CAAC;EAC9D;EAEAI,OAAOA,CAAA,CAAC;EAAA,EAAa;IACnB;IACA;IACA,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;EACpC;EAEAC,IAAIA,CAAA,CAAC;EAAA,EAAc;IACjB;IACA,MAAM,IAAID,KAAK,CAAC,iBAAiB,CAAC;EACpC;AACF;AAEAE,MAAM,CAACC,OAAO,GAAGvB,YAAY"}