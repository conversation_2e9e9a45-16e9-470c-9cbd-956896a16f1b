{"version": 3, "file": "filter-xform.js", "names": ["BaseXform", "require", "FilterXform", "tag", "render", "xmlStream", "model", "leafNode", "val", "parseOpen", "node", "name", "attributes", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/table/filter-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass FilterXform extends BaseXform {\n  get tag() {\n    return 'filter';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.leafNode(this.tag, {\n      val: model.val,\n    });\n  }\n\n  parseOpen(node) {\n    if (node.name === this.tag) {\n      this.model = {\n        val: node.attributes.val,\n      };\n      return true;\n    }\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = FilterXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,WAAW,SAASF,SAAS,CAAC;EAClC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,QAAQ;EACjB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,EAAE;MAC3BK,GAAG,EAAEF,KAAK,CAACE;IACb,CAAC,CAAC;EACJ;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,IAAI,CAACR,GAAG,EAAE;MAC1B,IAAI,CAACG,KAAK,GAAG;QACXE,GAAG,EAAEE,IAAI,CAACE,UAAU,CAACJ;MACvB,CAAC;MACD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAK,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGd,WAAW"}