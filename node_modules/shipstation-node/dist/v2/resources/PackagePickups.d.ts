import type BaseAPI from '../../BaseAPI';
import { BaseResource } from '../../BaseResource';
import type { DeleteScheduledPickupResponse, ListPackagePickupsOptions, ListPackagePickupsResponse, PackagePickup, SchedulePackagePickupData } from '../types';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/package_pickups)
 *
 * Scheduled pickups and manage pickup requests for supported carriers.
 */
export declare class PackagePickups extends BaseResource {
    constructor(shipstation: BaseAPI);
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_pickups/get_pickup_by_id)
     *
     * Get Pickup By ID
     *
     * @param pickupId Pickup Resource ID (>= 4 characters)
     * @example "pik_3YcKU5zdtJuCqoeNwyqqbW"
     *
     * @returns Package pickup details
     */
    getById(pickupId: string): Promise<PackagePickup>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_pickups/list_scheduled_pickups)
     *
     * List all pickups that have been scheduled for this carrier
     *
     * @param options List package pickups options
     *
     * @returns List of package pickups
     */
    listScheduled(options?: ListPackagePickupsOptions): Promise<ListPackagePickupsResponse>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_pickups/schedule_pickup)
     *
     * Schedule a package pickup with a carrier
     *
     * @param labelIds Label IDs that will be included in the pickup request
     * @example ["se-28529731"]
     *
     * @returns Scheduled package pickup details
     */
    schedule(data: SchedulePackagePickupData): Promise<PackagePickup>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_pickups/delete_scheduled_pickup)
     *
     * Delete a previously-scheduled pickup by ID
     *
     * @param pickupId Pickup Resource ID (>= 4 characters)
     * @example "pik_3YcKU5zdtJuCqoeNwyqqbW"
     *
     * @returns Deleted package pickup ID
     */
    deleteScheduled(pickupId: string): Promise<DeleteScheduledPickupResponse>;
}
