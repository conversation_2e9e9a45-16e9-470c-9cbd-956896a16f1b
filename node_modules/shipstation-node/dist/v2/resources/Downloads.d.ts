import type BaseAPI from '../../BaseAPI';
import { BaseResource } from '../../BaseResource';
import type { DownloadFileOptions, DownloadFileResponse } from '../types';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/downloads)
 *
 * Download your label files in PDF, PNG, and ZPL.
 */
export declare class Downloads extends BaseResource {
    constructor(shipstation: BaseAPI);
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/downloads/download_file)
     *
     * Download labels and other shipment-related documents.
     *
     * @param options Options for the download request
     *
     * @returns Carrier details
     */
    downloadFile(options: DownloadFileOptions): Promise<DownloadFileResponse>;
}
