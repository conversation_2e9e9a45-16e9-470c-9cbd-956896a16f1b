var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource } from '../../BaseResource';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/inventory)
 *
 * Manage inventory, adjust quantities, and handle warehouses and locations.
 */
export class InventoryLocations extends BaseResource {
    constructor(shipstation) {
        super(shipstation, 'inventory_locations');
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/listinventorylocations)
     *
     * @param options Options for the request
     *
     * @returns List of inventory locations
     */
    list(options) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'GET',
                params: options
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/createinventorylocation)
     *
     * @param data Data for creating the inventory location
     *
     * @returns The newly created inventory location
     */
    create(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'POST',
                data
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/getinventorylocationbyid)
     *
     * @param inventoryLocationId ID of the inventory location to get
     *
     * @returns The inventory location with the given ID
     */
    getById(inventoryLocationId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${inventoryLocationId}`,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/updateinventorylocation)
     *
     * @param inventoryLocationId ID of the inventory location to update
     * @param name New name for the inventory location
     */
    update(inventoryLocationId, name) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.shipstation.request({
                url: `${this.baseUrl}/${inventoryLocationId}`,
                method: 'PUT',
                data: { name }
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/deleteinventorylocation)
     *
     * @param inventoryLocationId ID of the inventory location to delete
     * @param options Options for the request
     */
    delete(inventoryLocationId, options) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.shipstation.request({
                url: `${this.baseUrl}/${inventoryLocationId}`,
                method: 'DELETE',
                params: options
            });
        });
    }
}
