var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource } from '../../BaseResource';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/rates)
 *
 * Quickly compare rates using the Rates endpoint. You can see and compare rates for the carriers connected to your
 * account (as long as they support sending rates).
 */
export class Rates extends BaseResource {
    constructor(shipstation) {
        super(shipstation, 'rates');
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/rates/get_rate_by_id)
     *
     * Retrieve a previously queried rate by its ID
     *
     * @param rateId Rate ID [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-********"
     *
     * @returns The rate specified by the ID
     */
    getById(rateId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${rateId}`,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/rates/calculate_rates)
     *
     * It's not uncommon that you want to give your customer the choice between whether they want to ship the fastest,
     * cheapest, or the most trusted route. Most companies don't solely ship things using a single shipping option; so we
     * provide functionality to show you all your options!
     *
     * @param options The options for the request
     *
     * @returns
     */
    get(options) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'POST',
                data: options
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/rates/estimate_rates)
     *
     * Get Rate Estimates
     *
     * @param options The options for the request
     *
     * @returns A list of rate estimates
     */
    estimate(options) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/estimate`,
                method: 'POST',
                data: options
            });
        });
    }
}
