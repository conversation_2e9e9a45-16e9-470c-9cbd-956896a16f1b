var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource } from '../../BaseResource';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/package_pickups)
 *
 * Scheduled pickups and manage pickup requests for supported carriers.
 */
export class PackagePickups extends BaseResource {
    constructor(shipstation) {
        super(shipstation, 'pickups');
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_pickups/get_pickup_by_id)
     *
     * Get Pickup By ID
     *
     * @param pickupId Pickup Resource ID (>= 4 characters)
     * @example "pik_3YcKU5zdtJuCqoeNwyqqbW"
     *
     * @returns Package pickup details
     */
    getById(pickupId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${pickupId}`,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_pickups/list_scheduled_pickups)
     *
     * List all pickups that have been scheduled for this carrier
     *
     * @param options List package pickups options
     *
     * @returns List of package pickups
     */
    listScheduled(options) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'GET',
                params: options
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_pickups/schedule_pickup)
     *
     * Schedule a package pickup with a carrier
     *
     * @param labelIds Label IDs that will be included in the pickup request
     * @example ["se-28529731"]
     *
     * @returns Scheduled package pickup details
     */
    schedule(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'POST',
                data
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_pickups/delete_scheduled_pickup)
     *
     * Delete a previously-scheduled pickup by ID
     *
     * @param pickupId Pickup Resource ID (>= 4 characters)
     * @example "pik_3YcKU5zdtJuCqoeNwyqqbW"
     *
     * @returns Deleted package pickup ID
     */
    deleteScheduled(pickupId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${pickupId}`,
                method: 'DELETE'
            });
        });
    }
}
