import type BaseAPI from '../../BaseAPI';
import { BaseResource } from '../../BaseResource';
import type { CreateInventoryLocationOptions, DeleteInventoryLocationOptions, InventoryLocation, ListInventoryLocationsOptions, ListInventoryLocationsResponse } from '../types';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/inventory)
 *
 * Manage inventory, adjust quantities, and handle warehouses and locations.
 */
export declare class InventoryLocations extends BaseResource {
    constructor(shipstation: BaseAPI);
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/listinventorylocations)
     *
     * @param options Options for the request
     *
     * @returns List of inventory locations
     */
    list(options?: ListInventoryLocationsOptions): Promise<ListInventoryLocationsResponse>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/createinventorylocation)
     *
     * @param data Data for creating the inventory location
     *
     * @returns The newly created inventory location
     */
    create(data: CreateInventoryLocationOptions): Promise<InventoryLocation>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/getinventorylocationbyid)
     *
     * @param inventoryLocationId ID of the inventory location to get
     *
     * @returns The inventory location with the given ID
     */
    getById(inventoryLocationId: string): Promise<InventoryLocation>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/updateinventorylocation)
     *
     * @param inventoryLocationId ID of the inventory location to update
     * @param name New name for the inventory location
     */
    update(inventoryLocationId: string, name: string): Promise<void>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/deleteinventorylocation)
     *
     * @param inventoryLocationId ID of the inventory location to delete
     * @param options Options for the request
     */
    delete(inventoryLocationId: string, options?: DeleteInventoryLocationOptions): Promise<void>;
}
