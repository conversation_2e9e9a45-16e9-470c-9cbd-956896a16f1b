var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource } from '../../BaseResource';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/manifests)
 *
 * A manifest is a document that provides a list of the day's shipments. It typically contains a barcode that allows the
 * pickup driver to scan a single document to register all shipments, rather than scanning each shipment individually.
 */
export class Manifests extends BaseResource {
    constructor(shipstation) {
        super(shipstation, 'manifests');
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/manifests/list_manifests)
     *
     * Similar to querying shipments, we allow you to query manifests since there will likely be a large number over a
     * long period of time.
     *
     * @param params Options for listing the manifests
     *
     * @returns A list of manifests
     */
    list(params) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'GET',
                params
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/manifests/get_manifest_by_id)
     *
     * Get Manifest By Id
     *
     * @param manifestId [1-25] characters `^se(-[a-z0-9]+)+$`
     *
     * @example "se-28529731"
     *
     * @returns A manifest
     */
    getById(manifestId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${manifestId}`,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/manifests/create_manifest)
     *
     * Each ShipStation manifest is created for a specific warehouse, so you'll need to provide the warehouse_id rather
     * than the ship_from address. You can create a warehouse for each location that you want to create manifests for.
     *
     * @param params Data for creating a manifest
     *
     * @returns A manifest
     */
    create(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'POST',
                data
            });
        });
    }
}
