var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource } from '../../BaseResource';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/inventory)
 *
 * Manage inventory, adjust quantities, and handle warehouses and locations.
 */
export class Inventory extends BaseResource {
    constructor(shipstation) {
        super(shipstation, 'inventory');
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/getinventorylevels)
     *
     * @param options Options for the request
     *
     * @returns SKU inventory levels
     */
    listSKUInventoryLevels(options) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'GET',
                params: options
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/updateskustocklevels)
     *
     * @param data Data for the request
     */
    updateSKULevelsAndProperties(options) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.shipstation.request({
                url: this.baseUrl,
                method: 'POST',
                data: options
            });
        });
    }
}
