import type BaseAPI from '../../BaseAPI';
import { BaseResource } from '../../BaseResource';
import type { ListSKUInventoryLevelsOptions, ListSKUInventoryLevelsResponse, UpdateSKULevelsAndPropertiesOptions } from '../types';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/inventory)
 *
 * Manage inventory, adjust quantities, and handle warehouses and locations.
 */
export declare class Inventory extends BaseResource {
    constructor(shipstation: BaseAPI);
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/getinventorylevels)
     *
     * @param options Options for the request
     *
     * @returns SKU inventory levels
     */
    listSKUInventoryLevels(options: ListSKUInventoryLevelsOptions): Promise<ListSKUInventoryLevelsResponse>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/updateskustocklevels)
     *
     * @param data Data for the request
     */
    updateSKULevelsAndProperties(options: UpdateSKULevelsAndPropertiesOptions): Promise<void>;
}
