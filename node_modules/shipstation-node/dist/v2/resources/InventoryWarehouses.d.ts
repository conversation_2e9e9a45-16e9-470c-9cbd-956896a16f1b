import type BaseAPI from '../../BaseAPI';
import { BaseResource } from '../../BaseResource';
import type { InventoryWarehouse, ListInventoryWarehousesOptions, ListInventoryWarehousesResponse } from '../types';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/inventory)
 *
 * Manage inventory, adjust quantities, and handle warehouses and locations.
 */
export declare class InventoryWarehouses extends BaseResource {
    constructor(shipstation: BaseAPI);
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/getinventorywarehouses)
     *
     * @param options Options for the request
     *
     * @returns List of inventory warehouses
     */
    list(options?: ListInventoryWarehousesOptions): Promise<ListInventoryWarehousesResponse>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/addnewinventorywarehouse)
     *
     * @param name Name of the inventory warehouse
     *
     * @returns The newly created inventory warehouse
     */
    create(name: string): Promise<InventoryWarehouse>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/getinventorywarehousebyid)
     *
     * @param inventoryWarehouseId ID of the inventory warehouse to get
     *
     * @returns The inventory warehouse with the given ID
     */
    getById(inventoryWarehouseId: string): Promise<InventoryWarehouse>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/updateinventorywarehouse)
     *
     * @param inventoryWarehouseId ID of the inventory warehouse to update
     * @param name New name for the inventory warehouse
     *
     * @returns The updated inventory warehouse
     */
    update(inventoryWarehouseId: string, name: string): Promise<InventoryWarehouse>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/deleteinventorywarehouse)
     *
     * @param inventoryWarehouseId ID of the inventory warehouse to delete
     */
    delete(inventoryWarehouseId: string): Promise<void>;
}
