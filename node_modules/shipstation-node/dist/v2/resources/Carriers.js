var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource } from '../../BaseResource';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/carriers)
 *
 * Retreive useful details about the carriers connected to your accounts, including carrier IDs, service IDs, advanced
 * options, and available carrier package types.
 */
export class Carriers extends BaseResource {
    constructor(shipstation) {
        super(shipstation, 'carriers');
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/carriers/get_carrier_by_id)
     *
     * Retrive details about a specific carrier by its carrier id.
     *
     * @param carrierId [1-25] characters `^se(-[a-z0-9]+)+$`
     *
     * @returns Carrier details
     */
    getById(carrierId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${carrierId}`,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/carriers/list_carriers)
     *
     * List all carriers that have been added to this account.
     *
     * @returns A list of carriers
     */
    list() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/carriers/get_carrier_options)
     *
     * Get a list of the options available for a specific carriers.
     *
     * @param carrierId [1-25] characters `^se(-[a-z0-9]+)+$`
     *
     * @returns A list of options for the carrier
     */
    getOptions(carrierId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${carrierId}/options`,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/carriers/list_carrier_services)
     *
     * List the services associated with a specific carrier id.
     *
     * @param carrierId [1-25] characters `^se(-[a-z0-9]+)+$`
     *
     * @returns A list of services for the carrier
     */
    listServices(carrierId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${carrierId}/services`,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/carriers/list_carrier_services)
     *
     * List the package types associated with a specific carrier.
     *
     * @param carrierId [1-25] characters `^se(-[a-z0-9]+)+$`
     *
     * @returns A list of package types for the carrier
     */
    listPackageTypes(carrierId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${carrierId}/packages`,
                method: 'GET'
            });
        });
    }
}
