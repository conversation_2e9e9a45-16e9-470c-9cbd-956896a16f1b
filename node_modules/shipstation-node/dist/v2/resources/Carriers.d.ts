import type BaseAPI from '../../BaseAPI';
import { BaseResource } from '../../BaseResource';
import type { GetCarrierByIdResponse, GetCarrierOptionsResponse, ListCarrierPackageTypesResponse, ListCarrierServicesResponse, ListCarriersResponse } from '../types';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/carriers)
 *
 * Retreive useful details about the carriers connected to your accounts, including carrier IDs, service IDs, advanced
 * options, and available carrier package types.
 */
export declare class Carriers extends BaseResource {
    constructor(shipstation: BaseAPI);
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/carriers/get_carrier_by_id)
     *
     * Retrive details about a specific carrier by its carrier id.
     *
     * @param carrierId [1-25] characters `^se(-[a-z0-9]+)+$`
     *
     * @returns Carrier details
     */
    getById(carrierId: string): Promise<GetCarrierByIdResponse>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/carriers/list_carriers)
     *
     * List all carriers that have been added to this account.
     *
     * @returns A list of carriers
     */
    list(): Promise<ListCarriersResponse>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/carriers/get_carrier_options)
     *
     * Get a list of the options available for a specific carriers.
     *
     * @param carrierId [1-25] characters `^se(-[a-z0-9]+)+$`
     *
     * @returns A list of options for the carrier
     */
    getOptions(carrierId: string): Promise<GetCarrierOptionsResponse>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/carriers/list_carrier_services)
     *
     * List the services associated with a specific carrier id.
     *
     * @param carrierId [1-25] characters `^se(-[a-z0-9]+)+$`
     *
     * @returns A list of services for the carrier
     */
    listServices(carrierId: string): Promise<ListCarrierServicesResponse>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/carriers/list_carrier_services)
     *
     * List the package types associated with a specific carrier.
     *
     * @param carrierId [1-25] characters `^se(-[a-z0-9]+)+$`
     *
     * @returns A list of package types for the carrier
     */
    listPackageTypes(carrierId: string): Promise<ListCarrierPackageTypesResponse>;
}
