import type <PERSON>AP<PERSON> from '../../BaseAPI';
import { BaseResource } from '../../BaseResource';
import type { CreateWebhookData, ListWebhooksResponse, UpdateWebhookData, Webhook } from '../types';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/webhooks)
 *
 * Webhooks are a powerful feature that can save you from sending repeated polling requests to check on the state of
 * something. With webhooks, ShipStation will automatically contact your servers when the stage changes. This can
 * include parcel tracking events, notification when a batch operation completes, and more.
 */
export declare class Webhooks extends BaseResource {
    constructor(shipstation: BaseAPI);
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/webhooks/list_webhooks)
     *
     * List all webhooks currently enabled for the account.
     *
     * @returns A list of webhooks
     */
    list(): Promise<ListWebhooksResponse>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/webhooks/create_webhook)
     *
     * Create a new webhook.
     *
     * @param data The data for the new webhook
     *
     * @returns The newly created webhook
     */
    create(data: CreateWebhookData): Promise<Webhook>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/webhooks/get_webhook_by_id)
     *
     * Retrieve individual webhook by an ID
     *
     * @param webhookId Webhook ID [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-********"
     *
     * @returns The webhook specified by the ID
     */
    getById(webhookId: string): Promise<Webhook>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/webhooks/update_webhook)
     *
     * Update the webhook url property
     *
     * @param webhookId Webhook ID [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-********"
     *
     * @param data The data for updating the webhook
     */
    update(webhookId: string, data: UpdateWebhookData): Promise<void>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/webhooks/delete_webhook)
     *
     * Delete a webhook.
     *
     * @param webhookId Webhook ID [1-25] characters `^se(-[a-z0-9]+)+$`
     */
    deleteById(webhookId: string): Promise<void>;
}
