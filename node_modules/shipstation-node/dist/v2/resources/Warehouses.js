var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource } from '../../BaseResource';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/warehouses)
 *
 * Get warehouse details like warehouse ID and related addresses using the warehouses endpoint.
 */
export class Warehouses extends BaseResource {
    constructor(shipstation) {
        super(shipstation, 'warehouses');
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/warehouses/get_warehouse_by_id)
     *
     * Retrieve warehouse data based on the warehouse ID
     *
     * @param warehouseId [1-25] characters `^se(-[a-z0-9]+)+$`
     *
     * @returns Warehouse details
     */
    getById(warehouseId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${warehouseId}`,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/warehouses/list_warehouses)
     *
     * Retrieve a list of warehouses associated with this account.
     *
     * @returns A list of warehouses
     */
    list() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'GET'
            });
        });
    }
}
