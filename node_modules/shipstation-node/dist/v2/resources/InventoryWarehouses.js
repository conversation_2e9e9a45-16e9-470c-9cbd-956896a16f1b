var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource } from '../../BaseResource';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/inventory)
 *
 * Manage inventory, adjust quantities, and handle warehouses and locations.
 */
export class InventoryWarehouses extends BaseResource {
    constructor(shipstation) {
        super(shipstation, 'inventory_warehouses');
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/getinventorywarehouses)
     *
     * @param options Options for the request
     *
     * @returns List of inventory warehouses
     */
    list(options) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'GET',
                params: options
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/addnewinventorywarehouse)
     *
     * @param name Name of the inventory warehouse
     *
     * @returns The newly created inventory warehouse
     */
    create(name) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'POST',
                data: { name }
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/getinventorywarehousebyid)
     *
     * @param inventoryWarehouseId ID of the inventory warehouse to get
     *
     * @returns The inventory warehouse with the given ID
     */
    getById(inventoryWarehouseId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${inventoryWarehouseId}`,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/updateinventorywarehouse)
     *
     * @param inventoryWarehouseId ID of the inventory warehouse to update
     * @param name New name for the inventory warehouse
     *
     * @returns The updated inventory warehouse
     */
    update(inventoryWarehouseId, name) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${inventoryWarehouseId}`,
                method: 'PUT',
                data: { name }
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/inventory/deleteinventorywarehouse)
     *
     * @param inventoryWarehouseId ID of the inventory warehouse to delete
     */
    delete(inventoryWarehouseId) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.shipstation.request({
                url: `${this.baseUrl}/${inventoryWarehouseId}`,
                method: 'DELETE'
            });
        });
    }
}
