import type BaseAPI from '../../BaseAPI';
import { BaseResource } from '../../BaseResource';
import type { CreateOrUpdatePackageTypeOptions, ListPackageTypesResponse, PackageType } from '../types';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/package_types)
 *
 * Create custom package types to use for your shipments, rather than the carriers' default package types.
 */
export declare class PackageTypes extends BaseResource {
    constructor(shipstation: BaseAPI);
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_types/list_package_types)
     *
     * List the custom package types associated with the account
     *
     * @returns Package pickup details
     */
    list(): Promise<ListPackageTypesResponse>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_types/create_package_type)
     *
     * Create a custom package type
     *
     * @param data Data for creating the package type
     *
     * @returns The newly created package type
     */
    create(data: CreateOrUpdatePackageTypeOptions): Promise<PackageType>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_types/get_package_type_by_id)
     *
     * Get Custom Package Type by ID
     *
     * @param packageId ID of the package type to get [1-25] characters `^se(-[a-z0-9]+)+$`
     *
     * @returns The package type with the given ID
     */
    getById(packageId: string): Promise<PackageType>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_types/update_package_type)
     *
     * Update the custom package type object by ID
     *
     * @param packageId ID of the package type to update [1-25] characters `^se(-[a-z0-9]+)+$`
     * @param data Data for updating the package type
     *
     * @returns The updated package type
     */
    update(packageId: string, data: CreateOrUpdatePackageTypeOptions): Promise<PackageType>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_types/delete_package_typ)
     *
     * Delete a custom package using the ID
     *
     * @param packageId ID of the package type to delete [1-25] characters `^se(-[a-z0-9]+)+$`
     */
    delete(packageId: string): Promise<void>;
}
