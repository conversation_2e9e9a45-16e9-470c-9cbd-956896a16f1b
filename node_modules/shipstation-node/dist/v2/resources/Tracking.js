var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource } from '../../BaseResource';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/tracking)
 *
 * Use the tracking endpoint to stop receiving tracking updates (more dedicated tracking endpoint methods coming soon).
 */
export class Tracking extends BaseResource {
    constructor(shipstation) {
        super(shipstation, 'downloads');
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/tracking/stop_tracking)
     *
     * Unsubscribe from tracking updates for a package.
     *
     * @param carrierCode A shipping carrier, such as fedex, dhl_express, stamps_com, etc.
     * @example "stamps_com"
     *
     * @param trackingNumber The tracking number associated with a shipment
     * @example "9405511899223197428490"
     */
    stop(carrierCode, trackingNumber) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.shipstation.request({
                url: `${this.baseUrl}/stop`,
                method: 'POST',
                params: {
                    carrier_code: carrierCode,
                    tracking_number: trackingNumber
                }
            });
        });
    }
}
