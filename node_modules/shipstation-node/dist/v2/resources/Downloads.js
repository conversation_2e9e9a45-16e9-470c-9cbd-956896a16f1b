var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
import { BaseResource } from '../../BaseResource';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/downloads)
 *
 * Download your label files in PDF, PNG, and ZPL.
 */
export class Downloads extends BaseResource {
    constructor(shipstation) {
        super(shipstation, 'downloads');
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/downloads/download_file)
     *
     * Download labels and other shipment-related documents.
     *
     * @param options Options for the download request
     *
     * @returns Carrier details
     */
    downloadFile(options) {
        return __awaiter(this, void 0, void 0, function* () {
            const { dir, subdir, filename } = options, params = __rest(options, ["dir", "subdir", "filename"]);
            return this.shipstation.request({
                url: `${this.baseUrl}/${dir}/${subdir}/${filename}`,
                method: 'GET',
                params
            });
        });
    }
}
