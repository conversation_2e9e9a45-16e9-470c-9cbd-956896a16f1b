import type <PERSON>AP<PERSON> from '../../BaseAPI';
import { BaseResource } from '../../BaseResource';
import type { Batch, CreateBatchOptions, GetBatchErrorsOptions, GetBatchErrorsResponse, ListBatchesOptions, ListBatchesResponse, ProcessBatchLabelsOptions, RemoveFromBatchOptions } from '../types';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/batches)
 *
 * Process labels in bulk and receive a large number of labels and customs forms in bulk responses. Batching is ideal
 * for workflows that need to process hundreds or thousands of labels quickly.
 */
export declare class Batches extends BaseResource {
    constructor(shipstation: BaseAPI);
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/batches/list_batches)
     *
     * List the batches associated with your ShipStation account.
     *
     * @param options Options for the list request
     *
     * @returns A list of batches
     */
    list(options: ListBatchesOptions): Promise<ListBatchesResponse>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/batches/create_batch)
     *
     * Create a batch containing multiple labels.
     *
     * @param options Options for the create request
     *
     * @returns The newly created batch
     */
    create(options: CreateBatchOptions): Promise<Batch>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/batches/get_batch_by_id)
     *
     * Get batch details for a specific batch id.
     *
     * @param batchId The batch ID of the batch to retrieve [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-********"
     *
     * @returns A batch specified by its batch ID
     */
    getById(batchId: string): Promise<Batch>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/batches/get_batch_by_external_id)
     *
     * Retreive a batch using an external batch ID
     *
     * @param externalBatchId The external batch ID of the batch to retrieve
     *
     * @returns A batch specified by its external batch ID
     */
    getByExternalId(externalBatchId: string): Promise<Batch>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/batches/delete_batch)
     *
     * Delete a batch based on its batch id. Sets its status to 'archived'.
     *
     * @param batchId The batch ID of the batch to retrieve [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-********"
     */
    delete(batchId: string): Promise<void>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/batches/update_batch)
     *
     * Update a batch by id setting its status to 'archived'.
     *
     * @param batchId The batch ID of the batch to retrieve [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-********"
     */
    updateToArchived(batchId: string): Promise<void>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/batches/add_to_batch)
     *
     * Add a shipment or rate to a batch.
     *
     * @param batchId The batch ID of the batch to retrieve [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-********"
     *
     * @param data The data to add to the batch
     */
    addToBatch(batchId: string, data: CreateBatchOptions): Promise<void>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/batches/list_batch_errors)
     *
     * Errors in batches must be handled differently from synchronous requests. You must retrieve the status of your batch
     * by getting a batch and getting an overview of the statuses or by listing the batch errors.
     *
     * @param batchId The batch ID of the batch to retrieve [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-********"
     *
     * @param options Options for the list request
     *
     * @returns A list of batch errors
     */
    getErrors(batchId: string, options?: GetBatchErrorsOptions): Promise<GetBatchErrorsResponse>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/batches/process_batch)
     *
     * Create and purchase the labels for the shipments included in the batch.
     *
     * @param batchId The batch ID of the batch to retrieve [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-********"
     *
     * @param options Options for the process request
     */
    processLabels(batchId: string, options: ProcessBatchLabelsOptions): Promise<void>;
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/batches/remove_from_batch)
     *
     * Remove specific shipment ids or rate ids from a batch.
     *
     * @param batchId The batch ID of the batch to retrieve [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-********"
     *
     * @param data The data to remove from the batch
     */
    removeFromBatch(batchId: string, data: RemoveFromBatchOptions): Promise<void>;
}
