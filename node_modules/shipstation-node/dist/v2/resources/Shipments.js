var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource } from '../../BaseResource';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/shipments)
 *
 * Shipments are at the core of most ShipStation capabilities. Shipment objects are required for cretaing labels and
 * manifests, as well as getting rates.
 */
export class Shipments extends BaseResource {
    constructor(shipstation) {
        super(shipstation, 'shipments');
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/shipments/list_shipments)
     *
     * Get list of Shipments
     *
     * @param options Options for the list request
     *
     * @returns A list of shipments
     */
    list(options) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'GET',
                params: options
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/shipments/get_shipment_by_external_id)
     *
     * Query Shipments created using your own custom ID convention using this endpoint
     *
     * @param externalId The external shipment id
     * @example "0bcb569d-1727-4ff9-ab49-b2fec0cee5ae"
     *
     * @returns The shipment specified by the external shipment id
     */
    getByExternalId(externalId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/external_shipment_id/${externalId}`,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/shipments/get_shipment_by_id)
     *
     * Get an individual shipment based on its ID
     *
     * @param shipmentId Shipment ID [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-28529731"
     *
     * @returns The shipment specified by the shipment id
     */
    getById(shipmentId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${shipmentId}`,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/shipments/cancel_shipments)
     *
     * Mark a shipment cancelled, if it is no longer needed or being used by your organized. Any label associated with the
     * shipment needs to be voided first An example use case would be if a batch label creation job is going to run at a
     * set time and only queries `pending` shipments. Marking a shipment as cancelled would remove it from this process
     *
     * @param shipmentId Shipment ID [1-25] characters `^se(-[a-z0-9]+)+$`
     */
    cancel(shipmentId) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.shipstation.request({
                url: `${this.baseUrl}/${shipmentId}/cancel`,
                method: 'PUT'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/shipments/list_shipment_rates)
     *
     * Get Rates for the shipment information associated with the shipment ID
     *
     * @param shipmentId Shipment ID [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-28529731"
     *
     * @param options Options for the request
     * @returns The rates for the shipment
     */
    getRates(shipmentId, options) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${shipmentId}/rates`,
                method: 'GET',
                params: options
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/shipments/tag_shipment)
     *
     * Add a tag to the shipment object
     *
     * @param shipmentId Shipment ID [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-28529731"
     *
     * @param tagName Tags are arbitrary strings that you can use to categorize shipments. For example, you may want to
     * use tags to distinguish between domestic and international shipments, or between insured and uninsured shipments.
     * Or maybe you want to create a tag for each of your customers so you can easily retrieve every shipment for a
     * customer.
     * @example "Fragile"
     *
     * @returns The information about the added tag
     */
    addTag(shipmentId, tagName) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${shipmentId}/tags/${tagName}`,
                method: 'POST'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/shipments/untag_shipment)
     *
     * Remove an existing tag from the Shipment object
     *
     * @param shipmentId Shipment ID [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-28529731"
     *
     * @param tagName Tags are arbitrary strings that you can use to categorize shipments. For example, you may want to
     * use tags to distinguish between domestic and international shipments, or between insured and uninsured shipments.
     * Or maybe you want to create a tag for each of your customers so you can easily retrieve every shipment for a
     * customer.
     * @example "Fragile"
     */
    removeTag(shipmentId, tagName) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.shipstation.request({
                url: `${this.baseUrl}/${shipmentId}/tags/${tagName}`,
                method: 'DELETE'
            });
        });
    }
}
