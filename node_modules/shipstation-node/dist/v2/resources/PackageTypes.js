var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource } from '../../BaseResource';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/package_types)
 *
 * Create custom package types to use for your shipments, rather than the carriers' default package types.
 */
export class PackageTypes extends BaseResource {
    constructor(shipstation) {
        super(shipstation, 'packages');
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_types/list_package_types)
     *
     * List the custom package types associated with the account
     *
     * @returns Package pickup details
     */
    list() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_types/create_package_type)
     *
     * Create a custom package type
     *
     * @param data Data for creating the package type
     *
     * @returns The newly created package type
     */
    create(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'POST',
                data
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_types/get_package_type_by_id)
     *
     * Get Custom Package Type by ID
     *
     * @param packageId ID of the package type to get [1-25] characters `^se(-[a-z0-9]+)+$`
     *
     * @returns The package type with the given ID
     */
    getById(packageId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${packageId}`,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_types/update_package_type)
     *
     * Update the custom package type object by ID
     *
     * @param packageId ID of the package type to update [1-25] characters `^se(-[a-z0-9]+)+$`
     * @param data Data for updating the package type
     *
     * @returns The updated package type
     */
    update(packageId, data) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${packageId}`,
                method: 'PUT',
                data
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/package_types/delete_package_typ)
     *
     * Delete a custom package using the ID
     *
     * @param packageId ID of the package type to delete [1-25] characters `^se(-[a-z0-9]+)+$`
     */
    delete(packageId) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.shipstation.request({
                url: `${this.baseUrl}/${packageId}`,
                method: 'DELETE'
            });
        });
    }
}
