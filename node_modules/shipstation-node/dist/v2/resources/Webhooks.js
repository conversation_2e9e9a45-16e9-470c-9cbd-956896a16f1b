var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource } from '../../BaseResource';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/webhooks)
 *
 * Webhooks are a powerful feature that can save you from sending repeated polling requests to check on the state of
 * something. With webhooks, ShipStation will automatically contact your servers when the stage changes. This can
 * include parcel tracking events, notification when a batch operation completes, and more.
 */
export class Webhooks extends BaseResource {
    constructor(shipstation) {
        super(shipstation, 'environment/webhooks');
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/webhooks/list_webhooks)
     *
     * List all webhooks currently enabled for the account.
     *
     * @returns A list of webhooks
     */
    list() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/webhooks/create_webhook)
     *
     * Create a new webhook.
     *
     * @param data The data for the new webhook
     *
     * @returns The newly created webhook
     */
    create(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'POST',
                data
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/webhooks/get_webhook_by_id)
     *
     * Retrieve individual webhook by an ID
     *
     * @param webhookId Webhook ID [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-28529731"
     *
     * @returns The webhook specified by the ID
     */
    getById(webhookId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${webhookId}`,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/webhooks/update_webhook)
     *
     * Update the webhook url property
     *
     * @param webhookId Webhook ID [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-28529731"
     *
     * @param data The data for updating the webhook
     */
    update(webhookId, data) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.shipstation.request({
                url: `${this.baseUrl}/${webhookId}`,
                method: 'PUT',
                data
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/webhooks/delete_webhook)
     *
     * Delete a webhook.
     *
     * @param webhookId Webhook ID [1-25] characters `^se(-[a-z0-9]+)+$`
     */
    deleteById(webhookId) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.shipstation.request({
                url: `${this.baseUrl}/${webhookId}`,
                method: 'DELETE'
            });
        });
    }
}
