var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource } from '../../BaseResource';
/**
 * [Official Documentation](https://docs.shipstation.com/openapi/labels)
 *
 * Purchase and print shipping labels for any carrier active on your account. The labels endpoint also supports creating
 * return labels, voiding labels, and getting label details like tracking.
 */
export class Labels extends BaseResource {
    constructor(shipstation) {
        super(shipstation, 'labels');
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/labels/list_labels)
     *
     * This method returns a list of labels that you've created. You can optionally filter the results as well as control
     * their sort order and the number of results returned at a time.
     *
     * By default all labels are returned 25 at a time, starting with the most recently created ones. You can combine
     * multiple filter options to narrow-down the results. For example, if you only want your UPS labels for your east coast
     * warehouse you could query by both `warehouse_id` and `carrier_id`.
     *
     * @param options Options for the list request
     *
     * @returns A `labels` array containing a page of results (as determined by the `page_size` query parameter). It also
     * includes other useful information, such as the total number of labels that match the query criteria, the number of
     * pages of results, and the URLs of the first, last, next, and previous pages of results.
     */
    list(options) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'GET',
                params: options
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/labels/get_label_by_id)
     *
     * Retrieve a specific label by its label id.
     *
     * @param labelId The ID of the label to get [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-28529731"
     *
     * @param options Options for the get request
     *
     * @returns The label specified by the label id.
     */
    getById(labelId, options) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${labelId}`,
                method: 'GET',
                params: options
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/labels/get_tracking_log_from_label)
     *
     * Retrieve the label's tracking details.
     *
     * @param labelId The ID of the label to get [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-28529731"
     *
     * @returns The label specified by the tracking number.
     */
    getTrackingInfo(labelId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${labelId}/tracking`,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/labels/void_label)
     *
     * Void a specific label using its label id. For labels that are paid for at time of label creation, this will also
     * request a refund from the carrier.
     *
     * @param labelId The ID of the label to void [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-28529731"
     *
     * @returns The status of the void operation.
     */
    void(labelId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${labelId}/void`,
                method: 'PUT'
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/labels/create_label)
     *
     * Purchase and print a label for shipment.
     *
     * @param data The data for the purchase request
     *
     * @returns The newly purchased label.
     */
    purchase(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'POST',
                data
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/labels/create_label_from_rate)
     *
     * When retrieving rates for shipments using the `/rates` endpoint, the returned information contains a `rate_id`
     * property that can be used to generate a label without having to refill in the shipment information repeatedly.
     *
     * @param rateId Rate ID [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-28529731"
     *
     * @param options Options for the purchase request
     *
     * @returns The newly purchased label.
     */
    purchaseWithRateId(rateId, options) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/rates/${rateId}`,
                method: 'POST',
                data: options
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/labels/create_label_from_shipment)
     *
     * Purchase a label using a shipment ID that has already been created with the desired address and package info.
     *
     * @param shipmentId Shipment ID [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-28529731"
     *
     * @param options Options for the purchase request
     *
     * @returns The newly purchased label.
     */
    purchaseWithShipmentId(shipmentId, options) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/shipments/${shipmentId}`,
                method: 'POST',
                data: options
            });
        });
    }
    /**
     * [Official Documentation](https://docs.shipstation.com/openapi/labels/create_return_label)
     *
     * Create a return label for a previously created outbound label. The return label will automatically swap the ship to
     * and ship from addresses from the original label.
     *
     * @param labelId Label ID [1-25] characters `^se(-[a-z0-9]+)+$`
     * @example "se-28529731"
     *
     * @param options Options for the purchase request
     *
     * @returns The newly purchased label.
     */
    createReturnLabel(labelId, options) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${labelId}/return`,
                method: 'POST',
                data: options
            });
        });
    }
}
