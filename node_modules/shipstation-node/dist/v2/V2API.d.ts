import type { ShipStationOptions } from '../BaseAPI';
import BaseAPI from '../BaseAPI';
import { Batches } from './resources/Batches';
import { Carriers } from './resources/Carriers';
import { Downloads } from './resources/Downloads';
import { Inventory } from './resources/Inventory';
import { InventoryLocations } from './resources/InventoryLocations';
import { InventoryWarehouses } from './resources/InventoryWarehouses';
import { Labels } from './resources/Labels';
import { Manifests } from './resources/Manifests';
import { PackagePickups } from './resources/PackagePickups';
import { PackageTypes } from './resources/PackageTypes';
import { Rates } from './resources/Rates';
import { Shipments } from './resources/Shipments';
import { Tags } from './resources/Tags';
import { Tracking } from './resources/Tracking';
import { Warehouses } from './resources/Warehouses';
import { Webhooks } from './resources/Webhooks';
export declare class V2API extends BaseAPI {
    isMock: boolean;
    batches: Batches;
    carriers: Carriers;
    downloads: Downloads;
    inventory: Inventory;
    inventoryLocations: InventoryLocations;
    inventoryWarehouses: InventoryWarehouses;
    labels: Labels;
    manifests: Manifests;
    packagePickups: PackagePickups;
    packageTypes: PackageTypes;
    rates: Rates;
    shipments: Shipments;
    tags: Tags;
    tracking: Tracking;
    warehouses: Warehouses;
    webhooks: Webhooks;
    constructor(options: ShipStationOptions);
}
