var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource } from '../../BaseResource';
export class Customers extends BaseResource {
    constructor(shipstation) {
        super(shipstation, 'customers');
    }
    /**
     * [Official Documentation](https://www.shipstation.com/docs/api/customers/get-customer/)
     *
     * To find a specific customerId, make an API Call to
     * [list customers](https://www.shipstation.com/docs/api/customers/list/) associated with your account.
     *
     * @param customerId The system-generated identifier for the Customer.
     *
     * @returns The details of the customer.
     */
    get(customerId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${customerId}`,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://www.shipstation.com/docs/api/customers/list/)
     *
     * Obtains a list of customers that match the specified criteria.
     *
     * @returns A list of tags for the account.
     */
    list(params) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'GET',
                params
            });
        });
    }
}
