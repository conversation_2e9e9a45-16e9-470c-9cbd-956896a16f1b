var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource } from '../../BaseResource';
export class Warehouses extends BaseResource {
    constructor(shipstation) {
        super(shipstation, 'warehouses');
    }
    /**
     * [Official Documentation](https://www.shipstation.com/docs/api/warehouses/get/)
     *
     * Returns a list of active Ship From Locations (formerly known as warehouses) on the ShipStation account.
     *
     * **NOTE:** In the API, the endpoint is called warehouse, but the process actually affects Ship From locations in the
     * UI on the application side of operations.
     *
     * @param warehouseId A unique ID generated by ShipStation and assigned to each Ship From Location
     * (formerly known as warehouse).
     *
     * @returns The warehouse details.
     */
    get(warehouseId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${warehouseId}`,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://www.shipstation.com/docs/api/warehouses/list/)
     *
     * Retrieves a list of your Ship From Locations (formerly known as warehouses).
     *
     * **NOTE:** In the API, the endpoint is called warehouse, but the process actually affects Ship From locations in the
     * UI on the application side of operations.
     *
     * @returns A list of warehouses.
     */
    list() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: this.baseUrl,
                method: 'GET'
            });
        });
    }
    /**
     * [Official Documentation](https://www.shipstation.com/docs/api/warehouses/create/)
     *
     * Adds a Ship From Location (formerly known as warehouse) to your account.
     *
     * **NOTE:** In the API, the endpoint is called warehouse, but the process actually affects Ship From locations on the
     * application side of operations.
     *
     * @param data The data for the request.
     *
     * @returns The created warehouse.
     */
    create(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/createwarehouse`,
                method: 'POST',
                data
            });
        });
    }
    /**
     * [Official Documentation](https://www.shipstation.com/docs/api/warehouses/update/)
     *
     * Updates an existing Ship From Location (formerly known as a warehouse). This call does not currently support
     * partial updates.
     *
     * The entire resource must be provided in the body of the request. If a `returnAddress` object is not specified, your
     * `originAddress` will be used as your `returnAddress`.
     *
     * **NOTE:** In the API, the endpoint is called `warehouse`, but the process actually affects Ship From locations in
     * the UI on the application side of operations.
     *
     * @param warehouseId A unique ID generated by ShipStation and assigned to each Ship From Location
     * (formerly known as warehouse).
     *
     * @returns The updated warehouse.
     */
    update(warehouseId, data) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${warehouseId}`,
                method: 'PUT',
                data
            });
        });
    }
    /**
     * [Official Documentation](https://www.shipstation.com/docs/api/warehouses/delete/)
     *
     * Removes a warehouse from ShipStation's UI and sets it to Inactive status.
     *
     * **NOTE:**
     *
     * In the API, the endpoint is called warehouse, but the process actually affects Ship From locations in the UI on the
     * application side of operations.
     *
     * This is a "soft" delete action, so the warehouse (or Ship From location) will still exist in the database, but this
     * action will set it to Inactive status.
     *
     * @param warehouseId A unique ID generated by ShipStation and assigned to each Ship From Location
     * (formerly known as warehouse).
     *
     * @returns Status of the delete operation.
     */
    delete(warehouseId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.shipstation.request({
                url: `${this.baseUrl}/${warehouseId}`,
                method: 'DELETE'
            });
        });
    }
}
