import type { Address } from './Address';
import type { AdvancedOptions } from './AdvancedOptions';
import type { Dimensions } from './Dimensions';
import type { InsuranceOptions } from './InsuranceOptions';
import type { InternationalOptions } from './InternationalOptions';
import type { OrderItem } from './OrderItem';
import type { Weight } from './Weight';
export type OrderStatus = 'awaiting_payment' | 'awaiting_shipment' | 'shipped' | 'on_hold' | 'cancelled' | 'pending_fulfillment';
export interface Order {
    /**
     * The system-generated identifier for the order.
     * @readonly
     */
    orderId: number;
    /** A user-defined order number used to identify an order. */
    orderNumber: string;
    /**
     * A user-provided key that should be unique to each order. Can only be initialized during order creation. If the
     * `orderKey` isn't specified during creation, a unique value will be generated and assigned to the order.
     */
    orderKey: string;
    /** The date the order was placed. */
    orderDate: string;
    /**
     * The timestamp the order was created in ShipStation's database.
     * @readonly
     */
    createDate: string;
    /**
     * The timestamp the order was modified in ShipStation. `modifyDate` will equal `createDate` until a modification is
     * made.
     * @readonly
     */
    modifyDate: string;
    /** The date the order was paid for. */
    paymentDate: string;
    /**
     * The date the order is to be shipped before or on.
     *
     * This field is a suggested value generated by the order source/platform/cart and passed to ShipStation. Not all
     * ShipStation integrations support this field and will show `null` if absent.
     */
    shipByDate: string;
    /** The order's status. */
    orderStatus: OrderStatus;
    /**
     * Unique identifier for the customer.
     *
     * Generated by ShipStation the first time the customer record is created.
     * @readonly
     */
    customerId: number;
    /**
     * Identifier for the customer in the originating system.
     *
     * This is typically a username or email address. This value is required to generate a customer profile.
     */
    customerUsername: string;
    /** The customer's email address. */
    customerEmail: string;
    /**
     * The recipient's billing address. The `residential` boolean specifies whether or not the given address is
     * residential.
     */
    billTo: Address;
    /**
     * The recipient's shipping address. The `residential` boolean specifies whether or not the given address is
     * residential.
     */
    shipTo: Address;
    /** Array of purchased items. */
    items: Array<OrderItem>;
    /**
     * The order total.
     * @readonly
     */
    orderTotal: number;
    /** The total amount paid for the Order. */
    amountPaid: number;
    /** The total tax amount for the Order. */
    taxAmount: number;
    /** Shipping amount paid by the customer, if any. */
    shippingAmount: number;
    /** Notes left by the customer when placing the order. */
    customerNotes: string;
    /** Private notes that are only visible to the seller. */
    internalNotes: string;
    /** Specifies whether or not this order is a gift. */
    gift: boolean;
    /** Gift message left by the customer when placing the order. */
    giftMessage: string;
    /** Method of payment used by the customer. */
    paymentMethod: string;
    /**
     * Identifies the shipping service selected by the customer when placing this order.
     *
     * This value is given to ShipStation by the marketplace/cart. If the value is `null` then the marketplace or cart
     * does not support this field in ShipStation.
     */
    requestedShippingService: string;
    /** The code for the carrier that is to be used (or was used) when this order is shipped (was shipped). */
    carrierCode: string;
    /** The code for the shipping service that is to be used (or was used) when this order is shipped (was shipped). */
    serviceCode: string;
    /** The code for the package type that is to be used (or was used) when this order is shipped (was shipped). */
    packageCode: string;
    /** The type of delivery confirmation that is to be used (or was used) when this order is shipped (was shipped). */
    confirmation: string;
    /** The date the order was shipped. */
    shipDate: string;
    /**
     * If placed on hold, this date is the expiration date for this order's hold status. The order is moved back to
     * `awaiting_shipment` status on this date.
     */
    holdUntilDate: string | null;
    /** [Weight](https://www.shipstation.com/docs/api/models/weight/) of the order. */
    weight: Weight;
    /** [Dimensions](https://www.shipstation.com/docs/api/models/dimensions/) of the order. */
    dimensions: Dimensions;
    /**
     * The shipping insurance information associated with this order.
     *
     * Learn more about [InsuranceOptions](https://www.shipstation.com/docs/api/models/insurance-options/)
     */
    insuranceOptions: InsuranceOptions;
    /**
     * Customs information that can be used to generate customs documents for international orders.
     *
     * Learn more about [InternationalOptions](https://www.shipstation.com/docs/api/models/international-options/)
     */
    internationalOptions: InternationalOptions;
    /**
     * Various [AdvancedOptions](https://www.shipstation.com/docs/api/models/advanced-options/) may be available depending
     * on the shipping carrier that is used to ship the order.
     */
    advancedOptions: AdvancedOptions;
    /**
     * The numbers in the array are the `tagIds`.
     *
     * Each `tagId` identifies a tag that has been associated with this order. See also
     * [Add Tag to Order](https://www.shipstation.com/docs/api/orders/add-tag/),
     * [List Orders by Tag](https://www.shipstation.com/docs/api/orders/list-by-tag/),
     * [Remove Tag from Order](https://www.shipstation.com/docs/api/orders/remove-tag/),
     * [Product Tag](https://www.shipstation.com/docs/api/models/product-tag/), and
     * [List Products](https://www.shipstation.com/docs/api/products/list/).
     */
    tagIds?: Array<number> | null;
    /**
     * User assigned to Order/Shipment in the GUID.
     * @readonly
     */
    userId?: string | null;
    /**
     * States whether or not the order is currently marked as being externally fulfilled by the marketplace.
     *
     * A common example is when an Amazon order is marked an Amazon Fulfilled Network (AFN). If the order is an AFN then
     * this element will be `true`.
     * @readonly
     */
    externallyFulfilled?: boolean;
    /**
     * If `externallFulfilled` is `true`, then this string will return how the order is being fulfilled by the
     * marketplace.
     * @readonly
     */
    externallyFulfilledBy?: string | null;
}
