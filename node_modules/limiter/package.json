{"name": "limiter", "description": "A generic rate limiter for the web and node.js. Useful for API clients, web crawling, or other tasks that need to be throttled", "version": "3.0.0", "author": "<PERSON> <<EMAIL>>", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "browser": "./dist/esm/index.js", "exports": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "repository": "git://github.com/jhurliman/node-rate-limiter", "bugs": {"url": "http://github.com/jhurliman/node-rate-limiter/issues"}, "license": "MIT", "files": ["dist", "src"], "jest": {"moduleNameMapper": {"^(.*)\\.js$": "$1"}}, "scripts": {"lint": "eslint --fix src", "prepack": "yarn tsc -p tsconfig.json && tsc -p tsconfig.cjs.json && node ./create-package-json.js", "test": "jest src"}, "devDependencies": {"@babel/core": "^7.24.4", "@babel/preset-env": "^7.24.4", "@babel/preset-typescript": "^7.24.1", "@types/babel__generator": "^7.6.8", "@types/jest": "^29.5.12", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "babel-jest": "^29.7.0", "eslint": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "prettier": "^3.2.5", "typescript": "^5.4.5"}, "keywords": ["rate", "limiting", "throttling"], "packageManager": "yarn@1.22.19+sha512.ff4579ab459bb25aa7c0ff75b62acebe576f6084b36aa842971cf250a5d8c6cd3bc9420b22ce63c7f93a0857bc6ef29291db39c3e7a23aab5adfd5a4dd6c5d71"}