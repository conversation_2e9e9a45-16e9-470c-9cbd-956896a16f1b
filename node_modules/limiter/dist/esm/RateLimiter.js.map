{"version": 3, "file": "RateLimiter.js", "sourceRoot": "", "sources": ["../../src/RateLimiter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAY,WAAW,EAAE,MAAM,kBAAkB,CAAC;AACzD,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAQnD;;;;;;;;;;;GAWG;AACH,MAAM,OAAO,WAAW;IACtB,WAAW,CAAc;IACzB,gBAAgB,CAAS;IACzB,kBAAkB,CAAS;IAC3B,eAAe,CAAU;IAEzB,YAAY,EAAE,iBAAiB,EAAE,QAAQ,EAAE,eAAe,EAAmB;QAC3E,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC;YACjC,UAAU,EAAE,iBAAiB;YAC7B,iBAAiB;YACjB,QAAQ;SACT,CAAC,CAAC;QAEH,iCAAiC;QACjC,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,iBAAiB,CAAC;QAE7C,IAAI,CAAC,gBAAgB,GAAG,eAAe,EAAE,CAAC;QAC1C,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,eAAe,IAAI,KAAK,CAAC;IAClD,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,YAAY,CAAC,KAAa;QAC9B,0DAA0D;QAC1D,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CACb,oBAAoB,KAAK,wCAAwC,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAC/F,CAAC;QACJ,CAAC;QAED,MAAM,GAAG,GAAG,eAAe,EAAE,CAAC;QAE9B,0EAA0E;QAC1E,YAAY;QACZ,IAAI,GAAG,GAAG,IAAI,CAAC,gBAAgB,IAAI,GAAG,GAAG,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;YAC5F,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC;YAC5B,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC9B,CAAC;QAED,uEAAuE;QACvE,gBAAgB;QAChB,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACzE,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACzB,OAAO,CAAC,CAAC,CAAC;YACZ,CAAC;iBAAM,CAAC;gBACN,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,GAAG,CAAC,CAAC;gBAClF,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC;gBACnB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACnE,IAAI,CAAC,kBAAkB,IAAI,KAAK,CAAC;gBACjC,OAAO,eAAe,CAAC;YACzB,CAAC;QACH,CAAC;QAED,8DAA8D;QAC9D,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACnE,IAAI,CAAC,kBAAkB,IAAI,KAAK,CAAC;QACjC,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;;;;;;;OAQG;IACH,eAAe,CAAC,KAAa;QAC3B,0DAA0D;QAC1D,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU;YAAE,OAAO,KAAK,CAAC;QAEtD,MAAM,GAAG,GAAG,eAAe,EAAE,CAAC;QAE9B,0EAA0E;QAC1E,YAAY;QACZ,IAAI,GAAG,GAAG,IAAI,CAAC,gBAAgB,IAAI,GAAG,GAAG,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;YAC5F,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC;YAC5B,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC9B,CAAC;QAED,qEAAqE;QACrE,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC,kBAAkB;YAAE,OAAO,KAAK,CAAC;QAEvF,qEAAqE;QACrE,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QACxD,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,kBAAkB,IAAI,KAAK,CAAC;QACnC,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;OAGG;IACH,kBAAkB;QAChB,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;IAClC,CAAC;CACF"}