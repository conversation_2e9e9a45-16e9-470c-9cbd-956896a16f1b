{"version": 3, "file": "TokenBucket.test.js", "sourceRoot": "", "sources": ["../../src/TokenBucket.test.ts"], "names": [], "mappings": ";;AAAA,qDAA+C;AAC/C,yCAAkC;AAElC,MAAM,cAAc,GAAG,EAAE,CAAC;AAE1B,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;IAC3B,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;YAC9B,MAAM,MAAM,GAAG,IAAI,4BAAW,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,iBAAiB,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC;YACxF,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,KAAK,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;YAC1B,MAAM,MAAM,GAAG,IAAI,4BAAW,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,iBAAiB,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC;YACxF,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAEtD,MAAM,QAAQ,GAAG,CAAC,IAAI,IAAI,EAAE,GAAG,KAAK,CAAC;YACrC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAC,CAAC;YACvC,MAAM,CAAC,IAAI,GAAG,cAAc,CAAC,CAAC;YAC9B,MAAM,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,MAAM,GAAG,IAAI,4BAAW,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,iBAAiB,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC;YACxF,MAAM,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAE9B,MAAM,KAAK,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;YAC1B,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACtD,MAAM,QAAQ,GAAG,CAAC,IAAI,IAAI,EAAE,GAAG,KAAK,CAAC;YACrC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAC,CAAC;YACvC,MAAM,CAAC,IAAI,GAAG,cAAc,CAAC,CAAC;YAC9B,MAAM,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,MAAM,GAAG,IAAI,4BAAW,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,iBAAiB,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC;YACxF,MAAM,IAAA,eAAI,EAAC,IAAI,CAAC,CAAC;YACjB,MAAM,KAAK,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;YAC1B,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACtD,MAAM,QAAQ,GAAG,CAAC,IAAI,IAAI,EAAE,GAAG,KAAK,CAAC;YACrC,MAAM,CAAC,QAAQ,GAAG,cAAc,CAAC,CAAC;YAClC,MAAM,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,MAAM,MAAM,GAAG,IAAI,4BAAW,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,iBAAiB,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC;YAExF,MAAM,KAAK,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;YAC1B,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,QAAQ,GAAG,CAAC,IAAI,IAAI,EAAE,GAAG,KAAK,CAAC;YACrC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC;YACtC,MAAM,CAAC,IAAI,GAAG,cAAc,CAAC,CAAC;YAC9B,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}