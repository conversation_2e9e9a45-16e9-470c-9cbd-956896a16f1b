{"version": 3, "file": "TokenBucket.js", "sourceRoot": "", "sources": ["../../src/TokenBucket.ts"], "names": [], "mappings": ";;;AAAA,yCAAmD;AAWnD;;;;;;;;;;;;;GAaG;AACH,MAAa,WAAW;IACtB,UAAU,CAAS;IACnB,iBAAiB,CAAS;IAC1B,QAAQ,CAAS;IACjB,YAAY,CAAe;IAC3B,OAAO,CAAS;IAChB,QAAQ,CAAS;IAEjB,YAAY,EAAE,UAAU,EAAE,iBAAiB,EAAE,QAAQ,EAAE,YAAY,EAAmB;QACpF,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAE3C,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,QAAQ,QAAQ,EAAE,CAAC;gBACjB,KAAK,KAAK,CAAC;gBACX,KAAK,QAAQ;oBACX,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;oBACrB,MAAM;gBACR,KAAK,KAAK,CAAC;gBACX,KAAK,QAAQ;oBACX,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;oBAC1B,MAAM;gBACR,KAAK,IAAI,CAAC;gBACV,KAAK,MAAM;oBACT,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;oBAC/B,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;oBACpC,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,mBAAmB,GAAG,QAAQ,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,IAAA,0BAAe,GAAE,CAAC;IACpC,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,YAAY,CAAC,KAAa;QAC9B,mCAAmC;QACnC,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,MAAM,CAAC,iBAAiB,CAAC;QAClC,CAAC;QAED,+DAA+D;QAC/D,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,oBAAoB,KAAK,wBAAwB,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QACtF,CAAC;QAED,mCAAmC;QACnC,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,MAAM,aAAa,GAAG,KAAK,IAAI,EAAE;YAC/B,mEAAmE;YACnE,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAC5F,MAAM,IAAA,eAAI,EAAC,MAAM,CAAC,CAAC;YACnB,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC,CAAC;QAEF,iEAAiE;QACjE,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO;YAAE,OAAO,aAAa,EAAE,CAAC;QAEjD,IAAI,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,CAAC;YACnC,oDAAoD;YACpD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAEpE,wDAAwD;YACxD,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO;gBAAE,OAAO,aAAa,EAAE,CAAC;YAEjD,mEAAmE;YACnE,kEAAkE;YAClE,qEAAqE;YACrE,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC;YAEtB,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC;aAAM,CAAC;YACN,+CAA+C;YAC/C,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC;YACtB,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACH,eAAe,CAAC,KAAa;QAC3B,mCAAmC;QACnC,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO,IAAI,CAAC;QAElC,+DAA+D;QAC/D,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU;YAAE,OAAO,KAAK,CAAC;QAE1C,mCAAmC;QACnC,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,8DAA8D;QAC9D,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO;YAAE,OAAO,KAAK,CAAC;QAEvC,4DAA4D;QAC5D,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QAEjF,0DAA0D;QAC1D,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,IAAI;QACF,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE,CAAC;YACjC,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC;YACjC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC;YAC/B,OAAO,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC;QACpC,CAAC;QAED,MAAM,GAAG,GAAG,IAAA,0BAAe,GAAE,CAAC;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;QAEpB,MAAM,UAAU,GAAG,OAAO,GAAG,CAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtE,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,GAAG,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IAC5D,CAAC;CACF;AA7ID,kCA6IC"}