// Auto-generated by <PERSON>, do not modify.


import { account, async, auth, check, common, contacts, file_properties, file_requests, files, openid, paper, secondary_emails, seen_state, sharing, team, team_common, team_log, team_policies, users, users_common } from './dropbox_types';
export * from './dropbox_types';

export interface DropboxAuthOptions {
  // An access token for making authenticated requests.
  accessToken?: string;
  // The time at which the access token expires.
  accessTokenExpiresAt?: Date;
  // A refresh token for retrieving access tokens
  refreshToken?: string;
  // The client id for your app. Used to create authentication URL.
  clientId?: string;
  // The client secret for your app. Used for refresh and token exchange.
  clientSecret?: string;
  // The fetch library for making requests.
  fetch?: Function;
  // A custom domain to use when making api requests. This should only be used for testing as scaffolding to avoid making network requests.
  domain?: string;
  // A custom delimiter to use when separating domain from subdomain. This should only be used for testing as scaffolding.
  domainDelimiter?: string;
  // An object (in the form of header: value) designed to set custom headers to use during a request.
  customHeaders?: object;
  // Whether request data is sent on body or as URL params. Defaults to false.
  dataOnBody?: boolean;
}

export class DropboxAuth {
  /**
   * The DropboxAuth class that provides methods to manage, acquire, and refresh tokens.
   */
  constructor();

  /**
   * The DropboxAuth class that provides methods to manage, acquire, and refresh tokens.
   */
  constructor(options: DropboxAuthOptions);

  /**
   * Get the access token
   * @returns {String} Access token
   */
  getAccessToken(): string;

  /**
   * Get an OAuth2 access token from an OAuth2 Code.
   * @param redirectUri A URL to redirect the user to after authenticating.
   *   This must be added to your app through the admin interface.
   * @param code An OAuth2 code.
   * @returns {Object} An object containing the token and related info (if applicable)
   */
  getAccessTokenFromCode(redirectUri: string, code: string): Promise<DropboxResponse<object>>;

  /**
   * Get a URL that can be used to authenticate users for the Dropbox API.
   * @arg {String} redirectUri - A URL to redirect the user to after
   * authenticating. This must be added to your app through the admin interface.
   * @arg {String} [state] - State that will be returned in the redirect URL to help
   * prevent cross site scripting attacks.
   * @arg {String} [authType] - auth type, defaults to 'token', other option is 'code'
   * @arg {String} [tokenAccessType] - type of token to request.  From the following:
   * null - creates a token with the app default (either legacy or online)
   * legacy - creates one long-lived token with no expiration
   * online - create one short-lived token with an expiration
   * offline - create one short-lived token with an expiration with a refresh token
   * @arg {Array<String>} [scope] - scopes to request for the grant
   * @arg {String} [includeGrantedScopes] - whether or not to include previously granted scopes.
   * From the following:
   * user - include user scopes in the grant
   * team - include team scopes in the grant
   * Note: if this user has never linked the app, include_granted_scopes must be None
   * @arg {boolean} [usePKCE] - Whether or not to use Sha256 based PKCE. PKCE should be only use on
   * client apps which doesn't call your server. It is less secure than non-PKCE flow but
   * can be used if you are unable to safely retrieve your app secret
   * @returns {Promise<String>} - Url to send user to for Dropbox API authentication
   * returned in a promise
   */
  getAuthenticationUrl(redirectUri: string, state?: string, authType?: 'token' | 'code', tokenAccessType?: null | 'legacy' | 'offline' | 'online', scope?: Array<String>, includeGrantedScopes?: 'none' | 'user' | 'team', usePKCE?: boolean): Promise<String>;

  /**
   * Get the client id
   * @returns {String} Client id
   */
  getClientId(): string;

  /**
   * Set the access token used to authenticate requests to the API.
   * @param accessToken An access token.
   */
  setAccessToken(accessToken: string): void;

  /**
   * Set the client id, which is used to help gain an access token.
   * @param clientId Your app's client ID.
   */
  setClientId(clientId: string): void;

  /**
   * Set the client secret
   * @param clientSecret Your app's client secret.
   */
  setClientSecret(clientSecret: string): void;

  /**
   * Sets the refresh token
   * @param refreshToken - A refresh token
   */
  setRefreshToken(refreshToken: string): void;

  /**
   * Gets the refresh token
   * @returns {String} Refresh token
   */
  getRefreshToken(): string;

  /**
   * Sets the access token's expiration date
   * @param accessTokenExpiresAt - new expiration date
   */
  setAccessTokenExpiresAt(accessTokenExpiresAt: Date): void;

  /**
   * Gets the access token's expiration date
   * @returns {Date} date of token expiration
   */
  getAccessTokenExpiresAt(): Date;

  /**
    * Sets the code verifier for PKCE flow
    * @param {String} codeVerifier - new code verifier 
    */
  setCodeVerifier(codeVerifier: string): void;

  /**
    * Gets the code verifier for PKCE flow
    * @returns {String} - code verifier for PKCE
    */
  getCodeVerifier(): string;

  /**
   * Checks if a token is needed, can be refreshed and if the token is expired.
   * If so, attempts to refresh access token
   * @returns {Promise<*>}
   */
  checkAndRefreshAccessToken(): void;

  /**
   * Refreshes the access token using the refresh token, if available
   * @arg {List} scope - a subset of scopes from the original
   * refresh to acquire with an access token
   * @returns {Promise<*>}
   */
  refreshAccessToken(scope?: Array<String>): void;

}

export interface DropboxOptions {
  // Select user is only used for team functionality. It specifies which user the team access token should be acting as.
  selectUser?: string;
  // Select admin is only used by team functionality. It specifies which team admin the team access token should be acting as.
  selectAdmin?: string;
  // Root path to access other namespaces. Use to access team folders for example
  pathRoot?: string;
  // The DropboxAuth object used to authenticate requests. If this is set, the remaining parameters will be ignored.
  auth?: DropboxAuth | null;
  // An access token for making authenticated requests.
  accessToken?: string;
  // The time at which the access token expires.
  accessTokenExpiresAt?: Date;
  // A refresh token for retrieving access tokens
  refreshToken?: string;
  // The client id for your app. Used to create authentication URL.
  clientId?: string;
  // The client secret for your app. Used for refresh and token exchange.
  clientSecret?: string;
  // The fetch library for making requests.
  fetch?: Function;
  // A custom domain to use when making api requests. This should only be used for testing as scaffolding to avoid making network requests.
  domain?: string;
  // A custom delimiter to use when separating domain subdomain. This should only be used for testing as scaffolding.
  domainDelimiter?: string;
  // An object (in the form of header: value) designed to set custom headers to use during a request.
  customHeaders?: object;
}

export class DropboxResponseError<T> {
  /**
   * The response class of HTTP errors from API calls using the Dropbox SDK.
   */
  constructor(status: number, headers: any, error: T);

  /**
   * HTTP Status code of the call
   */
  status: number;

  /**
   * Headers returned from the call. Set as any to support both node and browser.
   */
  headers: any;

  /**
   * Serialized Error of the call
   */
  error: T;
}

export class DropboxResponse<T> {
  /**
   * The response class of all successful API calls using the Dropbox SDK.
   */
  constructor(status: number, headers: any, result: T);

  /**
   * HTTP Status code of the call
   */
  status: number;

  /**
   * Headers returned from the call. Set as any to support both node and browser.
   */
  headers: any;

  /**
   * Serialized Result of the call
   */
  result: T;
}

export class Dropbox {
  /**
   * The Dropbox SDK class that provides methods to read, write and
   * create files or folders in a user or team's Dropbox.
   */
  constructor();

  /**
   * The Dropbox SDK class that provides methods to read, write and
   * create files or folders in a user or team's Dropbox.
   */
  constructor(options: DropboxOptions);
/*ROUTES*/

    /**
     * Sets a user's profile photo.
     *
     * Route attributes:
     *   scope: account_info.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<account.SetProfilePhotoError>.
     * @param arg The request parameters.
     */
    public accountSetProfilePhoto(arg: account.SetProfilePhotoArg): Promise<DropboxResponse<account.SetProfilePhotoResult>>;

    /**
     * Creates an OAuth 2.0 access token from the supplied OAuth 1.0 access
     * token.
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<auth.TokenFromOAuth1Error>.
     * @param arg The request parameters.
     */
    public authTokenFromOauth1(arg: auth.TokenFromOAuth1Arg): Promise<DropboxResponse<auth.TokenFromOAuth1Result>>;

    /**
     * Disables the access token used to authenticate the call. If there is a
     * corresponding refresh token for the access token, this disables that
     * refresh token, as well as any other access tokens for that refresh token.
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     */
    public authTokenRevoke(): Promise<DropboxResponse<void>>;

    /**
     * This endpoint performs App Authentication, validating the supplied app
     * key and secret, and returns the supplied string, to allow you to test
     * your code and connection to the Dropbox API. It has no other effect. If
     * you receive an HTTP 200 response with the supplied query, it indicates at
     * least part of the Dropbox API infrastructure is working and that the app
     * key and secret valid.
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @param arg The request parameters.
     */
    public checkApp(arg: check.EchoArg): Promise<DropboxResponse<check.EchoResult>>;

    /**
     * This endpoint performs User Authentication, validating the supplied
     * access token, and returns the supplied string, to allow you to test your
     * code and connection to the Dropbox API. It has no other effect. If you
     * receive an HTTP 200 response with the supplied query, it indicates at
     * least part of the Dropbox API infrastructure is working and that the
     * access token is valid.
     *
     * Route attributes:
     *   scope: account_info.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @param arg The request parameters.
     */
    public checkUser(arg: check.EchoArg): Promise<DropboxResponse<check.EchoResult>>;

    /**
     * Removes all manually added contacts. You'll still keep contacts who are
     * on your team or who you imported. New contacts will be added when you
     * share.
     *
     * Route attributes:
     *   scope: contacts.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     */
    public contactsDeleteManualContacts(): Promise<DropboxResponse<void>>;

    /**
     * Removes manually added contacts from the given list.
     *
     * Route attributes:
     *   scope: contacts.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<contacts.DeleteManualContactsError>.
     * @param arg The request parameters.
     */
    public contactsDeleteManualContactsBatch(arg: contacts.DeleteManualContactsArg): Promise<DropboxResponse<void>>;

    /**
     * Add property groups to a Dropbox file. See templatesAddForUser() or
     * templatesAddForTeam() to create new templates.
     *
     * Route attributes:
     *   scope: files.metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.AddPropertiesError>.
     * @param arg The request parameters.
     */
    public filePropertiesPropertiesAdd(arg: file_properties.AddPropertiesArg): Promise<DropboxResponse<void>>;

    /**
     * Overwrite property groups associated with a file. This endpoint should be
     * used instead of propertiesUpdate() when property groups are being updated
     * via a "snapshot" instead of via a "delta". In other words, this endpoint
     * will delete all omitted fields from a property group, whereas
     * propertiesUpdate() will only delete fields that are explicitly marked for
     * deletion.
     *
     * Route attributes:
     *   scope: files.metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.InvalidPropertyGroupError>.
     * @param arg The request parameters.
     */
    public filePropertiesPropertiesOverwrite(arg: file_properties.OverwritePropertyGroupArg): Promise<DropboxResponse<void>>;

    /**
     * Permanently removes the specified property group from the file. To remove
     * specific property field key value pairs, see propertiesUpdate(). To
     * update a template, see templatesUpdateForUser() or
     * templatesUpdateForTeam(). To remove a template, see
     * templatesRemoveForUser() or templatesRemoveForTeam().
     *
     * Route attributes:
     *   scope: files.metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.RemovePropertiesError>.
     * @param arg The request parameters.
     */
    public filePropertiesPropertiesRemove(arg: file_properties.RemovePropertiesArg): Promise<DropboxResponse<void>>;

    /**
     * Search across property templates for particular property field values.
     *
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.PropertiesSearchError>.
     * @param arg The request parameters.
     */
    public filePropertiesPropertiesSearch(arg: file_properties.PropertiesSearchArg): Promise<DropboxResponse<file_properties.PropertiesSearchResult>>;

    /**
     * Once a cursor has been retrieved from propertiesSearch(), use this to
     * paginate through all search results.
     *
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.PropertiesSearchContinueError>.
     * @param arg The request parameters.
     */
    public filePropertiesPropertiesSearchContinue(arg: file_properties.PropertiesSearchContinueArg): Promise<DropboxResponse<file_properties.PropertiesSearchResult>>;

    /**
     * Add, update or remove properties associated with the supplied file and
     * templates. This endpoint should be used instead of propertiesOverwrite()
     * when property groups are being updated via a "delta" instead of via a
     * "snapshot" . In other words, this endpoint will not delete any omitted
     * fields from a property group, whereas propertiesOverwrite() will delete
     * any fields that are omitted from a property group.
     *
     * Route attributes:
     *   scope: files.metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.UpdatePropertiesError>.
     * @param arg The request parameters.
     */
    public filePropertiesPropertiesUpdate(arg: file_properties.UpdatePropertiesArg): Promise<DropboxResponse<void>>;

    /**
     * Add a template associated with a team. See propertiesAdd() to add
     * properties to a file or folder. Note: this endpoint will create
     * team-owned templates.
     *
     * Route attributes:
     *   scope: files.team_metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.ModifyTemplateError>.
     * @param arg The request parameters.
     */
    public filePropertiesTemplatesAddForTeam(arg: file_properties.AddTemplateArg): Promise<DropboxResponse<file_properties.AddTemplateResult>>;

    /**
     * Add a template associated with a user. See propertiesAdd() to add
     * properties to a file. This endpoint can't be called on a team member or
     * admin's behalf.
     *
     * Route attributes:
     *   scope: files.metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.ModifyTemplateError>.
     * @param arg The request parameters.
     */
    public filePropertiesTemplatesAddForUser(arg: file_properties.AddTemplateArg): Promise<DropboxResponse<file_properties.AddTemplateResult>>;

    /**
     * Get the schema for a specified template.
     *
     * Route attributes:
     *   scope: files.team_metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.TemplateError>.
     * @param arg The request parameters.
     */
    public filePropertiesTemplatesGetForTeam(arg: file_properties.GetTemplateArg): Promise<DropboxResponse<file_properties.GetTemplateResult>>;

    /**
     * Get the schema for a specified template. This endpoint can't be called on
     * a team member or admin's behalf.
     *
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.TemplateError>.
     * @param arg The request parameters.
     */
    public filePropertiesTemplatesGetForUser(arg: file_properties.GetTemplateArg): Promise<DropboxResponse<file_properties.GetTemplateResult>>;

    /**
     * Get the template identifiers for a team. To get the schema of each
     * template use templatesGetForTeam().
     *
     * Route attributes:
     *   scope: files.team_metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.TemplateError>.
     */
    public filePropertiesTemplatesListForTeam(): Promise<DropboxResponse<file_properties.ListTemplateResult>>;

    /**
     * Get the template identifiers for a team. To get the schema of each
     * template use templatesGetForUser(). This endpoint can't be called on a
     * team member or admin's behalf.
     *
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.TemplateError>.
     */
    public filePropertiesTemplatesListForUser(): Promise<DropboxResponse<file_properties.ListTemplateResult>>;

    /**
     * Permanently removes the specified template created from
     * templatesAddForUser(). All properties associated with the template will
     * also be removed. This action cannot be undone.
     *
     * Route attributes:
     *   scope: files.team_metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.TemplateError>.
     * @param arg The request parameters.
     */
    public filePropertiesTemplatesRemoveForTeam(arg: file_properties.RemoveTemplateArg): Promise<DropboxResponse<void>>;

    /**
     * Permanently removes the specified template created from
     * templatesAddForUser(). All properties associated with the template will
     * also be removed. This action cannot be undone.
     *
     * Route attributes:
     *   scope: files.metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.TemplateError>.
     * @param arg The request parameters.
     */
    public filePropertiesTemplatesRemoveForUser(arg: file_properties.RemoveTemplateArg): Promise<DropboxResponse<void>>;

    /**
     * Update a template associated with a team. This route can update the
     * template name, the template description and add optional properties to
     * templates.
     *
     * Route attributes:
     *   scope: files.team_metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.ModifyTemplateError>.
     * @param arg The request parameters.
     */
    public filePropertiesTemplatesUpdateForTeam(arg: file_properties.UpdateTemplateArg): Promise<DropboxResponse<file_properties.UpdateTemplateResult>>;

    /**
     * Update a template associated with a user. This route can update the
     * template name, the template description and add optional properties to
     * templates. This endpoint can't be called on a team member or admin's
     * behalf.
     *
     * Route attributes:
     *   scope: files.metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.ModifyTemplateError>.
     * @param arg The request parameters.
     */
    public filePropertiesTemplatesUpdateForUser(arg: file_properties.UpdateTemplateArg): Promise<DropboxResponse<file_properties.UpdateTemplateResult>>;

    /**
     * Returns the total number of file requests owned by this user. Includes
     * both open and closed file requests.
     *
     * Route attributes:
     *   scope: file_requests.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_requests.CountFileRequestsError>.
     */
    public fileRequestsCount(): Promise<DropboxResponse<file_requests.CountFileRequestsResult>>;

    /**
     * Creates a file request for this user.
     *
     * Route attributes:
     *   scope: file_requests.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_requests.CreateFileRequestError>.
     * @param arg The request parameters.
     */
    public fileRequestsCreate(arg: file_requests.CreateFileRequestArgs): Promise<DropboxResponse<file_requests.FileRequest>>;

    /**
     * Delete a batch of closed file requests.
     *
     * Route attributes:
     *   scope: file_requests.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_requests.DeleteFileRequestError>.
     * @param arg The request parameters.
     */
    public fileRequestsDelete(arg: file_requests.DeleteFileRequestArgs): Promise<DropboxResponse<file_requests.DeleteFileRequestsResult>>;

    /**
     * Delete all closed file requests owned by this user.
     *
     * Route attributes:
     *   scope: file_requests.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_requests.DeleteAllClosedFileRequestsError>.
     */
    public fileRequestsDeleteAllClosed(): Promise<DropboxResponse<file_requests.DeleteAllClosedFileRequestsResult>>;

    /**
     * Returns the specified file request.
     *
     * Route attributes:
     *   scope: file_requests.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_requests.GetFileRequestError>.
     * @param arg The request parameters.
     */
    public fileRequestsGet(arg: file_requests.GetFileRequestArgs): Promise<DropboxResponse<file_requests.FileRequest>>;

    /**
     * Returns a list of file requests owned by this user. For apps with the app
     * folder permission, this will only return file requests with destinations
     * in the app folder.
     *
     * Route attributes:
     *   scope: file_requests.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_requests.ListFileRequestsError>.
     * @param arg The request parameters.
     */
    public fileRequestsListV2(arg: file_requests.ListFileRequestsArg): Promise<DropboxResponse<file_requests.ListFileRequestsV2Result>>;

    /**
     * Returns a list of file requests owned by this user. For apps with the app
     * folder permission, this will only return file requests with destinations
     * in the app folder.
     *
     * Route attributes:
     *   scope: file_requests.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_requests.ListFileRequestsError>.
     */
    public fileRequestsList(): Promise<DropboxResponse<file_requests.ListFileRequestsResult>>;

    /**
     * Once a cursor has been retrieved from listV2(), use this to paginate
     * through all file requests. The cursor must come from a previous call to
     * listV2() or listContinue().
     *
     * Route attributes:
     *   scope: file_requests.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_requests.ListFileRequestsContinueError>.
     * @param arg The request parameters.
     */
    public fileRequestsListContinue(arg: file_requests.ListFileRequestsContinueArg): Promise<DropboxResponse<file_requests.ListFileRequestsV2Result>>;

    /**
     * Update a file request.
     *
     * Route attributes:
     *   scope: file_requests.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_requests.UpdateFileRequestError>.
     * @param arg The request parameters.
     */
    public fileRequestsUpdate(arg: file_requests.UpdateFileRequestArgs): Promise<DropboxResponse<file_requests.FileRequest>>;

    /**
     * Returns the metadata for a file or folder. This is an alpha endpoint
     * compatible with the properties API. Note: Metadata for the root folder is
     * unsupported.
     *
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.AlphaGetMetadataError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesAlphaGetMetadata(arg: files.AlphaGetMetadataArg): Promise<DropboxResponse<files.FileMetadataReference|files.FolderMetadataReference|files.DeletedMetadataReference>>;

    /**
     * Create a new file with the contents provided in the request. Note that
     * the behavior of this alpha endpoint is unstable and subject to change. Do
     * not use this to upload a file larger than 150 MB. Instead, create an
     * upload session with uploadSessionStart().
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.UploadError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesAlphaUpload(arg: files.UploadArg): Promise<DropboxResponse<files.FileMetadata>>;

    /**
     * Copy a file or folder to a different location in the user's Dropbox. If
     * the source path is a folder all its contents will be copied.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.RelocationError>.
     * @param arg The request parameters.
     */
    public filesCopyV2(arg: files.RelocationArg): Promise<DropboxResponse<files.RelocationResult>>;

    /**
     * Copy a file or folder to a different location in the user's Dropbox. If
     * the source path is a folder all its contents will be copied.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.RelocationError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesCopy(arg: files.RelocationArg): Promise<DropboxResponse<files.FileMetadataReference|files.FolderMetadataReference|files.DeletedMetadataReference>>;

    /**
     * Copy multiple files or folders to different locations at once in the
     * user's Dropbox. This route will replace copyBatch(). The main difference
     * is this route will return status for each entry, while copyBatch() raises
     * failure if any entry fails. This route will either finish synchronously,
     * or return a job ID and do the async copy job in background. Please use
     * copyBatchCheckV2() to check the job status.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @param arg The request parameters.
     */
    public filesCopyBatchV2(arg: files.CopyBatchArg): Promise<DropboxResponse<files.RelocationBatchV2Launch>>;

    /**
     * Copy multiple files or folders to different locations at once in the
     * user's Dropbox. This route will return job ID immediately and do the
     * async copy job in background. Please use copyBatchCheck() to check the
     * job status.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesCopyBatch(arg: files.RelocationBatchArg): Promise<DropboxResponse<files.RelocationBatchLaunch>>;

    /**
     * Returns the status of an asynchronous job for copyBatchV2(). It returns
     * list of results for each entry.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<async.PollError>.
     * @param arg The request parameters.
     */
    public filesCopyBatchCheckV2(arg: async.PollArg): Promise<DropboxResponse<files.RelocationBatchV2JobStatus>>;

    /**
     * Returns the status of an asynchronous job for copyBatch(). If success, it
     * returns list of results for each entry.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<async.PollError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesCopyBatchCheck(arg: async.PollArg): Promise<DropboxResponse<files.RelocationBatchJobStatus>>;

    /**
     * Get a copy reference to a file or folder. This reference string can be
     * used to save that file or folder to another user's Dropbox by passing it
     * to copyReferenceSave().
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.GetCopyReferenceError>.
     * @param arg The request parameters.
     */
    public filesCopyReferenceGet(arg: files.GetCopyReferenceArg): Promise<DropboxResponse<files.GetCopyReferenceResult>>;

    /**
     * Save a copy reference returned by copyReferenceGet() to the user's
     * Dropbox.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.SaveCopyReferenceError>.
     * @param arg The request parameters.
     */
    public filesCopyReferenceSave(arg: files.SaveCopyReferenceArg): Promise<DropboxResponse<files.SaveCopyReferenceResult>>;

    /**
     * Create a folder at a given path.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.CreateFolderError>.
     * @param arg The request parameters.
     */
    public filesCreateFolderV2(arg: files.CreateFolderArg): Promise<DropboxResponse<files.CreateFolderResult>>;

    /**
     * Create a folder at a given path.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.CreateFolderError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesCreateFolder(arg: files.CreateFolderArg): Promise<DropboxResponse<files.FolderMetadata>>;

    /**
     * Create multiple folders at once. This route is asynchronous for large
     * batches, which returns a job ID immediately and runs the create folder
     * batch asynchronously. Otherwise, creates the folders and returns the
     * result synchronously for smaller inputs. You can force asynchronous
     * behaviour by using the CreateFolderBatchArg.force_async flag.  Use
     * createFolderBatchCheck() to check the job status.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @param arg The request parameters.
     */
    public filesCreateFolderBatch(arg: files.CreateFolderBatchArg): Promise<DropboxResponse<files.CreateFolderBatchLaunch>>;

    /**
     * Returns the status of an asynchronous job for createFolderBatch(). If
     * success, it returns list of result for each entry.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<async.PollError>.
     * @param arg The request parameters.
     */
    public filesCreateFolderBatchCheck(arg: async.PollArg): Promise<DropboxResponse<files.CreateFolderBatchJobStatus>>;

    /**
     * Delete the file or folder at a given path. If the path is a folder, all
     * its contents will be deleted too. A successful response indicates that
     * the file or folder was deleted. The returned metadata will be the
     * corresponding FileMetadata or FolderMetadata for the item at time of
     * deletion, and not a DeletedMetadata object.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.DeleteError>.
     * @param arg The request parameters.
     */
    public filesDeleteV2(arg: files.DeleteArg): Promise<DropboxResponse<files.DeleteResult>>;

    /**
     * Delete the file or folder at a given path. If the path is a folder, all
     * its contents will be deleted too. A successful response indicates that
     * the file or folder was deleted. The returned metadata will be the
     * corresponding FileMetadata or FolderMetadata for the item at time of
     * deletion, and not a DeletedMetadata object.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.DeleteError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesDelete(arg: files.DeleteArg): Promise<DropboxResponse<files.FileMetadataReference|files.FolderMetadataReference|files.DeletedMetadataReference>>;

    /**
     * Delete multiple files/folders at once. This route is asynchronous, which
     * returns a job ID immediately and runs the delete batch asynchronously.
     * Use deleteBatchCheck() to check the job status.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @param arg The request parameters.
     */
    public filesDeleteBatch(arg: files.DeleteBatchArg): Promise<DropboxResponse<files.DeleteBatchLaunch>>;

    /**
     * Returns the status of an asynchronous job for deleteBatch(). If success,
     * it returns list of result for each entry.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<async.PollError>.
     * @param arg The request parameters.
     */
    public filesDeleteBatchCheck(arg: async.PollArg): Promise<DropboxResponse<files.DeleteBatchJobStatus>>;

    /**
     * Download a file from a user's Dropbox.
     *
     * Route attributes:
     *   scope: files.content.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.DownloadError>.
     * @param arg The request parameters.
     */
    public filesDownload(arg: files.DownloadArg): Promise<DropboxResponse<files.FileMetadata>>;

    /**
     * Download a folder from the user's Dropbox, as a zip file. The folder must
     * be less than 20 GB in size and any single file within must be less than 4
     * GB in size. The resulting zip must have fewer than 10,000 total file and
     * folder entries, including the top level folder. The input cannot be a
     * single file. Note: this endpoint does not support HTTP range requests.
     *
     * Route attributes:
     *   scope: files.content.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.DownloadZipError>.
     * @param arg The request parameters.
     */
    public filesDownloadZip(arg: files.DownloadZipArg): Promise<DropboxResponse<files.DownloadZipResult>>;

    /**
     * Export a file from a user's Dropbox. This route only supports exporting
     * files that cannot be downloaded directly  and whose
     * ExportResult.file_metadata has ExportInfo.export_as populated.
     *
     * Route attributes:
     *   scope: files.content.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.ExportError>.
     * @param arg The request parameters.
     */
    public filesExport(arg: files.ExportArg): Promise<DropboxResponse<files.ExportResult>>;

    /**
     * Return the lock metadata for the given list of paths.
     *
     * Route attributes:
     *   scope: files.content.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.LockFileError>.
     * @param arg The request parameters.
     */
    public filesGetFileLockBatch(arg: files.LockFileBatchArg): Promise<DropboxResponse<files.LockFileBatchResult>>;

    /**
     * Returns the metadata for a file or folder. Note: Metadata for the root
     * folder is unsupported.
     *
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.GetMetadataError>.
     * @param arg The request parameters.
     */
    public filesGetMetadata(arg: files.GetMetadataArg): Promise<DropboxResponse<files.FileMetadataReference|files.FolderMetadataReference|files.DeletedMetadataReference>>;

    /**
     * Get a preview for a file. Currently, PDF previews are generated for files
     * with the following extensions: .ai, .doc, .docm, .docx, .eps, .gdoc,
     * .gslides, .odp, .odt, .pps, .ppsm, .ppsx, .ppt, .pptm, .pptx, .rtf. HTML
     * previews are generated for files with the following extensions: .csv,
     * .ods, .xls, .xlsm, .gsheet, .xlsx. Other formats will return an
     * unsupported extension error.
     *
     * Route attributes:
     *   scope: files.content.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.PreviewError>.
     * @param arg The request parameters.
     */
    public filesGetPreview(arg: files.PreviewArg): Promise<DropboxResponse<files.FileMetadata>>;

    /**
     * Get a temporary link to stream content of a file. This link will expire
     * in four hours and afterwards you will get 410 Gone. This URL should not
     * be used to display content directly in the browser. The Content-Type of
     * the link is determined automatically by the file's mime type.
     *
     * Route attributes:
     *   scope: files.content.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.GetTemporaryLinkError>.
     * @param arg The request parameters.
     */
    public filesGetTemporaryLink(arg: files.GetTemporaryLinkArg): Promise<DropboxResponse<files.GetTemporaryLinkResult>>;

    /**
     * Get a one-time use temporary upload link to upload a file to a Dropbox
     * location.  This endpoint acts as a delayed upload(). The returned
     * temporary upload link may be used to make a POST request with the data to
     * be uploaded. The upload will then be perfomed with the CommitInfo
     * previously provided to getTemporaryUploadLink() but evaluated only upon
     * consumption. Hence, errors stemming from invalid CommitInfo with respect
     * to the state of the user's Dropbox will only be communicated at
     * consumption time. Additionally, these errors are surfaced as generic HTTP
     * 409 Conflict responses, potentially hiding issue details. The maximum
     * temporary upload link duration is 4 hours. Upon consumption or
     * expiration, a new link will have to be generated. Multiple links may
     * exist for a specific upload path at any given time.  The POST request on
     * the temporary upload link must have its Content-Type set to
     * "application/octet-stream".  Example temporary upload link consumption
     * request:  curl -X POST
     * https://content.dropboxapi.com/apitul/1/bNi2uIYF51cVBND --header
     * "Content-Type: application/octet-stream" --data-binary @local_file.txt  A
     * successful temporary upload link consumption request returns the content
     * hash of the uploaded data in JSON format.  Example successful temporary
     * upload link consumption response: {"content-hash":
     * "599d71033d700ac892a0e48fa61b125d2f5994"}  An unsuccessful temporary
     * upload link consumption request returns any of the following status
     * codes:  HTTP 400 Bad Request: Content-Type is not one of
     * application/octet-stream and text/plain or request is invalid. HTTP 409
     * Conflict: The temporary upload link does not exist or is currently
     * unavailable, the upload failed, or another error happened. HTTP 410 Gone:
     * The temporary upload link is expired or consumed.  Example unsuccessful
     * temporary upload link consumption response: Temporary upload link has
     * been recently consumed.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @param arg The request parameters.
     */
    public filesGetTemporaryUploadLink(arg: files.GetTemporaryUploadLinkArg): Promise<DropboxResponse<files.GetTemporaryUploadLinkResult>>;

    /**
     * Get a thumbnail for an image. This method currently supports files with
     * the following file extensions: jpg, jpeg, png, tiff, tif, gif, webp, ppm
     * and bmp. Photos that are larger than 20MB in size won't be converted to a
     * thumbnail.
     *
     * Route attributes:
     *   scope: files.content.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.ThumbnailError>.
     * @param arg The request parameters.
     */
    public filesGetThumbnail(arg: files.ThumbnailArg): Promise<DropboxResponse<files.FileMetadata>>;

    /**
     * Get a thumbnail for an image. This method currently supports files with
     * the following file extensions: jpg, jpeg, png, tiff, tif, gif, webp, ppm
     * and bmp. Photos that are larger than 20MB in size won't be converted to a
     * thumbnail.
     *
     * Route attributes:
     *   scope: files.content.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.ThumbnailV2Error>.
     * @param arg The request parameters.
     */
    public filesGetThumbnailV2(arg: files.ThumbnailV2Arg): Promise<DropboxResponse<files.PreviewResult>>;

    /**
     * Get thumbnails for a list of images. We allow up to 25 thumbnails in a
     * single batch. This method currently supports files with the following
     * file extensions: jpg, jpeg, png, tiff, tif, gif, webp, ppm and bmp.
     * Photos that are larger than 20MB in size won't be converted to a
     * thumbnail.
     *
     * Route attributes:
     *   scope: files.content.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.GetThumbnailBatchError>.
     * @param arg The request parameters.
     */
    public filesGetThumbnailBatch(arg: files.GetThumbnailBatchArg): Promise<DropboxResponse<files.GetThumbnailBatchResult>>;

    /**
     * Starts returning the contents of a folder. If the result's
     * ListFolderResult.has_more field is true, call listFolderContinue() with
     * the returned ListFolderResult.cursor to retrieve more entries. If you're
     * using ListFolderArg.recursive set to true to keep a local cache of the
     * contents of a Dropbox account, iterate through each entry in order and
     * process them as follows to keep your local state in sync: For each
     * FileMetadata, store the new entry at the given path in your local state.
     * If the required parent folders don't exist yet, create them. If there's
     * already something else at the given path, replace it and remove all its
     * children. For each FolderMetadata, store the new entry at the given path
     * in your local state. If the required parent folders don't exist yet,
     * create them. If there's already something else at the given path, replace
     * it but leave the children as they are. Check the new entry's
     * FolderSharingInfo.read_only and set all its children's read-only statuses
     * to match. For each DeletedMetadata, if your local state has something at
     * the given path, remove it and all its children. If there's nothing at the
     * given path, ignore this entry. Note: auth.RateLimitError may be returned
     * if multiple listFolder() or listFolderContinue() calls with same
     * parameters are made simultaneously by same API app for same user. If your
     * app implements retry logic, please hold off the retry until the previous
     * request finishes.
     *
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.ListFolderError>.
     * @param arg The request parameters.
     */
    public filesListFolder(arg: files.ListFolderArg): Promise<DropboxResponse<files.ListFolderResult>>;

    /**
     * Once a cursor has been retrieved from listFolder(), use this to paginate
     * through all files and retrieve updates to the folder, following the same
     * rules as documented for listFolder().
     *
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.ListFolderContinueError>.
     * @param arg The request parameters.
     */
    public filesListFolderContinue(arg: files.ListFolderContinueArg): Promise<DropboxResponse<files.ListFolderResult>>;

    /**
     * A way to quickly get a cursor for the folder's state. Unlike
     * listFolder(), listFolderGetLatestCursor() doesn't return any entries.
     * This endpoint is for app which only needs to know about new files and
     * modifications and doesn't need to know about files that already exist in
     * Dropbox.
     *
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.ListFolderError>.
     * @param arg The request parameters.
     */
    public filesListFolderGetLatestCursor(arg: files.ListFolderArg): Promise<DropboxResponse<files.ListFolderGetLatestCursorResult>>;

    /**
     * A longpoll endpoint to wait for changes on an account. In conjunction
     * with listFolderContinue(), this call gives you a low-latency way to
     * monitor an account for file changes. The connection will block until
     * there are changes available or a timeout occurs. This endpoint is useful
     * mostly for client-side apps. If you're looking for server-side
     * notifications, check out our [webhooks documentation]{@link
     * https://www.dropbox.com/developers/reference/webhooks}.
     *
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.ListFolderLongpollError>.
     * @param arg The request parameters.
     */
    public filesListFolderLongpoll(arg: files.ListFolderLongpollArg): Promise<DropboxResponse<files.ListFolderLongpollResult>>;

    /**
     * Returns revisions for files based on a file path or a file id. The file
     * path or file id is identified from the latest file entry at the given
     * file path or id. This end point allows your app to query either by file
     * path or file id by setting the mode parameter appropriately. In the
     * ListRevisionsMode.path (default) mode, all revisions at the same file
     * path as the latest file entry are returned. If revisions with the same
     * file id are desired, then mode must be set to ListRevisionsMode.id. The
     * ListRevisionsMode.id mode is useful to retrieve revisions for a given
     * file across moves or renames.
     *
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.ListRevisionsError>.
     * @param arg The request parameters.
     */
    public filesListRevisions(arg: files.ListRevisionsArg): Promise<DropboxResponse<files.ListRevisionsResult>>;

    /**
     * Lock the files at the given paths. A locked file will be writable only by
     * the lock holder. A successful response indicates that the file has been
     * locked. Returns a list of the locked file paths and their metadata after
     * this operation.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.LockFileError>.
     * @param arg The request parameters.
     */
    public filesLockFileBatch(arg: files.LockFileBatchArg): Promise<DropboxResponse<files.LockFileBatchResult>>;

    /**
     * Move a file or folder to a different location in the user's Dropbox. If
     * the source path is a folder all its contents will be moved. Note that we
     * do not currently support case-only renaming.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.RelocationError>.
     * @param arg The request parameters.
     */
    public filesMoveV2(arg: files.RelocationArg): Promise<DropboxResponse<files.RelocationResult>>;

    /**
     * Move a file or folder to a different location in the user's Dropbox. If
     * the source path is a folder all its contents will be moved.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.RelocationError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesMove(arg: files.RelocationArg): Promise<DropboxResponse<files.FileMetadataReference|files.FolderMetadataReference|files.DeletedMetadataReference>>;

    /**
     * Move multiple files or folders to different locations at once in the
     * user's Dropbox. Note that we do not currently support case-only renaming.
     * This route will replace moveBatch(). The main difference is this route
     * will return status for each entry, while moveBatch() raises failure if
     * any entry fails. This route will either finish synchronously, or return a
     * job ID and do the async move job in background. Please use
     * moveBatchCheckV2() to check the job status.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @param arg The request parameters.
     */
    public filesMoveBatchV2(arg: files.MoveBatchArg): Promise<DropboxResponse<files.RelocationBatchV2Launch>>;

    /**
     * Move multiple files or folders to different locations at once in the
     * user's Dropbox. This route will return job ID immediately and do the
     * async moving job in background. Please use moveBatchCheck() to check the
     * job status.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesMoveBatch(arg: files.RelocationBatchArg): Promise<DropboxResponse<files.RelocationBatchLaunch>>;

    /**
     * Returns the status of an asynchronous job for moveBatchV2(). It returns
     * list of results for each entry.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<async.PollError>.
     * @param arg The request parameters.
     */
    public filesMoveBatchCheckV2(arg: async.PollArg): Promise<DropboxResponse<files.RelocationBatchV2JobStatus>>;

    /**
     * Returns the status of an asynchronous job for moveBatch(). If success, it
     * returns list of results for each entry.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<async.PollError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesMoveBatchCheck(arg: async.PollArg): Promise<DropboxResponse<files.RelocationBatchJobStatus>>;

    /**
     * Creates a new Paper doc with the provided content.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.PaperCreateError>.
     * @param arg The request parameters.
     */
    public filesPaperCreate(arg: files.PaperCreateArg): Promise<DropboxResponse<files.PaperCreateResult>>;

    /**
     * Updates an existing Paper doc with the provided content.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.PaperUpdateError>.
     * @param arg The request parameters.
     */
    public filesPaperUpdate(arg: files.PaperUpdateArg): Promise<DropboxResponse<files.PaperUpdateResult>>;

    /**
     * Permanently delete the file or folder at a given path (see
     * https://www.dropbox.com/en/help/40). If the given file or folder is not
     * yet deleted, this route will first delete it. It is possible for this
     * route to successfully delete, then fail to permanently delete. Note: This
     * endpoint is only available for Dropbox Business apps.
     *
     * Route attributes:
     *   scope: files.permanent_delete
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.DeleteError>.
     * @param arg The request parameters.
     */
    public filesPermanentlyDelete(arg: files.DeleteArg): Promise<DropboxResponse<void>>;

    /**
     * Route attributes:
     *   scope: files.metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.AddPropertiesError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesPropertiesAdd(arg: file_properties.AddPropertiesArg): Promise<DropboxResponse<void>>;

    /**
     * Route attributes:
     *   scope: files.metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.InvalidPropertyGroupError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesPropertiesOverwrite(arg: file_properties.OverwritePropertyGroupArg): Promise<DropboxResponse<void>>;

    /**
     * Route attributes:
     *   scope: files.metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.RemovePropertiesError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesPropertiesRemove(arg: file_properties.RemovePropertiesArg): Promise<DropboxResponse<void>>;

    /**
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.TemplateError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesPropertiesTemplateGet(arg: file_properties.GetTemplateArg): Promise<DropboxResponse<file_properties.GetTemplateResult>>;

    /**
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.TemplateError>.
     * @deprecated
     */
    public filesPropertiesTemplateList(): Promise<DropboxResponse<file_properties.ListTemplateResult>>;

    /**
     * Route attributes:
     *   scope: files.metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.UpdatePropertiesError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesPropertiesUpdate(arg: file_properties.UpdatePropertiesArg): Promise<DropboxResponse<void>>;

    /**
     * Restore a specific revision of a file to the given path.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.RestoreError>.
     * @param arg The request parameters.
     */
    public filesRestore(arg: files.RestoreArg): Promise<DropboxResponse<files.FileMetadata>>;

    /**
     * Save the data from a specified URL into a file in user's Dropbox. Note
     * that the transfer from the URL must complete within 5 minutes, or the
     * operation will time out and the job will fail. If the given path already
     * exists, the file will be renamed to avoid the conflict (e.g. myfile
     * (1).txt).
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.SaveUrlError>.
     * @param arg The request parameters.
     */
    public filesSaveUrl(arg: files.SaveUrlArg): Promise<DropboxResponse<files.SaveUrlResult>>;

    /**
     * Check the status of a saveUrl() job.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<async.PollError>.
     * @param arg The request parameters.
     */
    public filesSaveUrlCheckJobStatus(arg: async.PollArg): Promise<DropboxResponse<files.SaveUrlJobStatus>>;

    /**
     * Searches for files and folders. Note: Recent changes will be reflected in
     * search results within a few seconds and older revisions of existing files
     * may still match your query for up to a few days.
     *
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.SearchError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesSearch(arg: files.SearchArg): Promise<DropboxResponse<files.SearchResult>>;

    /**
     * Searches for files and folders. Note: searchV2() along with
     * searchContinueV2() can only be used to retrieve a maximum of 10,000
     * matches. Recent changes may not immediately be reflected in search
     * results due to a short delay in indexing. Duplicate results may be
     * returned across pages. Some results may not be returned.
     *
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.SearchError>.
     * @param arg The request parameters.
     */
    public filesSearchV2(arg: files.SearchV2Arg): Promise<DropboxResponse<files.SearchV2Result>>;

    /**
     * Fetches the next page of search results returned from searchV2(). Note:
     * searchV2() along with searchContinueV2() can only be used to retrieve a
     * maximum of 10,000 matches. Recent changes may not immediately be
     * reflected in search results due to a short delay in indexing. Duplicate
     * results may be returned across pages. Some results may not be returned.
     *
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.SearchError>.
     * @param arg The request parameters.
     */
    public filesSearchContinueV2(arg: files.SearchV2ContinueArg): Promise<DropboxResponse<files.SearchV2Result>>;

    /**
     * Add a tag to an item. A tag is a string. The strings are automatically
     * converted to lowercase letters. No more than 20 tags can be added to a
     * given item.
     *
     * Route attributes:
     *   scope: files.metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.AddTagError>.
     * @param arg The request parameters.
     */
    public filesTagsAdd(arg: files.AddTagArg): Promise<DropboxResponse<void>>;

    /**
     * Get list of tags assigned to items.
     *
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.BaseTagError>.
     * @param arg The request parameters.
     */
    public filesTagsGet(arg: files.GetTagsArg): Promise<DropboxResponse<files.GetTagsResult>>;

    /**
     * Remove a tag from an item.
     *
     * Route attributes:
     *   scope: files.metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.RemoveTagError>.
     * @param arg The request parameters.
     */
    public filesTagsRemove(arg: files.RemoveTagArg): Promise<DropboxResponse<void>>;

    /**
     * Unlock the files at the given paths. A locked file can only be unlocked
     * by the lock holder or, if a business account, a team admin. A successful
     * response indicates that the file has been unlocked. Returns a list of the
     * unlocked file paths and their metadata after this operation.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.LockFileError>.
     * @param arg The request parameters.
     */
    public filesUnlockFileBatch(arg: files.UnlockFileBatchArg): Promise<DropboxResponse<files.LockFileBatchResult>>;

    /**
     * Create a new file with the contents provided in the request. Do not use
     * this to upload a file larger than 150 MB. Instead, create an upload
     * session with uploadSessionStart(). Calls to this endpoint will count as
     * data transport calls for any Dropbox Business teams with a limit on the
     * number of data transport calls allowed per month. For more information,
     * see the [Data transport limit page]{@link
     * https://www.dropbox.com/developers/reference/data-transport-limit}.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.UploadError>.
     * @param arg The request parameters.
     */
    public filesUpload(arg: files.UploadArg): Promise<DropboxResponse<files.FileMetadata>>;

    /**
     * Append more data to an upload session. When the parameter close is set,
     * this call will close the session. A single request should not upload more
     * than 150 MB. The maximum size of a file one can upload to an upload
     * session is 350 GB. Calls to this endpoint will count as data transport
     * calls for any Dropbox Business teams with a limit on the number of data
     * transport calls allowed per month. For more information, see the [Data
     * transport limit page]{@link
     * https://www.dropbox.com/developers/reference/data-transport-limit}.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.UploadSessionAppendError>.
     * @param arg The request parameters.
     */
    public filesUploadSessionAppendV2(arg: files.UploadSessionAppendArg): Promise<DropboxResponse<void>>;

    /**
     * Append more data to an upload session. A single request should not upload
     * more than 150 MB. The maximum size of a file one can upload to an upload
     * session is 350 GB. Calls to this endpoint will count as data transport
     * calls for any Dropbox Business teams with a limit on the number of data
     * transport calls allowed per month. For more information, see the [Data
     * transport limit page]{@link
     * https://www.dropbox.com/developers/reference/data-transport-limit}.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.UploadSessionAppendError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesUploadSessionAppend(arg: files.UploadSessionCursor): Promise<DropboxResponse<void>>;

    /**
     * Finish an upload session and save the uploaded data to the given file
     * path. A single request should not upload more than 150 MB. The maximum
     * size of a file one can upload to an upload session is 350 GB. Calls to
     * this endpoint will count as data transport calls for any Dropbox Business
     * teams with a limit on the number of data transport calls allowed per
     * month. For more information, see the [Data transport limit page]{@link
     * https://www.dropbox.com/developers/reference/data-transport-limit}.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.UploadSessionFinishError>.
     * @param arg The request parameters.
     */
    public filesUploadSessionFinish(arg: files.UploadSessionFinishArg): Promise<DropboxResponse<files.FileMetadata>>;

    /**
     * This route helps you commit many files at once into a user's Dropbox. Use
     * uploadSessionStart() and uploadSessionAppendV2() to upload file contents.
     * We recommend uploading many files in parallel to increase throughput.
     * Once the file contents have been uploaded, rather than calling
     * uploadSessionFinish(), use this route to finish all your upload sessions
     * in a single request. UploadSessionStartArg.close or
     * UploadSessionAppendArg.close needs to be true for the last
     * uploadSessionStart() or uploadSessionAppendV2() call. The maximum size of
     * a file one can upload to an upload session is 350 GB. This route will
     * return a job_id immediately and do the async commit job in background.
     * Use uploadSessionFinishBatchCheck() to check the job status. For the same
     * account, this route should be executed serially. That means you should
     * not start the next job before current job finishes. We allow up to 1000
     * entries in a single request. Calls to this endpoint will count as data
     * transport calls for any Dropbox Business teams with a limit on the number
     * of data transport calls allowed per month. For more information, see the
     * [Data transport limit page]{@link
     * https://www.dropbox.com/developers/reference/data-transport-limit}.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @deprecated
     * @param arg The request parameters.
     */
    public filesUploadSessionFinishBatch(arg: files.UploadSessionFinishBatchArg): Promise<DropboxResponse<files.UploadSessionFinishBatchLaunch>>;

    /**
     * This route helps you commit many files at once into a user's Dropbox. Use
     * uploadSessionStart() and uploadSessionAppendV2() to upload file contents.
     * We recommend uploading many files in parallel to increase throughput.
     * Once the file contents have been uploaded, rather than calling
     * uploadSessionFinish(), use this route to finish all your upload sessions
     * in a single request. UploadSessionStartArg.close or
     * UploadSessionAppendArg.close needs to be true for the last
     * uploadSessionStart() or uploadSessionAppendV2() call of each upload
     * session. The maximum size of a file one can upload to an upload session
     * is 350 GB. We allow up to 1000 entries in a single request. Calls to this
     * endpoint will count as data transport calls for any Dropbox Business
     * teams with a limit on the number of data transport calls allowed per
     * month. For more information, see the [Data transport limit page]{@link
     * https://www.dropbox.com/developers/reference/data-transport-limit}.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @param arg The request parameters.
     */
    public filesUploadSessionFinishBatchV2(arg: files.UploadSessionFinishBatchArg): Promise<DropboxResponse<files.UploadSessionFinishBatchResult>>;

    /**
     * Returns the status of an asynchronous job for uploadSessionFinishBatch().
     * If success, it returns list of result for each entry.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<async.PollError>.
     * @param arg The request parameters.
     */
    public filesUploadSessionFinishBatchCheck(arg: async.PollArg): Promise<DropboxResponse<files.UploadSessionFinishBatchJobStatus>>;

    /**
     * Upload sessions allow you to upload a single file in one or more
     * requests, for example where the size of the file is greater than 150 MB.
     * This call starts a new upload session with the given data. You can then
     * use uploadSessionAppendV2() to add more data and uploadSessionFinish() to
     * save all the data to a file in Dropbox. A single request should not
     * upload more than 150 MB. The maximum size of a file one can upload to an
     * upload session is 350 GB. An upload session can be used for a maximum of
     * 7 days. Attempting to use an UploadSessionStartResult.session_id with
     * uploadSessionAppendV2() or uploadSessionFinish() more than 7 days after
     * its creation will return a UploadSessionLookupError.not_found. Calls to
     * this endpoint will count as data transport calls for any Dropbox Business
     * teams with a limit on the number of data transport calls allowed per
     * month. For more information, see the [Data transport limit page]{@link
     * https://www.dropbox.com/developers/reference/data-transport-limit}. By
     * default, upload sessions require you to send content of the file in
     * sequential order via consecutive uploadSessionStart(),
     * uploadSessionAppendV2(), uploadSessionFinish() calls. For better
     * performance, you can instead optionally use a
     * UploadSessionType.concurrent upload session. To start a new concurrent
     * session, set UploadSessionStartArg.session_type to
     * UploadSessionType.concurrent. After that, you can send file data in
     * concurrent uploadSessionAppendV2() requests. Finally finish the session
     * with uploadSessionFinish(). There are couple of constraints with
     * concurrent sessions to make them work. You can not send data with
     * uploadSessionStart() or uploadSessionFinish() call, only with
     * uploadSessionAppendV2() call. Also data uploaded in
     * uploadSessionAppendV2() call must be multiple of 4194304 bytes (except
     * for last uploadSessionAppendV2() with UploadSessionStartArg.close to
     * true, that may contain any remaining data).
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<files.UploadSessionStartError>.
     * @param arg The request parameters.
     */
    public filesUploadSessionStart(arg: files.UploadSessionStartArg): Promise<DropboxResponse<files.UploadSessionStartResult>>;

    /**
     * This route starts batch of upload_sessions. Please refer to
     * `upload_session/start` usage. Calls to this endpoint will count as data
     * transport calls for any Dropbox Business teams with a limit on the number
     * of data transport calls allowed per month. For more information, see the
     * [Data transport limit page]{@link
     * https://www.dropbox.com/developers/reference/data-transport-limit}.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @param arg The request parameters.
     */
    public filesUploadSessionStartBatch(arg: files.UploadSessionStartBatchArg): Promise<DropboxResponse<files.UploadSessionStartBatchResult>>;

    /**
     * This route is used for refreshing the info that is found in the id_token
     * during the OIDC flow. This route doesn't require any arguments and will
     * use the scopes approved for the given access token.
     *
     * Route attributes:
     *   scope: openid
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<openid.UserInfoError>.
     * @param arg The request parameters.
     */
    public openidUserinfo(arg: openid.UserInfoArgs): Promise<DropboxResponse<openid.UserInfoResult>>;

    /**
     * Marks the given Paper doc as archived. This action can be performed or
     * undone by anyone with edit permissions to the doc. Note that this
     * endpoint will continue to work for content created by users on the older
     * version of Paper. To check which version of Paper a user is on, use
     * /users/features/get_values. If the paper_as_files feature is enabled,
     * then the user is running the new version of Paper. This endpoint will be
     * retired in September 2020. Refer to the [Paper Migration Guide]{@link
     * https://www.dropbox.com/lp/developers/reference/paper-migration-guide}
     * for more information.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<paper.DocLookupError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public paperDocsArchive(arg: paper.RefPaperDoc): Promise<DropboxResponse<void>>;

    /**
     * Creates a new Paper doc with the provided content. Note that this
     * endpoint will continue to work for content created by users on the older
     * version of Paper. To check which version of Paper a user is on, use
     * /users/features/get_values. If the paper_as_files feature is enabled,
     * then the user is running the new version of Paper. This endpoint will be
     * retired in September 2020. Refer to the [Paper Migration Guide]{@link
     * https://www.dropbox.com/lp/developers/reference/paper-migration-guide}
     * for more information.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<paper.PaperDocCreateError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public paperDocsCreate(arg: paper.PaperDocCreateArgs): Promise<DropboxResponse<paper.PaperDocCreateUpdateResult>>;

    /**
     * Exports and downloads Paper doc either as HTML or markdown. Note that
     * this endpoint will continue to work for content created by users on the
     * older version of Paper. To check which version of Paper a user is on, use
     * /users/features/get_values. If the paper_as_files feature is enabled,
     * then the user is running the new version of Paper. Refer to the [Paper
     * Migration Guide]{@link
     * https://www.dropbox.com/lp/developers/reference/paper-migration-guide}
     * for migration information.
     *
     * Route attributes:
     *   scope: files.content.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<paper.DocLookupError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public paperDocsDownload(arg: paper.PaperDocExport): Promise<DropboxResponse<paper.PaperDocExportResult>>;

    /**
     * Lists the users who are explicitly invited to the Paper folder in which
     * the Paper doc is contained. For private folders all users (including
     * owner) shared on the folder are listed and for team folders all non-team
     * users shared on the folder are returned. Note that this endpoint will
     * continue to work for content created by users on the older version of
     * Paper. To check which version of Paper a user is on, use
     * /users/features/get_values. If the paper_as_files feature is enabled,
     * then the user is running the new version of Paper. Refer to the [Paper
     * Migration Guide]{@link
     * https://www.dropbox.com/lp/developers/reference/paper-migration-guide}
     * for migration information.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<paper.DocLookupError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public paperDocsFolderUsersList(arg: paper.ListUsersOnFolderArgs): Promise<DropboxResponse<paper.ListUsersOnFolderResponse>>;

    /**
     * Once a cursor has been retrieved from docsFolderUsersList(), use this to
     * paginate through all users on the Paper folder. Note that this endpoint
     * will continue to work for content created by users on the older version
     * of Paper. To check which version of Paper a user is on, use
     * /users/features/get_values. If the paper_as_files feature is enabled,
     * then the user is running the new version of Paper. Refer to the [Paper
     * Migration Guide]{@link
     * https://www.dropbox.com/lp/developers/reference/paper-migration-guide}
     * for migration information.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<paper.ListUsersCursorError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public paperDocsFolderUsersListContinue(arg: paper.ListUsersOnFolderContinueArgs): Promise<DropboxResponse<paper.ListUsersOnFolderResponse>>;

    /**
     * Retrieves folder information for the given Paper doc. This includes:   -
     * folder sharing policy; permissions for subfolders are set by the
     * top-level folder.   - full 'filepath', i.e. the list of folders (both
     * folderId and folderName) from     the root folder to the folder directly
     * containing the Paper doc.  If the Paper doc is not in any folder (aka
     * unfiled) the response will be empty. Note that this endpoint will
     * continue to work for content created by users on the older version of
     * Paper. To check which version of Paper a user is on, use
     * /users/features/get_values. If the paper_as_files feature is enabled,
     * then the user is running the new version of Paper. Refer to the [Paper
     * Migration Guide]{@link
     * https://www.dropbox.com/lp/developers/reference/paper-migration-guide}
     * for migration information.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<paper.DocLookupError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public paperDocsGetFolderInfo(arg: paper.RefPaperDoc): Promise<DropboxResponse<paper.FoldersContainingPaperDoc>>;

    /**
     * Return the list of all Paper docs according to the argument
     * specifications. To iterate over through the full pagination, pass the
     * cursor to docsListContinue(). Note that this endpoint will continue to
     * work for content created by users on the older version of Paper. To check
     * which version of Paper a user is on, use /users/features/get_values. If
     * the paper_as_files feature is enabled, then the user is running the new
     * version of Paper. Refer to the [Paper Migration Guide]{@link
     * https://www.dropbox.com/lp/developers/reference/paper-migration-guide}
     * for migration information.
     *
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @deprecated
     * @param arg The request parameters.
     */
    public paperDocsList(arg: paper.ListPaperDocsArgs): Promise<DropboxResponse<paper.ListPaperDocsResponse>>;

    /**
     * Once a cursor has been retrieved from docsList(), use this to paginate
     * through all Paper doc. Note that this endpoint will continue to work for
     * content created by users on the older version of Paper. To check which
     * version of Paper a user is on, use /users/features/get_values. If the
     * paper_as_files feature is enabled, then the user is running the new
     * version of Paper. Refer to the [Paper Migration Guide]{@link
     * https://www.dropbox.com/lp/developers/reference/paper-migration-guide}
     * for migration information.
     *
     * Route attributes:
     *   scope: files.metadata.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<paper.ListDocsCursorError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public paperDocsListContinue(arg: paper.ListPaperDocsContinueArgs): Promise<DropboxResponse<paper.ListPaperDocsResponse>>;

    /**
     * Permanently deletes the given Paper doc. This operation is final as the
     * doc cannot be recovered. This action can be performed only by the doc
     * owner. Note that this endpoint will continue to work for content created
     * by users on the older version of Paper. To check which version of Paper a
     * user is on, use /users/features/get_values. If the paper_as_files feature
     * is enabled, then the user is running the new version of Paper. Refer to
     * the [Paper Migration Guide]{@link
     * https://www.dropbox.com/lp/developers/reference/paper-migration-guide}
     * for migration information.
     *
     * Route attributes:
     *   scope: files.permanent_delete
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<paper.DocLookupError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public paperDocsPermanentlyDelete(arg: paper.RefPaperDoc): Promise<DropboxResponse<void>>;

    /**
     * Gets the default sharing policy for the given Paper doc. Note that this
     * endpoint will continue to work for content created by users on the older
     * version of Paper. To check which version of Paper a user is on, use
     * /users/features/get_values. If the paper_as_files feature is enabled,
     * then the user is running the new version of Paper. Refer to the [Paper
     * Migration Guide]{@link
     * https://www.dropbox.com/lp/developers/reference/paper-migration-guide}
     * for migration information.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<paper.DocLookupError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public paperDocsSharingPolicyGet(arg: paper.RefPaperDoc): Promise<DropboxResponse<paper.SharingPolicy>>;

    /**
     * Sets the default sharing policy for the given Paper doc. The default
     * 'team_sharing_policy' can be changed only by teams, omit this field for
     * personal accounts. The 'public_sharing_policy' policy can't be set to the
     * value 'disabled' because this setting can be changed only via the team
     * admin console. Note that this endpoint will continue to work for content
     * created by users on the older version of Paper. To check which version of
     * Paper a user is on, use /users/features/get_values. If the paper_as_files
     * feature is enabled, then the user is running the new version of Paper.
     * Refer to the [Paper Migration Guide]{@link
     * https://www.dropbox.com/lp/developers/reference/paper-migration-guide}
     * for migration information.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<paper.DocLookupError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public paperDocsSharingPolicySet(arg: paper.PaperDocSharingPolicy): Promise<DropboxResponse<void>>;

    /**
     * Updates an existing Paper doc with the provided content. Note that this
     * endpoint will continue to work for content created by users on the older
     * version of Paper. To check which version of Paper a user is on, use
     * /users/features/get_values. If the paper_as_files feature is enabled,
     * then the user is running the new version of Paper. This endpoint will be
     * retired in September 2020. Refer to the [Paper Migration Guide]{@link
     * https://www.dropbox.com/lp/developers/reference/paper-migration-guide}
     * for more information.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<paper.PaperDocUpdateError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public paperDocsUpdate(arg: paper.PaperDocUpdateArgs): Promise<DropboxResponse<paper.PaperDocCreateUpdateResult>>;

    /**
     * Allows an owner or editor to add users to a Paper doc or change their
     * permissions using their email address or Dropbox account ID. The doc
     * owner's permissions cannot be changed. Note that this endpoint will
     * continue to work for content created by users on the older version of
     * Paper. To check which version of Paper a user is on, use
     * /users/features/get_values. If the paper_as_files feature is enabled,
     * then the user is running the new version of Paper. Refer to the [Paper
     * Migration Guide]{@link
     * https://www.dropbox.com/lp/developers/reference/paper-migration-guide}
     * for migration information.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<paper.DocLookupError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public paperDocsUsersAdd(arg: paper.AddPaperDocUser): Promise<DropboxResponse<Array<paper.AddPaperDocUserMemberResult>>>;

    /**
     * Lists all users who visited the Paper doc or users with explicit access.
     * This call excludes users who have been removed. The list is sorted by the
     * date of the visit or the share date. The list will include both users,
     * the explicitly shared ones as well as those who came in using the Paper
     * url link. Note that this endpoint will continue to work for content
     * created by users on the older version of Paper. To check which version of
     * Paper a user is on, use /users/features/get_values. If the paper_as_files
     * feature is enabled, then the user is running the new version of Paper.
     * Refer to the [Paper Migration Guide]{@link
     * https://www.dropbox.com/lp/developers/reference/paper-migration-guide}
     * for migration information.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<paper.DocLookupError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public paperDocsUsersList(arg: paper.ListUsersOnPaperDocArgs): Promise<DropboxResponse<paper.ListUsersOnPaperDocResponse>>;

    /**
     * Once a cursor has been retrieved from docsUsersList(), use this to
     * paginate through all users on the Paper doc. Note that this endpoint will
     * continue to work for content created by users on the older version of
     * Paper. To check which version of Paper a user is on, use
     * /users/features/get_values. If the paper_as_files feature is enabled,
     * then the user is running the new version of Paper. Refer to the [Paper
     * Migration Guide]{@link
     * https://www.dropbox.com/lp/developers/reference/paper-migration-guide}
     * for migration information.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<paper.ListUsersCursorError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public paperDocsUsersListContinue(arg: paper.ListUsersOnPaperDocContinueArgs): Promise<DropboxResponse<paper.ListUsersOnPaperDocResponse>>;

    /**
     * Allows an owner or editor to remove users from a Paper doc using their
     * email address or Dropbox account ID. The doc owner cannot be removed.
     * Note that this endpoint will continue to work for content created by
     * users on the older version of Paper. To check which version of Paper a
     * user is on, use /users/features/get_values. If the paper_as_files feature
     * is enabled, then the user is running the new version of Paper. Refer to
     * the [Paper Migration Guide]{@link
     * https://www.dropbox.com/lp/developers/reference/paper-migration-guide}
     * for migration information.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<paper.DocLookupError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public paperDocsUsersRemove(arg: paper.RemovePaperDocUser): Promise<DropboxResponse<void>>;

    /**
     * Create a new Paper folder with the provided info. Note that this endpoint
     * will continue to work for content created by users on the older version
     * of Paper. To check which version of Paper a user is on, use
     * /users/features/get_values. If the paper_as_files feature is enabled,
     * then the user is running the new version of Paper. Refer to the [Paper
     * Migration Guide]{@link
     * https://www.dropbox.com/lp/developers/reference/paper-migration-guide}
     * for migration information.
     *
     * Route attributes:
     *   scope: files.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<paper.PaperFolderCreateError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public paperFoldersCreate(arg: paper.PaperFolderCreateArg): Promise<DropboxResponse<paper.PaperFolderCreateResult>>;

    /**
     * Adds specified members to a file.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.AddFileMemberError>.
     * @param arg The request parameters.
     */
    public sharingAddFileMember(arg: sharing.AddFileMemberArgs): Promise<DropboxResponse<Array<sharing.FileMemberActionResult>>>;

    /**
     * Allows an owner or editor (if the ACL update policy allows) of a shared
     * folder to add another member. For the new member to get access to all the
     * functionality for this folder, you will need to call mountFolder() on
     * their behalf.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.AddFolderMemberError>.
     * @param arg The request parameters.
     */
    public sharingAddFolderMember(arg: sharing.AddFolderMemberArg): Promise<DropboxResponse<void>>;

    /**
     * Returns the status of an asynchronous job.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<async.PollError>.
     * @param arg The request parameters.
     */
    public sharingCheckJobStatus(arg: async.PollArg): Promise<DropboxResponse<sharing.JobStatus>>;

    /**
     * Returns the status of an asynchronous job for sharing a folder.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<async.PollError>.
     * @param arg The request parameters.
     */
    public sharingCheckRemoveMemberJobStatus(arg: async.PollArg): Promise<DropboxResponse<sharing.RemoveMemberJobStatus>>;

    /**
     * Returns the status of an asynchronous job for sharing a folder.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<async.PollError>.
     * @param arg The request parameters.
     */
    public sharingCheckShareJobStatus(arg: async.PollArg): Promise<DropboxResponse<sharing.ShareFolderJobStatus>>;

    /**
     * Create a shared link. If a shared link already exists for the given path,
     * that link is returned. Previously, it was technically possible to break a
     * shared link by moving or renaming the corresponding file or folder. In
     * the future, this will no longer be the case, so your app shouldn't rely
     * on this behavior. Instead, if your app needs to revoke a shared link, use
     * revokeSharedLink().
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.CreateSharedLinkError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public sharingCreateSharedLink(arg: sharing.CreateSharedLinkArg): Promise<DropboxResponse<sharing.PathLinkMetadata>>;

    /**
     * Create a shared link with custom settings. If no settings are given then
     * the default visibility is RequestedVisibility.public (The resolved
     * visibility, though, may depend on other aspects such as team and shared
     * folder settings).
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.CreateSharedLinkWithSettingsError>.
     * @param arg The request parameters.
     */
    public sharingCreateSharedLinkWithSettings(arg: sharing.CreateSharedLinkWithSettingsArg): Promise<DropboxResponse<sharing.FileLinkMetadataReference|sharing.FolderLinkMetadataReference|sharing.SharedLinkMetadataReference>>;

    /**
     * Returns shared file metadata.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.GetFileMetadataError>.
     * @param arg The request parameters.
     */
    public sharingGetFileMetadata(arg: sharing.GetFileMetadataArg): Promise<DropboxResponse<sharing.SharedFileMetadata>>;

    /**
     * Returns shared file metadata.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.SharingUserError>.
     * @param arg The request parameters.
     */
    public sharingGetFileMetadataBatch(arg: sharing.GetFileMetadataBatchArg): Promise<DropboxResponse<Array<sharing.GetFileMetadataBatchResult>>>;

    /**
     * Returns shared folder metadata by its folder ID.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.SharedFolderAccessError>.
     * @param arg The request parameters.
     */
    public sharingGetFolderMetadata(arg: sharing.GetMetadataArgs): Promise<DropboxResponse<sharing.SharedFolderMetadata>>;

    /**
     * Download the shared link's file from a user's Dropbox.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.GetSharedLinkFileError>.
     * @param arg The request parameters.
     */
    public sharingGetSharedLinkFile(arg: sharing.GetSharedLinkFileArg): Promise<DropboxResponse<sharing.FileLinkMetadataReference|sharing.FolderLinkMetadataReference|sharing.SharedLinkMetadataReference>>;

    /**
     * Get the shared link's metadata.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.SharedLinkError>.
     * @param arg The request parameters.
     */
    public sharingGetSharedLinkMetadata(arg: sharing.GetSharedLinkMetadataArg): Promise<DropboxResponse<sharing.FileLinkMetadataReference|sharing.FolderLinkMetadataReference|sharing.SharedLinkMetadataReference>>;

    /**
     * Returns a list of LinkMetadata objects for this user, including
     * collection links. If no path is given, returns a list of all shared links
     * for the current user, including collection links, up to a maximum of 1000
     * links. If a non-empty path is given, returns a list of all shared links
     * that allow access to the given path.  Collection links are never returned
     * in this case.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.GetSharedLinksError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public sharingGetSharedLinks(arg: sharing.GetSharedLinksArg): Promise<DropboxResponse<sharing.GetSharedLinksResult>>;

    /**
     * Use to obtain the members who have been invited to a file, both inherited
     * and uninherited members.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.ListFileMembersError>.
     * @param arg The request parameters.
     */
    public sharingListFileMembers(arg: sharing.ListFileMembersArg): Promise<DropboxResponse<sharing.SharedFileMembers>>;

    /**
     * Get members of multiple files at once. The arguments to this route are
     * more limited, and the limit on query result size per file is more strict.
     * To customize the results more, use the individual file endpoint.
     * Inherited users and groups are not included in the result, and
     * permissions are not returned for this endpoint.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.SharingUserError>.
     * @param arg The request parameters.
     */
    public sharingListFileMembersBatch(arg: sharing.ListFileMembersBatchArg): Promise<DropboxResponse<Array<sharing.ListFileMembersBatchResult>>>;

    /**
     * Once a cursor has been retrieved from listFileMembers() or
     * listFileMembersBatch(), use this to paginate through all shared file
     * members.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.ListFileMembersContinueError>.
     * @param arg The request parameters.
     */
    public sharingListFileMembersContinue(arg: sharing.ListFileMembersContinueArg): Promise<DropboxResponse<sharing.SharedFileMembers>>;

    /**
     * Returns shared folder membership by its folder ID.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.SharedFolderAccessError>.
     * @param arg The request parameters.
     */
    public sharingListFolderMembers(arg: sharing.ListFolderMembersArgs): Promise<DropboxResponse<sharing.SharedFolderMembers>>;

    /**
     * Once a cursor has been retrieved from listFolderMembers(), use this to
     * paginate through all shared folder members.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.ListFolderMembersContinueError>.
     * @param arg The request parameters.
     */
    public sharingListFolderMembersContinue(arg: sharing.ListFolderMembersContinueArg): Promise<DropboxResponse<sharing.SharedFolderMembers>>;

    /**
     * Return the list of all shared folders the current user has access to.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @param arg The request parameters.
     */
    public sharingListFolders(arg: sharing.ListFoldersArgs): Promise<DropboxResponse<sharing.ListFoldersResult>>;

    /**
     * Once a cursor has been retrieved from listFolders(), use this to paginate
     * through all shared folders. The cursor must come from a previous call to
     * listFolders() or listFoldersContinue().
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.ListFoldersContinueError>.
     * @param arg The request parameters.
     */
    public sharingListFoldersContinue(arg: sharing.ListFoldersContinueArg): Promise<DropboxResponse<sharing.ListFoldersResult>>;

    /**
     * Return the list of all shared folders the current user can mount or
     * unmount.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @param arg The request parameters.
     */
    public sharingListMountableFolders(arg: sharing.ListFoldersArgs): Promise<DropboxResponse<sharing.ListFoldersResult>>;

    /**
     * Once a cursor has been retrieved from listMountableFolders(), use this to
     * paginate through all mountable shared folders. The cursor must come from
     * a previous call to listMountableFolders() or
     * listMountableFoldersContinue().
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.ListFoldersContinueError>.
     * @param arg The request parameters.
     */
    public sharingListMountableFoldersContinue(arg: sharing.ListFoldersContinueArg): Promise<DropboxResponse<sharing.ListFoldersResult>>;

    /**
     * Returns a list of all files shared with current user.  Does not include
     * files the user has received via shared folders, and does  not include
     * unclaimed invitations.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.SharingUserError>.
     * @param arg The request parameters.
     */
    public sharingListReceivedFiles(arg: sharing.ListFilesArg): Promise<DropboxResponse<sharing.ListFilesResult>>;

    /**
     * Get more results with a cursor from listReceivedFiles().
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.ListFilesContinueError>.
     * @param arg The request parameters.
     */
    public sharingListReceivedFilesContinue(arg: sharing.ListFilesContinueArg): Promise<DropboxResponse<sharing.ListFilesResult>>;

    /**
     * List shared links of this user. If no path is given, returns a list of
     * all shared links for the current user. For members of business teams
     * using team space and member folders, returns all shared links in the team
     * member's home folder unless the team space ID is specified in the request
     * header. For more information, refer to the [Namespace Guide]{@link
     * https://www.dropbox.com/developers/reference/namespace-guide}. If a
     * non-empty path is given, returns a list of all shared links that allow
     * access to the given path - direct links to the given path and links to
     * parent folders of the given path. Links to parent folders can be
     * suppressed by setting direct_only to true.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.ListSharedLinksError>.
     * @param arg The request parameters.
     */
    public sharingListSharedLinks(arg: sharing.ListSharedLinksArg): Promise<DropboxResponse<sharing.ListSharedLinksResult>>;

    /**
     * Modify the shared link's settings. If the requested visibility conflict
     * with the shared links policy of the team or the shared folder (in case
     * the linked file is part of a shared folder) then the
     * LinkPermissions.resolved_visibility of the returned SharedLinkMetadata
     * will reflect the actual visibility of the shared link and the
     * LinkPermissions.requested_visibility will reflect the requested
     * visibility.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.ModifySharedLinkSettingsError>.
     * @param arg The request parameters.
     */
    public sharingModifySharedLinkSettings(arg: sharing.ModifySharedLinkSettingsArgs): Promise<DropboxResponse<sharing.FileLinkMetadataReference|sharing.FolderLinkMetadataReference|sharing.SharedLinkMetadataReference>>;

    /**
     * The current user mounts the designated folder. Mount a shared folder for
     * a user after they have been added as a member. Once mounted, the shared
     * folder will appear in their Dropbox.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.MountFolderError>.
     * @param arg The request parameters.
     */
    public sharingMountFolder(arg: sharing.MountFolderArg): Promise<DropboxResponse<sharing.SharedFolderMetadata>>;

    /**
     * The current user relinquishes their membership in the designated file.
     * Note that the current user may still have inherited access to this file
     * through the parent folder.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.RelinquishFileMembershipError>.
     * @param arg The request parameters.
     */
    public sharingRelinquishFileMembership(arg: sharing.RelinquishFileMembershipArg): Promise<DropboxResponse<void>>;

    /**
     * The current user relinquishes their membership in the designated shared
     * folder and will no longer have access to the folder.  A folder owner
     * cannot relinquish membership in their own folder. This will run
     * synchronously if leave_a_copy is false, and asynchronously if
     * leave_a_copy is true.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.RelinquishFolderMembershipError>.
     * @param arg The request parameters.
     */
    public sharingRelinquishFolderMembership(arg: sharing.RelinquishFolderMembershipArg): Promise<DropboxResponse<async.LaunchEmptyResult>>;

    /**
     * Identical to remove_file_member_2 but with less information returned.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.RemoveFileMemberError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public sharingRemoveFileMember(arg: sharing.RemoveFileMemberArg): Promise<DropboxResponse<sharing.FileMemberActionIndividualResult>>;

    /**
     * Removes a specified member from the file.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.RemoveFileMemberError>.
     * @param arg The request parameters.
     */
    public sharingRemoveFileMember2(arg: sharing.RemoveFileMemberArg): Promise<DropboxResponse<sharing.FileMemberRemoveActionResult>>;

    /**
     * Allows an owner or editor (if the ACL update policy allows) of a shared
     * folder to remove another member.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.RemoveFolderMemberError>.
     * @param arg The request parameters.
     */
    public sharingRemoveFolderMember(arg: sharing.RemoveFolderMemberArg): Promise<DropboxResponse<async.LaunchResultBase>>;

    /**
     * Revoke a shared link. Note that even after revoking a shared link to a
     * file, the file may be accessible if there are shared links leading to any
     * of the file parent folders. To list all shared links that enable access
     * to a specific file, you can use the listSharedLinks() with the file as
     * the ListSharedLinksArg.path argument.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.RevokeSharedLinkError>.
     * @param arg The request parameters.
     */
    public sharingRevokeSharedLink(arg: sharing.RevokeSharedLinkArg): Promise<DropboxResponse<void>>;

    /**
     * Change the inheritance policy of an existing Shared Folder. Only
     * permitted for shared folders in a shared team root. If a
     * ShareFolderLaunch.async_job_id is returned, you'll need to call
     * checkShareJobStatus() until the action completes to get the metadata for
     * the folder.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.SetAccessInheritanceError>.
     * @param arg The request parameters.
     */
    public sharingSetAccessInheritance(arg: sharing.SetAccessInheritanceArg): Promise<DropboxResponse<sharing.ShareFolderLaunch>>;

    /**
     * Share a folder with collaborators. Most sharing will be completed
     * synchronously. Large folders will be completed asynchronously. To make
     * testing the async case repeatable, set `ShareFolderArg.force_async`. If a
     * ShareFolderLaunch.async_job_id is returned, you'll need to call
     * checkShareJobStatus() until the action completes to get the metadata for
     * the folder.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.ShareFolderError>.
     * @param arg The request parameters.
     */
    public sharingShareFolder(arg: sharing.ShareFolderArg): Promise<DropboxResponse<sharing.ShareFolderLaunch>>;

    /**
     * Transfer ownership of a shared folder to a member of the shared folder.
     * User must have AccessLevel.owner access to the shared folder to perform a
     * transfer.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.TransferFolderError>.
     * @param arg The request parameters.
     */
    public sharingTransferFolder(arg: sharing.TransferFolderArg): Promise<DropboxResponse<void>>;

    /**
     * The current user unmounts the designated folder. They can re-mount the
     * folder at a later time using mountFolder().
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.UnmountFolderError>.
     * @param arg The request parameters.
     */
    public sharingUnmountFolder(arg: sharing.UnmountFolderArg): Promise<DropboxResponse<void>>;

    /**
     * Remove all members from this file. Does not remove inherited members.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.UnshareFileError>.
     * @param arg The request parameters.
     */
    public sharingUnshareFile(arg: sharing.UnshareFileArg): Promise<DropboxResponse<void>>;

    /**
     * Allows a shared folder owner to unshare the folder. You'll need to call
     * checkJobStatus() to determine if the action has completed successfully.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.UnshareFolderError>.
     * @param arg The request parameters.
     */
    public sharingUnshareFolder(arg: sharing.UnshareFolderArg): Promise<DropboxResponse<async.LaunchEmptyResult>>;

    /**
     * Changes a member's access on a shared file.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.FileMemberActionError>.
     * @param arg The request parameters.
     */
    public sharingUpdateFileMember(arg: sharing.UpdateFileMemberArgs): Promise<DropboxResponse<sharing.MemberAccessLevelResult>>;

    /**
     * Allows an owner or editor of a shared folder to update another member's
     * permissions.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.UpdateFolderMemberError>.
     * @param arg The request parameters.
     */
    public sharingUpdateFolderMember(arg: sharing.UpdateFolderMemberArg): Promise<DropboxResponse<sharing.MemberAccessLevelResult>>;

    /**
     * Update the sharing policies for a shared folder. User must have
     * AccessLevel.owner access to the shared folder to update its policies.
     *
     * Route attributes:
     *   scope: sharing.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<sharing.UpdateFolderPolicyError>.
     * @param arg The request parameters.
     */
    public sharingUpdateFolderPolicy(arg: sharing.UpdateFolderPolicyArg): Promise<DropboxResponse<sharing.SharedFolderMetadata>>;

    /**
     * List all device sessions of a team's member.
     *
     * Route attributes:
     *   scope: sessions.list
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.ListMemberDevicesError>.
     * @param arg The request parameters.
     */
    public teamDevicesListMemberDevices(arg: team.ListMemberDevicesArg): Promise<DropboxResponse<team.ListMemberDevicesResult>>;

    /**
     * List all device sessions of a team. Permission : Team member file access.
     *
     * Route attributes:
     *   scope: sessions.list
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.ListMembersDevicesError>.
     * @param arg The request parameters.
     */
    public teamDevicesListMembersDevices(arg: team.ListMembersDevicesArg): Promise<DropboxResponse<team.ListMembersDevicesResult>>;

    /**
     * List all device sessions of a team. Permission : Team member file access.
     *
     * Route attributes:
     *   scope: sessions.list
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.ListTeamDevicesError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public teamDevicesListTeamDevices(arg: team.ListTeamDevicesArg): Promise<DropboxResponse<team.ListTeamDevicesResult>>;

    /**
     * Revoke a device session of a team's member.
     *
     * Route attributes:
     *   scope: sessions.modify
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.RevokeDeviceSessionError>.
     * @param arg The request parameters.
     */
    public teamDevicesRevokeDeviceSession(arg: team.RevokeDeviceSessionArg): Promise<DropboxResponse<void>>;

    /**
     * Revoke a list of device sessions of team members.
     *
     * Route attributes:
     *   scope: sessions.modify
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.RevokeDeviceSessionBatchError>.
     * @param arg The request parameters.
     */
    public teamDevicesRevokeDeviceSessionBatch(arg: team.RevokeDeviceSessionBatchArg): Promise<DropboxResponse<team.RevokeDeviceSessionBatchResult>>;

    /**
     * Get the values for one or more featues. This route allows you to check
     * your account's capability for what feature you can access or what value
     * you have for certain features. Permission : Team information.
     *
     * Route attributes:
     *   scope: team_info.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.FeaturesGetValuesBatchError>.
     * @param arg The request parameters.
     */
    public teamFeaturesGetValues(arg: team.FeaturesGetValuesBatchArg): Promise<DropboxResponse<team.FeaturesGetValuesBatchResult>>;

    /**
     * Retrieves information about a team.
     *
     * Route attributes:
     *   scope: team_info.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     */
    public teamGetInfo(): Promise<DropboxResponse<team.TeamGetInfoResult>>;

    /**
     * Creates a new, empty group, with a requested name. Permission : Team
     * member management.
     *
     * Route attributes:
     *   scope: groups.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.GroupCreateError>.
     * @param arg The request parameters.
     */
    public teamGroupsCreate(arg: team.GroupCreateArg): Promise<DropboxResponse<team.GroupFullInfo>>;

    /**
     * Deletes a group. The group is deleted immediately. However the revoking
     * of group-owned resources may take additional time. Use the
     * groupsJobStatusGet() to determine whether this process has completed.
     * Permission : Team member management.
     *
     * Route attributes:
     *   scope: groups.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.GroupDeleteError>.
     * @param arg The request parameters.
     */
    public teamGroupsDelete(arg: team.GroupSelector): Promise<DropboxResponse<async.LaunchEmptyResult>>;

    /**
     * Retrieves information about one or more groups. Note that the optional
     * field  GroupFullInfo.members is not returned for system-managed groups.
     * Permission : Team Information.
     *
     * Route attributes:
     *   scope: groups.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.GroupsGetInfoError>.
     * @param arg The request parameters.
     */
    public teamGroupsGetInfo(arg: team.GroupsSelector): Promise<DropboxResponse<team.GroupsGetInfoResult>>;

    /**
     * Once an async_job_id is returned from groupsDelete(), groupsMembersAdd()
     * , or groupsMembersRemove() use this method to poll the status of
     * granting/revoking group members' access to group-owned resources.
     * Permission : Team member management.
     *
     * Route attributes:
     *   scope: groups.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.GroupsPollError>.
     * @param arg The request parameters.
     */
    public teamGroupsJobStatusGet(arg: async.PollArg): Promise<DropboxResponse<async.PollEmptyResult>>;

    /**
     * Lists groups on a team. Permission : Team Information.
     *
     * Route attributes:
     *   scope: groups.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @param arg The request parameters.
     */
    public teamGroupsList(arg: team.GroupsListArg): Promise<DropboxResponse<team.GroupsListResult>>;

    /**
     * Once a cursor has been retrieved from groupsList(), use this to paginate
     * through all groups. Permission : Team Information.
     *
     * Route attributes:
     *   scope: groups.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.GroupsListContinueError>.
     * @param arg The request parameters.
     */
    public teamGroupsListContinue(arg: team.GroupsListContinueArg): Promise<DropboxResponse<team.GroupsListResult>>;

    /**
     * Adds members to a group. The members are added immediately. However the
     * granting of group-owned resources may take additional time. Use the
     * groupsJobStatusGet() to determine whether this process has completed.
     * Permission : Team member management.
     *
     * Route attributes:
     *   scope: groups.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.GroupMembersAddError>.
     * @param arg The request parameters.
     */
    public teamGroupsMembersAdd(arg: team.GroupMembersAddArg): Promise<DropboxResponse<team.GroupMembersChangeResult>>;

    /**
     * Lists members of a group. Permission : Team Information.
     *
     * Route attributes:
     *   scope: groups.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.GroupSelectorError>.
     * @param arg The request parameters.
     */
    public teamGroupsMembersList(arg: team.GroupsMembersListArg): Promise<DropboxResponse<team.GroupsMembersListResult>>;

    /**
     * Once a cursor has been retrieved from groupsMembersList(), use this to
     * paginate through all members of the group. Permission : Team information.
     *
     * Route attributes:
     *   scope: groups.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.GroupsMembersListContinueError>.
     * @param arg The request parameters.
     */
    public teamGroupsMembersListContinue(arg: team.GroupsMembersListContinueArg): Promise<DropboxResponse<team.GroupsMembersListResult>>;

    /**
     * Removes members from a group. The members are removed immediately.
     * However the revoking of group-owned resources may take additional time.
     * Use the groupsJobStatusGet() to determine whether this process has
     * completed. This method permits removing the only owner of a group, even
     * in cases where this is not possible via the web client. Permission : Team
     * member management.
     *
     * Route attributes:
     *   scope: groups.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.GroupMembersRemoveError>.
     * @param arg The request parameters.
     */
    public teamGroupsMembersRemove(arg: team.GroupMembersRemoveArg): Promise<DropboxResponse<team.GroupMembersChangeResult>>;

    /**
     * Sets a member's access type in a group. Permission : Team member
     * management.
     *
     * Route attributes:
     *   scope: groups.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.GroupMemberSetAccessTypeError>.
     * @param arg The request parameters.
     */
    public teamGroupsMembersSetAccessType(arg: team.GroupMembersSetAccessTypeArg): Promise<DropboxResponse<team.GroupsGetInfoResult>>;

    /**
     * Updates a group's name and/or external ID. Permission : Team member
     * management.
     *
     * Route attributes:
     *   scope: groups.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.GroupUpdateError>.
     * @param arg The request parameters.
     */
    public teamGroupsUpdate(arg: team.GroupUpdateArgs): Promise<DropboxResponse<team.GroupFullInfo>>;

    /**
     * Creates new legal hold policy. Note: Legal Holds is a paid add-on. Not
     * all teams have the feature. Permission : Team member file access.
     *
     * Route attributes:
     *   scope: team_data.governance.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.LegalHoldsPolicyCreateError>.
     * @param arg The request parameters.
     */
    public teamLegalHoldsCreatePolicy(arg: team.LegalHoldsPolicyCreateArg): Promise<DropboxResponse<team.LegalHoldsPolicyCreateResult>>;

    /**
     * Gets a legal hold by Id. Note: Legal Holds is a paid add-on. Not all
     * teams have the feature. Permission : Team member file access.
     *
     * Route attributes:
     *   scope: team_data.governance.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.LegalHoldsGetPolicyError>.
     * @param arg The request parameters.
     */
    public teamLegalHoldsGetPolicy(arg: team.LegalHoldsGetPolicyArg): Promise<DropboxResponse<team.LegalHoldsGetPolicyResult>>;

    /**
     * List the file metadata that's under the hold. Note: Legal Holds is a paid
     * add-on. Not all teams have the feature. Permission : Team member file
     * access.
     *
     * Route attributes:
     *   scope: team_data.governance.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.LegalHoldsListHeldRevisionsError>.
     * @param arg The request parameters.
     */
    public teamLegalHoldsListHeldRevisions(arg: team.LegalHoldsListHeldRevisionsArg): Promise<DropboxResponse<team.LegalHoldsListHeldRevisionResult>>;

    /**
     * Continue listing the file metadata that's under the hold. Note: Legal
     * Holds is a paid add-on. Not all teams have the feature. Permission : Team
     * member file access.
     *
     * Route attributes:
     *   scope: team_data.governance.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.LegalHoldsListHeldRevisionsError>.
     * @param arg The request parameters.
     */
    public teamLegalHoldsListHeldRevisionsContinue(arg: team.LegalHoldsListHeldRevisionsContinueArg): Promise<DropboxResponse<team.LegalHoldsListHeldRevisionResult>>;

    /**
     * Lists legal holds on a team. Note: Legal Holds is a paid add-on. Not all
     * teams have the feature. Permission : Team member file access.
     *
     * Route attributes:
     *   scope: team_data.governance.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.LegalHoldsListPoliciesError>.
     * @param arg The request parameters.
     */
    public teamLegalHoldsListPolicies(arg: team.LegalHoldsListPoliciesArg): Promise<DropboxResponse<team.LegalHoldsListPoliciesResult>>;

    /**
     * Releases a legal hold by Id. Note: Legal Holds is a paid add-on. Not all
     * teams have the feature. Permission : Team member file access.
     *
     * Route attributes:
     *   scope: team_data.governance.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.LegalHoldsPolicyReleaseError>.
     * @param arg The request parameters.
     */
    public teamLegalHoldsReleasePolicy(arg: team.LegalHoldsPolicyReleaseArg): Promise<DropboxResponse<void>>;

    /**
     * Updates a legal hold. Note: Legal Holds is a paid add-on. Not all teams
     * have the feature. Permission : Team member file access.
     *
     * Route attributes:
     *   scope: team_data.governance.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.LegalHoldsPolicyUpdateError>.
     * @param arg The request parameters.
     */
    public teamLegalHoldsUpdatePolicy(arg: team.LegalHoldsPolicyUpdateArg): Promise<DropboxResponse<team.LegalHoldsPolicyUpdateResult>>;

    /**
     * List all linked applications of the team member. Note, this endpoint does
     * not list any team-linked applications.
     *
     * Route attributes:
     *   scope: sessions.list
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.ListMemberAppsError>.
     * @param arg The request parameters.
     */
    public teamLinkedAppsListMemberLinkedApps(arg: team.ListMemberAppsArg): Promise<DropboxResponse<team.ListMemberAppsResult>>;

    /**
     * List all applications linked to the team members' accounts. Note, this
     * endpoint does not list any team-linked applications.
     *
     * Route attributes:
     *   scope: sessions.list
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.ListMembersAppsError>.
     * @param arg The request parameters.
     */
    public teamLinkedAppsListMembersLinkedApps(arg: team.ListMembersAppsArg): Promise<DropboxResponse<team.ListMembersAppsResult>>;

    /**
     * List all applications linked to the team members' accounts. Note, this
     * endpoint doesn't list any team-linked applications.
     *
     * Route attributes:
     *   scope: sessions.list
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.ListTeamAppsError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public teamLinkedAppsListTeamLinkedApps(arg: team.ListTeamAppsArg): Promise<DropboxResponse<team.ListTeamAppsResult>>;

    /**
     * Revoke a linked application of the team member.
     *
     * Route attributes:
     *   scope: sessions.modify
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.RevokeLinkedAppError>.
     * @param arg The request parameters.
     */
    public teamLinkedAppsRevokeLinkedApp(arg: team.RevokeLinkedApiAppArg): Promise<DropboxResponse<void>>;

    /**
     * Revoke a list of linked applications of the team members.
     *
     * Route attributes:
     *   scope: sessions.modify
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.RevokeLinkedAppBatchError>.
     * @param arg The request parameters.
     */
    public teamLinkedAppsRevokeLinkedAppBatch(arg: team.RevokeLinkedApiAppBatchArg): Promise<DropboxResponse<team.RevokeLinkedAppBatchResult>>;

    /**
     * Add users to member space limits excluded users list.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.ExcludedUsersUpdateError>.
     * @param arg The request parameters.
     */
    public teamMemberSpaceLimitsExcludedUsersAdd(arg: team.ExcludedUsersUpdateArg): Promise<DropboxResponse<team.ExcludedUsersUpdateResult>>;

    /**
     * List member space limits excluded users.
     *
     * Route attributes:
     *   scope: members.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.ExcludedUsersListError>.
     * @param arg The request parameters.
     */
    public teamMemberSpaceLimitsExcludedUsersList(arg: team.ExcludedUsersListArg): Promise<DropboxResponse<team.ExcludedUsersListResult>>;

    /**
     * Continue listing member space limits excluded users.
     *
     * Route attributes:
     *   scope: members.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.ExcludedUsersListContinueError>.
     * @param arg The request parameters.
     */
    public teamMemberSpaceLimitsExcludedUsersListContinue(arg: team.ExcludedUsersListContinueArg): Promise<DropboxResponse<team.ExcludedUsersListResult>>;

    /**
     * Remove users from member space limits excluded users list.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.ExcludedUsersUpdateError>.
     * @param arg The request parameters.
     */
    public teamMemberSpaceLimitsExcludedUsersRemove(arg: team.ExcludedUsersUpdateArg): Promise<DropboxResponse<team.ExcludedUsersUpdateResult>>;

    /**
     * Get users custom quota. Returns none as the custom quota if none was set.
     * A maximum of 1000 members can be specified in a single call.
     *
     * Route attributes:
     *   scope: members.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.CustomQuotaError>.
     * @param arg The request parameters.
     */
    public teamMemberSpaceLimitsGetCustomQuota(arg: team.CustomQuotaUsersArg): Promise<DropboxResponse<Array<team.CustomQuotaResult>>>;

    /**
     * Remove users custom quota. A maximum of 1000 members can be specified in
     * a single call.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.CustomQuotaError>.
     * @param arg The request parameters.
     */
    public teamMemberSpaceLimitsRemoveCustomQuota(arg: team.CustomQuotaUsersArg): Promise<DropboxResponse<Array<team.RemoveCustomQuotaResult>>>;

    /**
     * Set users custom quota. Custom quota has to be at least 15GB. A maximum
     * of 1000 members can be specified in a single call.
     *
     * Route attributes:
     *   scope: members.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.SetCustomQuotaError>.
     * @param arg The request parameters.
     */
    public teamMemberSpaceLimitsSetCustomQuota(arg: team.SetCustomQuotaArg): Promise<DropboxResponse<Array<team.CustomQuotaResult>>>;

    /**
     * Adds members to a team. Permission : Team member management A maximum of
     * 20 members can be specified in a single call. If no Dropbox account
     * exists with the email address specified, a new Dropbox account will be
     * created with the given email address, and that account will be invited to
     * the team. If a personal Dropbox account exists with the email address
     * specified in the call, this call will create a placeholder Dropbox
     * account for the user on the team and send an email inviting the user to
     * migrate their existing personal account onto the team. Team member
     * management apps are required to set an initial given_name and surname for
     * a user to use in the team invitation and for 'Perform as team member'
     * actions taken on the user before they become 'active'.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @param arg The request parameters.
     */
    public teamMembersAddV2(arg: team.MembersAddV2Arg): Promise<DropboxResponse<team.MembersAddLaunchV2Result>>;

    /**
     * Adds members to a team. Permission : Team member management A maximum of
     * 20 members can be specified in a single call. If no Dropbox account
     * exists with the email address specified, a new Dropbox account will be
     * created with the given email address, and that account will be invited to
     * the team. If a personal Dropbox account exists with the email address
     * specified in the call, this call will create a placeholder Dropbox
     * account for the user on the team and send an email inviting the user to
     * migrate their existing personal account onto the team. Team member
     * management apps are required to set an initial given_name and surname for
     * a user to use in the team invitation and for 'Perform as team member'
     * actions taken on the user before they become 'active'.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @param arg The request parameters.
     */
    public teamMembersAdd(arg: team.MembersAddArg): Promise<DropboxResponse<team.MembersAddLaunch>>;

    /**
     * Once an async_job_id is returned from membersAddV2() , use this to poll
     * the status of the asynchronous request. Permission : Team member
     * management.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<async.PollError>.
     * @param arg The request parameters.
     */
    public teamMembersAddJobStatusGetV2(arg: async.PollArg): Promise<DropboxResponse<team.MembersAddJobStatusV2Result>>;

    /**
     * Once an async_job_id is returned from membersAdd() , use this to poll the
     * status of the asynchronous request. Permission : Team member management.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<async.PollError>.
     * @param arg The request parameters.
     */
    public teamMembersAddJobStatusGet(arg: async.PollArg): Promise<DropboxResponse<team.MembersAddJobStatus>>;

    /**
     * Deletes a team member's profile photo. Permission : Team member
     * management.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersDeleteProfilePhotoError>.
     * @param arg The request parameters.
     */
    public teamMembersDeleteProfilePhotoV2(arg: team.MembersDeleteProfilePhotoArg): Promise<DropboxResponse<team.TeamMemberInfoV2Result>>;

    /**
     * Deletes a team member's profile photo. Permission : Team member
     * management.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersDeleteProfilePhotoError>.
     * @param arg The request parameters.
     */
    public teamMembersDeleteProfilePhoto(arg: team.MembersDeleteProfilePhotoArg): Promise<DropboxResponse<team.TeamMemberInfo>>;

    /**
     * Get available TeamMemberRoles for the connected team. To be used with
     * membersSetAdminPermissionsV2(). Permission : Team member management.
     *
     * Route attributes:
     *   scope: members.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     */
    public teamMembersGetAvailableTeamMemberRoles(): Promise<DropboxResponse<team.MembersGetAvailableTeamMemberRolesResult>>;

    /**
     * Returns information about multiple team members. Permission : Team
     * information This endpoint will return MembersGetInfoItem.id_not_found,
     * for IDs (or emails) that cannot be matched to a valid team member.
     *
     * Route attributes:
     *   scope: members.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersGetInfoError>.
     * @param arg The request parameters.
     */
    public teamMembersGetInfoV2(arg: team.MembersGetInfoV2Arg): Promise<DropboxResponse<team.MembersGetInfoV2Result>>;

    /**
     * Returns information about multiple team members. Permission : Team
     * information This endpoint will return MembersGetInfoItem.id_not_found,
     * for IDs (or emails) that cannot be matched to a valid team member.
     *
     * Route attributes:
     *   scope: members.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersGetInfoError>.
     * @param arg The request parameters.
     */
    public teamMembersGetInfo(arg: team.MembersGetInfoArgs): Promise<DropboxResponse<team.MembersGetInfoResult>>;

    /**
     * Lists members of a team. Permission : Team information.
     *
     * Route attributes:
     *   scope: members.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersListError>.
     * @param arg The request parameters.
     */
    public teamMembersListV2(arg: team.MembersListArg): Promise<DropboxResponse<team.MembersListV2Result>>;

    /**
     * Lists members of a team. Permission : Team information.
     *
     * Route attributes:
     *   scope: members.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersListError>.
     * @param arg The request parameters.
     */
    public teamMembersList(arg: team.MembersListArg): Promise<DropboxResponse<team.MembersListResult>>;

    /**
     * Once a cursor has been retrieved from membersListV2(), use this to
     * paginate through all team members. Permission : Team information.
     *
     * Route attributes:
     *   scope: members.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersListContinueError>.
     * @param arg The request parameters.
     */
    public teamMembersListContinueV2(arg: team.MembersListContinueArg): Promise<DropboxResponse<team.MembersListV2Result>>;

    /**
     * Once a cursor has been retrieved from membersList(), use this to paginate
     * through all team members. Permission : Team information.
     *
     * Route attributes:
     *   scope: members.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersListContinueError>.
     * @param arg The request parameters.
     */
    public teamMembersListContinue(arg: team.MembersListContinueArg): Promise<DropboxResponse<team.MembersListResult>>;

    /**
     * Moves removed member's files to a different member. This endpoint
     * initiates an asynchronous job. To obtain the final result of the job, the
     * client should periodically poll
     * membersMoveFormerMemberFilesJobStatusCheck(). Permission : Team member
     * management.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersTransferFormerMembersFilesError>.
     * @param arg The request parameters.
     */
    public teamMembersMoveFormerMemberFiles(arg: team.MembersDataTransferArg): Promise<DropboxResponse<async.LaunchEmptyResult>>;

    /**
     * Once an async_job_id is returned from membersMoveFormerMemberFiles() ,
     * use this to poll the status of the asynchronous request. Permission :
     * Team member management.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<async.PollError>.
     * @param arg The request parameters.
     */
    public teamMembersMoveFormerMemberFilesJobStatusCheck(arg: async.PollArg): Promise<DropboxResponse<async.PollEmptyResult>>;

    /**
     * Recover a deleted member. Permission : Team member management Exactly one
     * of team_member_id, email, or external_id must be provided to identify the
     * user account.
     *
     * Route attributes:
     *   scope: members.delete
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersRecoverError>.
     * @param arg The request parameters.
     */
    public teamMembersRecover(arg: team.MembersRecoverArg): Promise<DropboxResponse<void>>;

    /**
     * Removes a member from a team. Permission : Team member management Exactly
     * one of team_member_id, email, or external_id must be provided to identify
     * the user account. Accounts can be recovered via membersRecover() for a 7
     * day period or until the account has been permanently deleted or
     * transferred to another account (whichever comes first). Calling
     * membersAdd() while a user is still recoverable on your team will return
     * with MemberAddResult.user_already_on_team. Accounts can have their files
     * transferred via the admin console for a limited time, based on the
     * version history length associated with the team (180 days for most
     * teams). This endpoint may initiate an asynchronous job. To obtain the
     * final result of the job, the client should periodically poll
     * membersRemoveJobStatusGet().
     *
     * Route attributes:
     *   scope: members.delete
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersRemoveError>.
     * @param arg The request parameters.
     */
    public teamMembersRemove(arg: team.MembersRemoveArg): Promise<DropboxResponse<async.LaunchEmptyResult>>;

    /**
     * Once an async_job_id is returned from membersRemove() , use this to poll
     * the status of the asynchronous request. Permission : Team member
     * management.
     *
     * Route attributes:
     *   scope: members.delete
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<async.PollError>.
     * @param arg The request parameters.
     */
    public teamMembersRemoveJobStatusGet(arg: async.PollArg): Promise<DropboxResponse<async.PollEmptyResult>>;

    /**
     * Add secondary emails to users. Permission : Team member management.
     * Emails that are on verified domains will be verified automatically. For
     * each email address not on a verified domain a verification email will be
     * sent.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.AddSecondaryEmailsError>.
     * @param arg The request parameters.
     */
    public teamMembersSecondaryEmailsAdd(arg: team.AddSecondaryEmailsArg): Promise<DropboxResponse<team.AddSecondaryEmailsResult>>;

    /**
     * Delete secondary emails from users Permission : Team member management.
     * Users will be notified of deletions of verified secondary emails at both
     * the secondary email and their primary email.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @param arg The request parameters.
     */
    public teamMembersSecondaryEmailsDelete(arg: team.DeleteSecondaryEmailsArg): Promise<DropboxResponse<team.DeleteSecondaryEmailsResult>>;

    /**
     * Resend secondary email verification emails. Permission : Team member
     * management.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @param arg The request parameters.
     */
    public teamMembersSecondaryEmailsResendVerificationEmails(arg: team.ResendVerificationEmailArg): Promise<DropboxResponse<team.ResendVerificationEmailResult>>;

    /**
     * Sends welcome email to pending team member. Permission : Team member
     * management Exactly one of team_member_id, email, or external_id must be
     * provided to identify the user account. No-op if team member is not
     * pending.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersSendWelcomeError>.
     * @param arg The request parameters.
     */
    public teamMembersSendWelcomeEmail(arg: team.UserSelectorArg): Promise<DropboxResponse<void>>;

    /**
     * Updates a team member's permissions. Permission : Team member management.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersSetPermissions2Error>.
     * @param arg The request parameters.
     */
    public teamMembersSetAdminPermissionsV2(arg: team.MembersSetPermissions2Arg): Promise<DropboxResponse<team.MembersSetPermissions2Result>>;

    /**
     * Updates a team member's permissions. Permission : Team member management.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersSetPermissionsError>.
     * @param arg The request parameters.
     */
    public teamMembersSetAdminPermissions(arg: team.MembersSetPermissionsArg): Promise<DropboxResponse<team.MembersSetPermissionsResult>>;

    /**
     * Updates a team member's profile. Permission : Team member management.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersSetProfileError>.
     * @param arg The request parameters.
     */
    public teamMembersSetProfileV2(arg: team.MembersSetProfileArg): Promise<DropboxResponse<team.TeamMemberInfoV2Result>>;

    /**
     * Updates a team member's profile. Permission : Team member management.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersSetProfileError>.
     * @param arg The request parameters.
     */
    public teamMembersSetProfile(arg: team.MembersSetProfileArg): Promise<DropboxResponse<team.TeamMemberInfo>>;

    /**
     * Updates a team member's profile photo. Permission : Team member
     * management.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersSetProfilePhotoError>.
     * @param arg The request parameters.
     */
    public teamMembersSetProfilePhotoV2(arg: team.MembersSetProfilePhotoArg): Promise<DropboxResponse<team.TeamMemberInfoV2Result>>;

    /**
     * Updates a team member's profile photo. Permission : Team member
     * management.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersSetProfilePhotoError>.
     * @param arg The request parameters.
     */
    public teamMembersSetProfilePhoto(arg: team.MembersSetProfilePhotoArg): Promise<DropboxResponse<team.TeamMemberInfo>>;

    /**
     * Suspend a member from a team. Permission : Team member management Exactly
     * one of team_member_id, email, or external_id must be provided to identify
     * the user account.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersSuspendError>.
     * @param arg The request parameters.
     */
    public teamMembersSuspend(arg: team.MembersDeactivateArg): Promise<DropboxResponse<void>>;

    /**
     * Unsuspend a member from a team. Permission : Team member management
     * Exactly one of team_member_id, email, or external_id must be provided to
     * identify the user account.
     *
     * Route attributes:
     *   scope: members.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.MembersUnsuspendError>.
     * @param arg The request parameters.
     */
    public teamMembersUnsuspend(arg: team.MembersUnsuspendArg): Promise<DropboxResponse<void>>;

    /**
     * Returns a list of all team-accessible namespaces. This list includes team
     * folders, shared folders containing team members, team members' home
     * namespaces, and team members' app folders. Home namespaces and app
     * folders are always owned by this team or members of the team, but shared
     * folders may be owned by other users or other teams. Duplicates may occur
     * in the list.
     *
     * Route attributes:
     *   scope: team_data.member
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.TeamNamespacesListError>.
     * @param arg The request parameters.
     */
    public teamNamespacesList(arg: team.TeamNamespacesListArg): Promise<DropboxResponse<team.TeamNamespacesListResult>>;

    /**
     * Once a cursor has been retrieved from namespacesList(), use this to
     * paginate through all team-accessible namespaces. Duplicates may occur in
     * the list.
     *
     * Route attributes:
     *   scope: team_data.member
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.TeamNamespacesListContinueError>.
     * @param arg The request parameters.
     */
    public teamNamespacesListContinue(arg: team.TeamNamespacesListContinueArg): Promise<DropboxResponse<team.TeamNamespacesListResult>>;

    /**
     * Permission : Team member file access.
     *
     * Route attributes:
     *   scope: files.team_metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.ModifyTemplateError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public teamPropertiesTemplateAdd(arg: file_properties.AddTemplateArg): Promise<DropboxResponse<file_properties.AddTemplateResult>>;

    /**
     * Permission : Team member file access. The scope for the route is
     * files.team_metadata.write.
     *
     * Route attributes:
     *   scope: files.team_metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.TemplateError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public teamPropertiesTemplateGet(arg: file_properties.GetTemplateArg): Promise<DropboxResponse<file_properties.GetTemplateResult>>;

    /**
     * Permission : Team member file access. The scope for the route is
     * files.team_metadata.write.
     *
     * Route attributes:
     *   scope: files.team_metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.TemplateError>.
     * @deprecated
     */
    public teamPropertiesTemplateList(): Promise<DropboxResponse<file_properties.ListTemplateResult>>;

    /**
     * Permission : Team member file access.
     *
     * Route attributes:
     *   scope: files.team_metadata.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<file_properties.ModifyTemplateError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public teamPropertiesTemplateUpdate(arg: file_properties.UpdateTemplateArg): Promise<DropboxResponse<file_properties.UpdateTemplateResult>>;

    /**
     * Retrieves reporting data about a team's user activity. Deprecated: Will
     * be removed on July 1st 2021.
     *
     * Route attributes:
     *   scope: team_info.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.DateRangeError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public teamReportsGetActivity(arg: team.DateRange): Promise<DropboxResponse<team.GetActivityReport>>;

    /**
     * Retrieves reporting data about a team's linked devices. Deprecated: Will
     * be removed on July 1st 2021.
     *
     * Route attributes:
     *   scope: team_info.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.DateRangeError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public teamReportsGetDevices(arg: team.DateRange): Promise<DropboxResponse<team.GetDevicesReport>>;

    /**
     * Retrieves reporting data about a team's membership. Deprecated: Will be
     * removed on July 1st 2021.
     *
     * Route attributes:
     *   scope: team_info.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.DateRangeError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public teamReportsGetMembership(arg: team.DateRange): Promise<DropboxResponse<team.GetMembershipReport>>;

    /**
     * Retrieves reporting data about a team's storage usage. Deprecated: Will
     * be removed on July 1st 2021.
     *
     * Route attributes:
     *   scope: team_info.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.DateRangeError>.
     * @deprecated
     * @param arg The request parameters.
     */
    public teamReportsGetStorage(arg: team.DateRange): Promise<DropboxResponse<team.GetStorageReport>>;

    /**
     * Endpoint adds Approve List entries. Changes are effective immediately.
     * Changes are committed in transaction. In case of single validation error
     * - all entries are rejected. Valid domains (RFC-1034/5) and emails
     * (RFC-5322/822) are accepted. Added entries cannot overflow limit of 10000
     * entries per team. Maximum 100 entries per call is allowed.
     *
     * Route attributes:
     *   scope: team_info.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.SharingAllowlistAddError>.
     * @param arg The request parameters.
     */
    public teamSharingAllowlistAdd(arg: team.SharingAllowlistAddArgs): Promise<DropboxResponse<team.SharingAllowlistAddResponse>>;

    /**
     * Lists Approve List entries for given team, from newest to oldest,
     * returning up to `limit` entries at a time. If there are more than `limit`
     * entries associated with the current team, more can be fetched by passing
     * the returned `cursor` to sharingAllowlistListContinue().
     *
     * Route attributes:
     *   scope: team_info.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.SharingAllowlistListError>.
     * @param arg The request parameters.
     */
    public teamSharingAllowlistList(arg: team.SharingAllowlistListArg): Promise<DropboxResponse<team.SharingAllowlistListResponse>>;

    /**
     * Lists entries associated with given team, starting from a the cursor. See
     * sharingAllowlistList().
     *
     * Route attributes:
     *   scope: team_info.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.SharingAllowlistListContinueError>.
     * @param arg The request parameters.
     */
    public teamSharingAllowlistListContinue(arg: team.SharingAllowlistListContinueArg): Promise<DropboxResponse<team.SharingAllowlistListResponse>>;

    /**
     * Endpoint removes Approve List entries. Changes are effective immediately.
     * Changes are committed in transaction. In case of single validation error
     * - all entries are rejected. Valid domains (RFC-1034/5) and emails
     * (RFC-5322/822) are accepted. Entries being removed have to be present on
     * the list. Maximum 1000 entries per call is allowed.
     *
     * Route attributes:
     *   scope: team_info.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.SharingAllowlistRemoveError>.
     * @param arg The request parameters.
     */
    public teamSharingAllowlistRemove(arg: team.SharingAllowlistRemoveArgs): Promise<DropboxResponse<team.SharingAllowlistRemoveResponse>>;

    /**
     * Sets an archived team folder's status to active. Permission : Team member
     * file access.
     *
     * Route attributes:
     *   scope: team_data.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.TeamFolderActivateError>.
     * @param arg The request parameters.
     */
    public teamTeamFolderActivate(arg: team.TeamFolderIdArg): Promise<DropboxResponse<team.TeamFolderMetadata>>;

    /**
     * Sets an active team folder's status to archived and removes all folder
     * and file members. This endpoint cannot be used for teams that have a
     * shared team space. Permission : Team member file access.
     *
     * Route attributes:
     *   scope: team_data.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.TeamFolderArchiveError>.
     * @param arg The request parameters.
     */
    public teamTeamFolderArchive(arg: team.TeamFolderArchiveArg): Promise<DropboxResponse<team.TeamFolderArchiveLaunch>>;

    /**
     * Returns the status of an asynchronous job for archiving a team folder.
     * Permission : Team member file access.
     *
     * Route attributes:
     *   scope: team_data.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<async.PollError>.
     * @param arg The request parameters.
     */
    public teamTeamFolderArchiveCheck(arg: async.PollArg): Promise<DropboxResponse<team.TeamFolderArchiveJobStatus>>;

    /**
     * Creates a new, active, team folder with no members. This endpoint can
     * only be used for teams that do not already have a shared team space.
     * Permission : Team member file access.
     *
     * Route attributes:
     *   scope: team_data.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.TeamFolderCreateError>.
     * @param arg The request parameters.
     */
    public teamTeamFolderCreate(arg: team.TeamFolderCreateArg): Promise<DropboxResponse<team.TeamFolderMetadata>>;

    /**
     * Retrieves metadata for team folders. Permission : Team member file
     * access.
     *
     * Route attributes:
     *   scope: team_data.content.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     * @param arg The request parameters.
     */
    public teamTeamFolderGetInfo(arg: team.TeamFolderIdListArg): Promise<DropboxResponse<Array<team.TeamFolderGetInfoItem>>>;

    /**
     * Lists all team folders. Permission : Team member file access.
     *
     * Route attributes:
     *   scope: team_data.content.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.TeamFolderListError>.
     * @param arg The request parameters.
     */
    public teamTeamFolderList(arg: team.TeamFolderListArg): Promise<DropboxResponse<team.TeamFolderListResult>>;

    /**
     * Once a cursor has been retrieved from teamFolderList(), use this to
     * paginate through all team folders. Permission : Team member file access.
     *
     * Route attributes:
     *   scope: team_data.content.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.TeamFolderListContinueError>.
     * @param arg The request parameters.
     */
    public teamTeamFolderListContinue(arg: team.TeamFolderListContinueArg): Promise<DropboxResponse<team.TeamFolderListResult>>;

    /**
     * Permanently deletes an archived team folder. This endpoint cannot be used
     * for teams that have a shared team space. Permission : Team member file
     * access.
     *
     * Route attributes:
     *   scope: team_data.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.TeamFolderPermanentlyDeleteError>.
     * @param arg The request parameters.
     */
    public teamTeamFolderPermanentlyDelete(arg: team.TeamFolderIdArg): Promise<DropboxResponse<void>>;

    /**
     * Changes an active team folder's name. Permission : Team member file
     * access.
     *
     * Route attributes:
     *   scope: team_data.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.TeamFolderRenameError>.
     * @param arg The request parameters.
     */
    public teamTeamFolderRename(arg: team.TeamFolderRenameArg): Promise<DropboxResponse<team.TeamFolderMetadata>>;

    /**
     * Updates the sync settings on a team folder or its contents.  Use of this
     * endpoint requires that the team has team selective sync enabled.
     *
     * Route attributes:
     *   scope: team_data.content.write
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.TeamFolderUpdateSyncSettingsError>.
     * @param arg The request parameters.
     */
    public teamTeamFolderUpdateSyncSettings(arg: team.TeamFolderUpdateSyncSettingsArg): Promise<DropboxResponse<team.TeamFolderMetadata>>;

    /**
     * Returns the member profile of the admin who generated the team access
     * token used to make the call.
     *
     * Route attributes:
     *   scope: team_info.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team.TokenGetAuthenticatedAdminError>.
     */
    public teamTokenGetAuthenticatedAdmin(): Promise<DropboxResponse<team.TokenGetAuthenticatedAdminResult>>;

    /**
     * Retrieves team events. If the result's GetTeamEventsResult.has_more field
     * is true, call getEventsContinue() with the returned cursor to retrieve
     * more entries. If end_time is not specified in your request, you may use
     * the returned cursor to poll getEventsContinue() for new events. Many
     * attributes note 'may be missing due to historical data gap'. Note that
     * the file_operations category and & analogous paper events are not
     * available on all Dropbox Business [plans]{@link
     * /business/plans-comparison}. Use [features/get_values]{@link
     * /developers/documentation/http/teams#team-features-get_values} to check
     * for this feature. Permission : Team Auditing.
     *
     * Route attributes:
     *   scope: events.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team_log.GetTeamEventsError>.
     * @param arg The request parameters.
     */
    public teamLogGetEvents(arg: team_log.GetTeamEventsArg): Promise<DropboxResponse<team_log.GetTeamEventsResult>>;

    /**
     * Once a cursor has been retrieved from getEvents(), use this to paginate
     * through all events. Permission : Team Auditing.
     *
     * Route attributes:
     *   scope: events.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<team_log.GetTeamEventsContinueError>.
     * @param arg The request parameters.
     */
    public teamLogGetEventsContinue(arg: team_log.GetTeamEventsContinueArg): Promise<DropboxResponse<team_log.GetTeamEventsResult>>;

    /**
     * Get a list of feature values that may be configured for the current
     * account.
     *
     * Route attributes:
     *   scope: account_info.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<users.UserFeaturesGetValuesBatchError>.
     * @param arg The request parameters.
     */
    public usersFeaturesGetValues(arg: users.UserFeaturesGetValuesBatchArg): Promise<DropboxResponse<users.UserFeaturesGetValuesBatchResult>>;

    /**
     * Get information about a user's account.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<users.GetAccountError>.
     * @param arg The request parameters.
     */
    public usersGetAccount(arg: users.GetAccountArg): Promise<DropboxResponse<users.BasicAccount>>;

    /**
     * Get information about multiple user accounts.  At most 300 accounts may
     * be queried per request.
     *
     * Route attributes:
     *   scope: sharing.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<users.GetAccountBatchError>.
     * @param arg The request parameters.
     */
    public usersGetAccountBatch(arg: users.GetAccountBatchArg): Promise<DropboxResponse<users.GetAccountBatchResult>>;

    /**
     * Get information about the current user's account.
     *
     * Route attributes:
     *   scope: account_info.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     */
    public usersGetCurrentAccount(): Promise<DropboxResponse<users.FullAccount>>;

    /**
     * Get the space usage information for the current user's account.
     *
     * Route attributes:
     *   scope: account_info.read
     *
     * When an error occurs, the route rejects the promise with type
     * DropboxResponseError<void>.
     */
    public usersGetSpaceUsage(): Promise<DropboxResponse<users.SpaceUsage>>;
}

