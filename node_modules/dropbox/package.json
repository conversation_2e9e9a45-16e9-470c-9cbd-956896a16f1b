{"name": "dropbox", "version": "10.34.0", "registry": "npm", "description": "The Dropbox JavaScript SDK is a lightweight, promise based interface to the Dropbox v2 API that works in both nodejs and browser environments.", "main": "cjs/index.js", "browser": "dist/Dropbox-sdk.min.js", "typings": "types/index", "types": "types/index", "module": "es/index.js", "jsnext:main": "es/index.js", "scripts": {"build:es": "cross-env BABEL_ENV=es babel src -d es/src && cross-env BABEL_ENV=es babel lib -d es/lib && cross-env BABEL_ENV=es babel index.js -d es", "build:cjs": "cross-env BABEL_ENV=commonjs babel src -d cjs/src && cross-env BABEL_ENV=commonjs babel lib -d cjs/lib && cross-env BABEL_ENV=commonjs babel index.js -d cjs", "build:umd": "cross-env BABEL_ENV=es BUNDLE_TYPE=normal rollup -c -i index.js -o dist/Dropbox-sdk.js -n Dropbox", "build:umd:min": "cross-env BABEL_ENV=es BUNDLE_TYPE=minified rollup -c -i index.js -o dist/Dropbox-sdk.min.js -n Dropbox", "build": "npm run build:es && npm run build:cjs && npm run build:umd && npm run build:umd:min", "lint": "eslint --ext .js,.jsx,.ts .", "lint-fix": "eslint --fix --ext .js,.jsx,.ts .", "test": "npm run test:typescript && npm run test:unit", "test:typescript": "tsc --build test/types", "test:integration": "mocha --timeout 10000 --require @babel/register test/integration/**/*.js", "test:unit": "mocha --require @babel/register test/unit/**/*.js", "test:build": "mocha --timeout 100000 --require @babel/register test/build/*.js", "report": "nyc report --reporter=lcov --reporter=text", "clean": "rm -rf dist es cjs", "generate-docs": "jsdoc -c ./.jsdoc.json", "coverage:unit": "cross-env BABEL_ENV=coverage nyc --reporter=lcov npm run test:unit", "coverage:integration": "cross-env BABEL_ENV=coverage nyc --reporter=lcov npm run test:integration"}, "files": ["*.md", "LICENSE", "index.js", "src", "lib", "types", "dist", "es", "cjs"], "keywords": ["dropbox", "files", "sync", "sdk", "client"], "homepage": "https://github.com/dropbox/dropbox-sdk-js#readme", "repository": {"type": "git", "url": "git+https://github.com/dropbox/dropbox-sdk-js.git"}, "bugs": {"url": "https://github.com/dropbox/dropbox-sdk-js/issues"}, "license": "MIT", "directories": {"example": "examples", "test": "test"}, "engines": {"node": ">=0.10.3"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<PERSON><PERSON><PERSON>.<EMAIL>>"], "devDependencies": {"@babel/cli": "^7.11.6", "@babel/core": "^7.11.6", "@babel/preset-env": "^7.11.5", "@babel/register": "^7.11.5", "@testing-library/dom": "^7.24.5", "@types/node": "^14.11.2", "@types/node-fetch": "^2.5.7", "@typescript-eslint/eslint-plugin": "^4.0.0", "@typescript-eslint/parser": "^3.10.1", "babel-plugin-istanbul": "^6.0.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "cross-env": "^7.0.2", "eslint": "^7.9.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-plugin-import": "^2.22.0", "express": "^4.17.1", "express-urlrewrite": "^1.3.0", "gh-pages": "^3.1.0", "ink-docstrap": "^1.3.2", "jsdoc": "^3.6.6", "jsdom": "^16.4.0", "mocha": "^8.1.3", "nyc": "^15.1.0", "prompt": "^1.0.0", "rollup": "^2.28.2", "rollup-endpoint": "^0.2.2", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "sinon": "^9.0.3", "typescript": "^4.0.3"}, "peerDependencies": {"@types/node-fetch": "^2.5.7"}, "dependencies": {"node-fetch": "^2.6.1"}}