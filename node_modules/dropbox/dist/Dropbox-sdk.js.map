{"version": 3, "file": "Dropbox-sdk.js", "sources": ["../src/constants.js", "../lib/routes.js", "../src/utils.js", "../src/error.js", "../src/response.js", "../src/auth.js", "../src/dropbox.js"], "sourcesContent": ["export const RPC = 'rpc';\nexport const UPLOAD = 'upload';\nexport const DOWNLOAD = 'download';\n\nexport const APP_AUTH = 'app';\nexport const USER_AUTH = 'user';\nexport const TEAM_AUTH = 'team';\nexport const NO_AUTH = 'noauth';\nexport const COOKIE = 'cookie';\n\nexport const DEFAULT_API_DOMAIN = 'dropboxapi.com';\nexport const DEFAULT_DOMAIN = 'dropbox.com';\n\nexport const TEST_DOMAIN_MAPPINGS = {\n  api: 'api',\n  notify: 'bolt',\n  content: 'api-content',\n};\n", "// Auto-generated by <PERSON>, do not modify.\nvar routes = {};\n\n/**\n * Sets a user's profile photo.\n * Route attributes:\n *   scope: account_info.write\n * @function Dropbox#accountSetProfilePhoto\n * @arg {AccountSetProfilePhotoArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<AccountSetProfilePhotoResult>, DropboxResponseError.<AccountSetProfilePhotoError>>}\n */\nroutes.accountSetProfilePhoto = function (arg) {\n  return this.request('account/set_profile_photo', arg, 'user', 'api', 'rpc', 'account_info.write');\n};\n\n/**\n * Creates an OAuth 2.0 access token from the supplied OAuth 1.0 access token.\n * @function Dropbox#authTokenFromOauth1\n * @arg {AuthTokenFromOAuth1Arg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<AuthTokenFromOAuth1Result>, DropboxResponseError.<AuthTokenFromOAuth1Error>>}\n */\nroutes.authTokenFromOauth1 = function (arg) {\n  return this.request('auth/token/from_oauth1', arg, 'app', 'api', 'rpc', null);\n};\n\n/**\n * Disables the access token used to authenticate the call. If there is a\n * corresponding refresh token for the access token, this disables that refresh\n * token, as well as any other access tokens for that refresh token.\n * @function Dropbox#authTokenRevoke\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<void>>}\n */\nroutes.authTokenRevoke = function () {\n  return this.request('auth/token/revoke', null, 'user', 'api', 'rpc', null);\n};\n\n/**\n * This endpoint performs App Authentication, validating the supplied app key\n * and secret, and returns the supplied string, to allow you to test your code\n * and connection to the Dropbox API. It has no other effect. If you receive an\n * HTTP 200 response with the supplied query, it indicates at least part of the\n * Dropbox API infrastructure is working and that the app key and secret valid.\n * @function Dropbox#checkApp\n * @arg {CheckEchoArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<CheckEchoResult>, DropboxResponseError.<void>>}\n */\nroutes.checkApp = function (arg) {\n  return this.request('check/app', arg, 'app', 'api', 'rpc', null);\n};\n\n/**\n * This endpoint performs User Authentication, validating the supplied access\n * token, and returns the supplied string, to allow you to test your code and\n * connection to the Dropbox API. It has no other effect. If you receive an HTTP\n * 200 response with the supplied query, it indicates at least part of the\n * Dropbox API infrastructure is working and that the access token is valid.\n * Route attributes:\n *   scope: account_info.read\n * @function Dropbox#checkUser\n * @arg {CheckEchoArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<CheckEchoResult>, DropboxResponseError.<void>>}\n */\nroutes.checkUser = function (arg) {\n  return this.request('check/user', arg, 'user', 'api', 'rpc', 'account_info.read');\n};\n\n/**\n * Removes all manually added contacts. You'll still keep contacts who are on\n * your team or who you imported. New contacts will be added when you share.\n * Route attributes:\n *   scope: contacts.write\n * @function Dropbox#contactsDeleteManualContacts\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<void>>}\n */\nroutes.contactsDeleteManualContacts = function () {\n  return this.request('contacts/delete_manual_contacts', null, 'user', 'api', 'rpc', 'contacts.write');\n};\n\n/**\n * Removes manually added contacts from the given list.\n * Route attributes:\n *   scope: contacts.write\n * @function Dropbox#contactsDeleteManualContactsBatch\n * @arg {ContactsDeleteManualContactsArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<ContactsDeleteManualContactsError>>}\n */\nroutes.contactsDeleteManualContactsBatch = function (arg) {\n  return this.request('contacts/delete_manual_contacts_batch', arg, 'user', 'api', 'rpc', 'contacts.write');\n};\n\n/**\n * Add property groups to a Dropbox file. See templates/add_for_user or\n * templates/add_for_team to create new templates.\n * Route attributes:\n *   scope: files.metadata.write\n * @function Dropbox#filePropertiesPropertiesAdd\n * @arg {FilePropertiesAddPropertiesArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<FilePropertiesAddPropertiesError>>}\n */\nroutes.filePropertiesPropertiesAdd = function (arg) {\n  return this.request('file_properties/properties/add', arg, 'user', 'api', 'rpc', 'files.metadata.write');\n};\n\n/**\n * Overwrite property groups associated with a file. This endpoint should be\n * used instead of properties/update when property groups are being updated via\n * a \"snapshot\" instead of via a \"delta\". In other words, this endpoint will\n * delete all omitted fields from a property group, whereas properties/update\n * will only delete fields that are explicitly marked for deletion.\n * Route attributes:\n *   scope: files.metadata.write\n * @function Dropbox#filePropertiesPropertiesOverwrite\n * @arg {FilePropertiesOverwritePropertyGroupArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<FilePropertiesInvalidPropertyGroupError>>}\n */\nroutes.filePropertiesPropertiesOverwrite = function (arg) {\n  return this.request('file_properties/properties/overwrite', arg, 'user', 'api', 'rpc', 'files.metadata.write');\n};\n\n/**\n * Permanently removes the specified property group from the file. To remove\n * specific property field key value pairs, see properties/update. To update a\n * template, see templates/update_for_user or templates/update_for_team. To\n * remove a template, see templates/remove_for_user or\n * templates/remove_for_team.\n * Route attributes:\n *   scope: files.metadata.write\n * @function Dropbox#filePropertiesPropertiesRemove\n * @arg {FilePropertiesRemovePropertiesArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<FilePropertiesRemovePropertiesError>>}\n */\nroutes.filePropertiesPropertiesRemove = function (arg) {\n  return this.request('file_properties/properties/remove', arg, 'user', 'api', 'rpc', 'files.metadata.write');\n};\n\n/**\n * Search across property templates for particular property field values.\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#filePropertiesPropertiesSearch\n * @arg {FilePropertiesPropertiesSearchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilePropertiesPropertiesSearchResult>, DropboxResponseError.<FilePropertiesPropertiesSearchError>>}\n */\nroutes.filePropertiesPropertiesSearch = function (arg) {\n  return this.request('file_properties/properties/search', arg, 'user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * Once a cursor has been retrieved from properties/search, use this to paginate\n * through all search results.\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#filePropertiesPropertiesSearchContinue\n * @arg {FilePropertiesPropertiesSearchContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilePropertiesPropertiesSearchResult>, DropboxResponseError.<FilePropertiesPropertiesSearchContinueError>>}\n */\nroutes.filePropertiesPropertiesSearchContinue = function (arg) {\n  return this.request('file_properties/properties/search/continue', arg, 'user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * Add, update or remove properties associated with the supplied file and\n * templates. This endpoint should be used instead of properties/overwrite when\n * property groups are being updated via a \"delta\" instead of via a \"snapshot\" .\n * In other words, this endpoint will not delete any omitted fields from a\n * property group, whereas properties/overwrite will delete any fields that are\n * omitted from a property group.\n * Route attributes:\n *   scope: files.metadata.write\n * @function Dropbox#filePropertiesPropertiesUpdate\n * @arg {FilePropertiesUpdatePropertiesArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<FilePropertiesUpdatePropertiesError>>}\n */\nroutes.filePropertiesPropertiesUpdate = function (arg) {\n  return this.request('file_properties/properties/update', arg, 'user', 'api', 'rpc', 'files.metadata.write');\n};\n\n/**\n * Add a template associated with a team. See properties/add to add properties\n * to a file or folder. Note: this endpoint will create team-owned templates.\n * Route attributes:\n *   scope: files.team_metadata.write\n * @function Dropbox#filePropertiesTemplatesAddForTeam\n * @arg {FilePropertiesAddTemplateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilePropertiesAddTemplateResult>, DropboxResponseError.<FilePropertiesModifyTemplateError>>}\n */\nroutes.filePropertiesTemplatesAddForTeam = function (arg) {\n  return this.request('file_properties/templates/add_for_team', arg, 'team', 'api', 'rpc', 'files.team_metadata.write');\n};\n\n/**\n * Add a template associated with a user. See properties/add to add properties\n * to a file. This endpoint can't be called on a team member or admin's behalf.\n * Route attributes:\n *   scope: files.metadata.write\n * @function Dropbox#filePropertiesTemplatesAddForUser\n * @arg {FilePropertiesAddTemplateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilePropertiesAddTemplateResult>, DropboxResponseError.<FilePropertiesModifyTemplateError>>}\n */\nroutes.filePropertiesTemplatesAddForUser = function (arg) {\n  return this.request('file_properties/templates/add_for_user', arg, 'user', 'api', 'rpc', 'files.metadata.write');\n};\n\n/**\n * Get the schema for a specified template.\n * Route attributes:\n *   scope: files.team_metadata.write\n * @function Dropbox#filePropertiesTemplatesGetForTeam\n * @arg {FilePropertiesGetTemplateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilePropertiesGetTemplateResult>, DropboxResponseError.<FilePropertiesTemplateError>>}\n */\nroutes.filePropertiesTemplatesGetForTeam = function (arg) {\n  return this.request('file_properties/templates/get_for_team', arg, 'team', 'api', 'rpc', 'files.team_metadata.write');\n};\n\n/**\n * Get the schema for a specified template. This endpoint can't be called on a\n * team member or admin's behalf.\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#filePropertiesTemplatesGetForUser\n * @arg {FilePropertiesGetTemplateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilePropertiesGetTemplateResult>, DropboxResponseError.<FilePropertiesTemplateError>>}\n */\nroutes.filePropertiesTemplatesGetForUser = function (arg) {\n  return this.request('file_properties/templates/get_for_user', arg, 'user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * Get the template identifiers for a team. To get the schema of each template\n * use templates/get_for_team.\n * Route attributes:\n *   scope: files.team_metadata.write\n * @function Dropbox#filePropertiesTemplatesListForTeam\n * @returns {Promise.<DropboxResponse<FilePropertiesListTemplateResult>, DropboxResponseError.<FilePropertiesTemplateError>>}\n */\nroutes.filePropertiesTemplatesListForTeam = function () {\n  return this.request('file_properties/templates/list_for_team', null, 'team', 'api', 'rpc', 'files.team_metadata.write');\n};\n\n/**\n * Get the template identifiers for a team. To get the schema of each template\n * use templates/get_for_user. This endpoint can't be called on a team member or\n * admin's behalf.\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#filePropertiesTemplatesListForUser\n * @returns {Promise.<DropboxResponse<FilePropertiesListTemplateResult>, DropboxResponseError.<FilePropertiesTemplateError>>}\n */\nroutes.filePropertiesTemplatesListForUser = function () {\n  return this.request('file_properties/templates/list_for_user', null, 'user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * Permanently removes the specified template created from\n * templates/add_for_user. All properties associated with the template will also\n * be removed. This action cannot be undone.\n * Route attributes:\n *   scope: files.team_metadata.write\n * @function Dropbox#filePropertiesTemplatesRemoveForTeam\n * @arg {FilePropertiesRemoveTemplateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<FilePropertiesTemplateError>>}\n */\nroutes.filePropertiesTemplatesRemoveForTeam = function (arg) {\n  return this.request('file_properties/templates/remove_for_team', arg, 'team', 'api', 'rpc', 'files.team_metadata.write');\n};\n\n/**\n * Permanently removes the specified template created from\n * templates/add_for_user. All properties associated with the template will also\n * be removed. This action cannot be undone.\n * Route attributes:\n *   scope: files.metadata.write\n * @function Dropbox#filePropertiesTemplatesRemoveForUser\n * @arg {FilePropertiesRemoveTemplateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<FilePropertiesTemplateError>>}\n */\nroutes.filePropertiesTemplatesRemoveForUser = function (arg) {\n  return this.request('file_properties/templates/remove_for_user', arg, 'user', 'api', 'rpc', 'files.metadata.write');\n};\n\n/**\n * Update a template associated with a team. This route can update the template\n * name, the template description and add optional properties to templates.\n * Route attributes:\n *   scope: files.team_metadata.write\n * @function Dropbox#filePropertiesTemplatesUpdateForTeam\n * @arg {FilePropertiesUpdateTemplateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilePropertiesUpdateTemplateResult>, DropboxResponseError.<FilePropertiesModifyTemplateError>>}\n */\nroutes.filePropertiesTemplatesUpdateForTeam = function (arg) {\n  return this.request('file_properties/templates/update_for_team', arg, 'team', 'api', 'rpc', 'files.team_metadata.write');\n};\n\n/**\n * Update a template associated with a user. This route can update the template\n * name, the template description and add optional properties to templates. This\n * endpoint can't be called on a team member or admin's behalf.\n * Route attributes:\n *   scope: files.metadata.write\n * @function Dropbox#filePropertiesTemplatesUpdateForUser\n * @arg {FilePropertiesUpdateTemplateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilePropertiesUpdateTemplateResult>, DropboxResponseError.<FilePropertiesModifyTemplateError>>}\n */\nroutes.filePropertiesTemplatesUpdateForUser = function (arg) {\n  return this.request('file_properties/templates/update_for_user', arg, 'user', 'api', 'rpc', 'files.metadata.write');\n};\n\n/**\n * Returns the total number of file requests owned by this user. Includes both\n * open and closed file requests.\n * Route attributes:\n *   scope: file_requests.read\n * @function Dropbox#fileRequestsCount\n * @returns {Promise.<DropboxResponse<FileRequestsCountFileRequestsResult>, DropboxResponseError.<FileRequestsCountFileRequestsError>>}\n */\nroutes.fileRequestsCount = function () {\n  return this.request('file_requests/count', null, 'user', 'api', 'rpc', 'file_requests.read');\n};\n\n/**\n * Creates a file request for this user.\n * Route attributes:\n *   scope: file_requests.write\n * @function Dropbox#fileRequestsCreate\n * @arg {FileRequestsCreateFileRequestArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FileRequestsFileRequest>, DropboxResponseError.<FileRequestsCreateFileRequestError>>}\n */\nroutes.fileRequestsCreate = function (arg) {\n  return this.request('file_requests/create', arg, 'user', 'api', 'rpc', 'file_requests.write');\n};\n\n/**\n * Delete a batch of closed file requests.\n * Route attributes:\n *   scope: file_requests.write\n * @function Dropbox#fileRequestsDelete\n * @arg {FileRequestsDeleteFileRequestArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FileRequestsDeleteFileRequestsResult>, DropboxResponseError.<FileRequestsDeleteFileRequestError>>}\n */\nroutes.fileRequestsDelete = function (arg) {\n  return this.request('file_requests/delete', arg, 'user', 'api', 'rpc', 'file_requests.write');\n};\n\n/**\n * Delete all closed file requests owned by this user.\n * Route attributes:\n *   scope: file_requests.write\n * @function Dropbox#fileRequestsDeleteAllClosed\n * @returns {Promise.<DropboxResponse<FileRequestsDeleteAllClosedFileRequestsResult>, DropboxResponseError.<FileRequestsDeleteAllClosedFileRequestsError>>}\n */\nroutes.fileRequestsDeleteAllClosed = function () {\n  return this.request('file_requests/delete_all_closed', null, 'user', 'api', 'rpc', 'file_requests.write');\n};\n\n/**\n * Returns the specified file request.\n * Route attributes:\n *   scope: file_requests.read\n * @function Dropbox#fileRequestsGet\n * @arg {FileRequestsGetFileRequestArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FileRequestsFileRequest>, DropboxResponseError.<FileRequestsGetFileRequestError>>}\n */\nroutes.fileRequestsGet = function (arg) {\n  return this.request('file_requests/get', arg, 'user', 'api', 'rpc', 'file_requests.read');\n};\n\n/**\n * Returns a list of file requests owned by this user. For apps with the app\n * folder permission, this will only return file requests with destinations in\n * the app folder.\n * Route attributes:\n *   scope: file_requests.read\n * @function Dropbox#fileRequestsListV2\n * @arg {FileRequestsListFileRequestsArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FileRequestsListFileRequestsV2Result>, DropboxResponseError.<FileRequestsListFileRequestsError>>}\n */\nroutes.fileRequestsListV2 = function (arg) {\n  return this.request('file_requests/list_v2', arg, 'user', 'api', 'rpc', 'file_requests.read');\n};\n\n/**\n * Returns a list of file requests owned by this user. For apps with the app\n * folder permission, this will only return file requests with destinations in\n * the app folder.\n * Route attributes:\n *   scope: file_requests.read\n * @function Dropbox#fileRequestsList\n * @returns {Promise.<DropboxResponse<FileRequestsListFileRequestsResult>, DropboxResponseError.<FileRequestsListFileRequestsError>>}\n */\nroutes.fileRequestsList = function () {\n  return this.request('file_requests/list', null, 'user', 'api', 'rpc', 'file_requests.read');\n};\n\n/**\n * Once a cursor has been retrieved from list_v2, use this to paginate through\n * all file requests. The cursor must come from a previous call to list_v2 or\n * list/continue.\n * Route attributes:\n *   scope: file_requests.read\n * @function Dropbox#fileRequestsListContinue\n * @arg {FileRequestsListFileRequestsContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FileRequestsListFileRequestsV2Result>, DropboxResponseError.<FileRequestsListFileRequestsContinueError>>}\n */\nroutes.fileRequestsListContinue = function (arg) {\n  return this.request('file_requests/list/continue', arg, 'user', 'api', 'rpc', 'file_requests.read');\n};\n\n/**\n * Update a file request.\n * Route attributes:\n *   scope: file_requests.write\n * @function Dropbox#fileRequestsUpdate\n * @arg {FileRequestsUpdateFileRequestArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FileRequestsFileRequest>, DropboxResponseError.<FileRequestsUpdateFileRequestError>>}\n */\nroutes.fileRequestsUpdate = function (arg) {\n  return this.request('file_requests/update', arg, 'user', 'api', 'rpc', 'file_requests.write');\n};\n\n/**\n * Returns the metadata for a file or folder. This is an alpha endpoint\n * compatible with the properties API. Note: Metadata for the root folder is\n * unsupported.\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#filesAlphaGetMetadata\n * @deprecated\n * @arg {FilesAlphaGetMetadataArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<(FilesFileMetadata|FilesFolderMetadata|FilesDeletedMetadata)>, DropboxResponseError.<FilesAlphaGetMetadataError>>}\n */\nroutes.filesAlphaGetMetadata = function (arg) {\n  return this.request('files/alpha/get_metadata', arg, 'user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * Create a new file with the contents provided in the request. Note that the\n * behavior of this alpha endpoint is unstable and subject to change. Do not use\n * this to upload a file larger than 150 MB. Instead, create an upload session\n * with upload_session/start.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesAlphaUpload\n * @deprecated\n * @arg {FilesUploadArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesFileMetadata>, DropboxResponseError.<FilesUploadError>>}\n */\nroutes.filesAlphaUpload = function (arg) {\n  return this.request('files/alpha/upload', arg, 'user', 'content', 'upload', 'files.content.write');\n};\n\n/**\n * Copy a file or folder to a different location in the user's Dropbox. If the\n * source path is a folder all its contents will be copied.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesCopyV2\n * @arg {FilesRelocationArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesRelocationResult>, DropboxResponseError.<FilesRelocationError>>}\n */\nroutes.filesCopyV2 = function (arg) {\n  return this.request('files/copy_v2', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Copy a file or folder to a different location in the user's Dropbox. If the\n * source path is a folder all its contents will be copied.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesCopy\n * @deprecated\n * @arg {FilesRelocationArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<(FilesFileMetadata|FilesFolderMetadata|FilesDeletedMetadata)>, DropboxResponseError.<FilesRelocationError>>}\n */\nroutes.filesCopy = function (arg) {\n  return this.request('files/copy', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Copy multiple files or folders to different locations at once in the user's\n * Dropbox. This route will replace copy_batch. The main difference is this\n * route will return status for each entry, while copy_batch raises failure if\n * any entry fails. This route will either finish synchronously, or return a job\n * ID and do the async copy job in background. Please use copy_batch/check_v2 to\n * check the job status.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesCopyBatchV2\n * @arg {Object} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesRelocationBatchV2Launch>, DropboxResponseError.<void>>}\n */\nroutes.filesCopyBatchV2 = function (arg) {\n  return this.request('files/copy_batch_v2', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Copy multiple files or folders to different locations at once in the user's\n * Dropbox. This route will return job ID immediately and do the async copy job\n * in background. Please use copy_batch/check to check the job status.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesCopyBatch\n * @deprecated\n * @arg {FilesRelocationBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesRelocationBatchLaunch>, DropboxResponseError.<void>>}\n */\nroutes.filesCopyBatch = function (arg) {\n  return this.request('files/copy_batch', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Returns the status of an asynchronous job for copy_batch_v2. It returns list\n * of results for each entry.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesCopyBatchCheckV2\n * @arg {AsyncPollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesRelocationBatchV2JobStatus>, DropboxResponseError.<AsyncPollError>>}\n */\nroutes.filesCopyBatchCheckV2 = function (arg) {\n  return this.request('files/copy_batch/check_v2', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Returns the status of an asynchronous job for copy_batch. If success, it\n * returns list of results for each entry.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesCopyBatchCheck\n * @deprecated\n * @arg {AsyncPollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesRelocationBatchJobStatus>, DropboxResponseError.<AsyncPollError>>}\n */\nroutes.filesCopyBatchCheck = function (arg) {\n  return this.request('files/copy_batch/check', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Get a copy reference to a file or folder. This reference string can be used\n * to save that file or folder to another user's Dropbox by passing it to\n * copy_reference/save.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesCopyReferenceGet\n * @arg {FilesGetCopyReferenceArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesGetCopyReferenceResult>, DropboxResponseError.<FilesGetCopyReferenceError>>}\n */\nroutes.filesCopyReferenceGet = function (arg) {\n  return this.request('files/copy_reference/get', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Save a copy reference returned by copy_reference/get to the user's Dropbox.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesCopyReferenceSave\n * @arg {FilesSaveCopyReferenceArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesSaveCopyReferenceResult>, DropboxResponseError.<FilesSaveCopyReferenceError>>}\n */\nroutes.filesCopyReferenceSave = function (arg) {\n  return this.request('files/copy_reference/save', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Create a folder at a given path.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesCreateFolderV2\n * @arg {FilesCreateFolderArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesCreateFolderResult>, DropboxResponseError.<FilesCreateFolderError>>}\n */\nroutes.filesCreateFolderV2 = function (arg) {\n  return this.request('files/create_folder_v2', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Create a folder at a given path.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesCreateFolder\n * @deprecated\n * @arg {FilesCreateFolderArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesFolderMetadata>, DropboxResponseError.<FilesCreateFolderError>>}\n */\nroutes.filesCreateFolder = function (arg) {\n  return this.request('files/create_folder', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Create multiple folders at once. This route is asynchronous for large\n * batches, which returns a job ID immediately and runs the create folder batch\n * asynchronously. Otherwise, creates the folders and returns the result\n * synchronously for smaller inputs. You can force asynchronous behaviour by\n * using the CreateFolderBatchArg.force_async flag.  Use\n * create_folder_batch/check to check the job status.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesCreateFolderBatch\n * @arg {FilesCreateFolderBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesCreateFolderBatchLaunch>, DropboxResponseError.<void>>}\n */\nroutes.filesCreateFolderBatch = function (arg) {\n  return this.request('files/create_folder_batch', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Returns the status of an asynchronous job for create_folder_batch. If\n * success, it returns list of result for each entry.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesCreateFolderBatchCheck\n * @arg {AsyncPollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesCreateFolderBatchJobStatus>, DropboxResponseError.<AsyncPollError>>}\n */\nroutes.filesCreateFolderBatchCheck = function (arg) {\n  return this.request('files/create_folder_batch/check', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Delete the file or folder at a given path. If the path is a folder, all its\n * contents will be deleted too. A successful response indicates that the file\n * or folder was deleted. The returned metadata will be the corresponding\n * FileMetadata or FolderMetadata for the item at time of deletion, and not a\n * DeletedMetadata object.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesDeleteV2\n * @arg {FilesDeleteArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesDeleteResult>, DropboxResponseError.<FilesDeleteError>>}\n */\nroutes.filesDeleteV2 = function (arg) {\n  return this.request('files/delete_v2', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Delete the file or folder at a given path. If the path is a folder, all its\n * contents will be deleted too. A successful response indicates that the file\n * or folder was deleted. The returned metadata will be the corresponding\n * FileMetadata or FolderMetadata for the item at time of deletion, and not a\n * DeletedMetadata object.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesDelete\n * @deprecated\n * @arg {FilesDeleteArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<(FilesFileMetadata|FilesFolderMetadata|FilesDeletedMetadata)>, DropboxResponseError.<FilesDeleteError>>}\n */\nroutes.filesDelete = function (arg) {\n  return this.request('files/delete', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Delete multiple files/folders at once. This route is asynchronous, which\n * returns a job ID immediately and runs the delete batch asynchronously. Use\n * delete_batch/check to check the job status.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesDeleteBatch\n * @arg {FilesDeleteBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesDeleteBatchLaunch>, DropboxResponseError.<void>>}\n */\nroutes.filesDeleteBatch = function (arg) {\n  return this.request('files/delete_batch', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Returns the status of an asynchronous job for delete_batch. If success, it\n * returns list of result for each entry.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesDeleteBatchCheck\n * @arg {AsyncPollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesDeleteBatchJobStatus>, DropboxResponseError.<AsyncPollError>>}\n */\nroutes.filesDeleteBatchCheck = function (arg) {\n  return this.request('files/delete_batch/check', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Download a file from a user's Dropbox.\n * Route attributes:\n *   scope: files.content.read\n * @function Dropbox#filesDownload\n * @arg {FilesDownloadArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesFileMetadata>, DropboxResponseError.<FilesDownloadError>>}\n */\nroutes.filesDownload = function (arg) {\n  return this.request('files/download', arg, 'user', 'content', 'download', 'files.content.read');\n};\n\n/**\n * Download a folder from the user's Dropbox, as a zip file. The folder must be\n * less than 20 GB in size and any single file within must be less than 4 GB in\n * size. The resulting zip must have fewer than 10,000 total file and folder\n * entries, including the top level folder. The input cannot be a single file.\n * Note: this endpoint does not support HTTP range requests.\n * Route attributes:\n *   scope: files.content.read\n * @function Dropbox#filesDownloadZip\n * @arg {FilesDownloadZipArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesDownloadZipResult>, DropboxResponseError.<FilesDownloadZipError>>}\n */\nroutes.filesDownloadZip = function (arg) {\n  return this.request('files/download_zip', arg, 'user', 'content', 'download', 'files.content.read');\n};\n\n/**\n * Export a file from a user's Dropbox. This route only supports exporting files\n * that cannot be downloaded directly  and whose ExportResult.file_metadata has\n * ExportInfo.export_as populated.\n * Route attributes:\n *   scope: files.content.read\n * @function Dropbox#filesExport\n * @arg {FilesExportArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesExportResult>, DropboxResponseError.<FilesExportError>>}\n */\nroutes.filesExport = function (arg) {\n  return this.request('files/export', arg, 'user', 'content', 'download', 'files.content.read');\n};\n\n/**\n * Return the lock metadata for the given list of paths.\n * Route attributes:\n *   scope: files.content.read\n * @function Dropbox#filesGetFileLockBatch\n * @arg {FilesLockFileBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesLockFileBatchResult>, DropboxResponseError.<FilesLockFileError>>}\n */\nroutes.filesGetFileLockBatch = function (arg) {\n  return this.request('files/get_file_lock_batch', arg, 'user', 'api', 'rpc', 'files.content.read');\n};\n\n/**\n * Returns the metadata for a file or folder. Note: Metadata for the root folder\n * is unsupported.\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#filesGetMetadata\n * @arg {FilesGetMetadataArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<(FilesFileMetadata|FilesFolderMetadata|FilesDeletedMetadata)>, DropboxResponseError.<FilesGetMetadataError>>}\n */\nroutes.filesGetMetadata = function (arg) {\n  return this.request('files/get_metadata', arg, 'user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * Get a preview for a file. Currently, PDF previews are generated for files\n * with the following extensions: .ai, .doc, .docm, .docx, .eps, .gdoc,\n * .gslides, .odp, .odt, .pps, .ppsm, .ppsx, .ppt, .pptm, .pptx, .rtf. HTML\n * previews are generated for files with the following extensions: .csv, .ods,\n * .xls, .xlsm, .gsheet, .xlsx. Other formats will return an unsupported\n * extension error.\n * Route attributes:\n *   scope: files.content.read\n * @function Dropbox#filesGetPreview\n * @arg {FilesPreviewArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesFileMetadata>, DropboxResponseError.<FilesPreviewError>>}\n */\nroutes.filesGetPreview = function (arg) {\n  return this.request('files/get_preview', arg, 'user', 'content', 'download', 'files.content.read');\n};\n\n/**\n * Get a temporary link to stream content of a file. This link will expire in\n * four hours and afterwards you will get 410 Gone. This URL should not be used\n * to display content directly in the browser. The Content-Type of the link is\n * determined automatically by the file's mime type.\n * Route attributes:\n *   scope: files.content.read\n * @function Dropbox#filesGetTemporaryLink\n * @arg {FilesGetTemporaryLinkArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesGetTemporaryLinkResult>, DropboxResponseError.<FilesGetTemporaryLinkError>>}\n */\nroutes.filesGetTemporaryLink = function (arg) {\n  return this.request('files/get_temporary_link', arg, 'user', 'api', 'rpc', 'files.content.read');\n};\n\n/**\n * Get a one-time use temporary upload link to upload a file to a Dropbox\n * location.  This endpoint acts as a delayed upload. The returned temporary\n * upload link may be used to make a POST request with the data to be uploaded.\n * The upload will then be perfomed with the CommitInfo previously provided to\n * get_temporary_upload_link but evaluated only upon consumption. Hence, errors\n * stemming from invalid CommitInfo with respect to the state of the user's\n * Dropbox will only be communicated at consumption time. Additionally, these\n * errors are surfaced as generic HTTP 409 Conflict responses, potentially\n * hiding issue details. The maximum temporary upload link duration is 4 hours.\n * Upon consumption or expiration, a new link will have to be generated.\n * Multiple links may exist for a specific upload path at any given time.  The\n * POST request on the temporary upload link must have its Content-Type set to\n * \"application/octet-stream\".  Example temporary upload link consumption\n * request:  curl -X POST\n * https://content.dropboxapi.com/apitul/1/bNi2uIYF51cVBND --header\n * \"Content-Type: application/octet-stream\" --data-binary @local_file.txt  A\n * successful temporary upload link consumption request returns the content hash\n * of the uploaded data in JSON format.  Example successful temporary upload\n * link consumption response: {\"content-hash\":\n * \"599d71033d700ac892a0e48fa61b125d2f5994\"}  An unsuccessful temporary upload\n * link consumption request returns any of the following status codes:  HTTP 400\n * Bad Request: Content-Type is not one of application/octet-stream and\n * text/plain or request is invalid. HTTP 409 Conflict: The temporary upload\n * link does not exist or is currently unavailable, the upload failed, or\n * another error happened. HTTP 410 Gone: The temporary upload link is expired\n * or consumed.  Example unsuccessful temporary upload link consumption\n * response: Temporary upload link has been recently consumed.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesGetTemporaryUploadLink\n * @arg {FilesGetTemporaryUploadLinkArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesGetTemporaryUploadLinkResult>, DropboxResponseError.<void>>}\n */\nroutes.filesGetTemporaryUploadLink = function (arg) {\n  return this.request('files/get_temporary_upload_link', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Get a thumbnail for an image. This method currently supports files with the\n * following file extensions: jpg, jpeg, png, tiff, tif, gif, webp, ppm and bmp.\n * Photos that are larger than 20MB in size won't be converted to a thumbnail.\n * Route attributes:\n *   scope: files.content.read\n * @function Dropbox#filesGetThumbnail\n * @arg {FilesThumbnailArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesFileMetadata>, DropboxResponseError.<FilesThumbnailError>>}\n */\nroutes.filesGetThumbnail = function (arg) {\n  return this.request('files/get_thumbnail', arg, 'user', 'content', 'download', 'files.content.read');\n};\n\n/**\n * Get a thumbnail for an image. This method currently supports files with the\n * following file extensions: jpg, jpeg, png, tiff, tif, gif, webp, ppm and bmp.\n * Photos that are larger than 20MB in size won't be converted to a thumbnail.\n * Route attributes:\n *   scope: files.content.read\n * @function Dropbox#filesGetThumbnailV2\n * @arg {FilesThumbnailV2Arg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesPreviewResult>, DropboxResponseError.<FilesThumbnailV2Error>>}\n */\nroutes.filesGetThumbnailV2 = function (arg) {\n  return this.request('files/get_thumbnail_v2', arg, 'app, user', 'content', 'download', 'files.content.read');\n};\n\n/**\n * Get thumbnails for a list of images. We allow up to 25 thumbnails in a single\n * batch. This method currently supports files with the following file\n * extensions: jpg, jpeg, png, tiff, tif, gif, webp, ppm and bmp. Photos that\n * are larger than 20MB in size won't be converted to a thumbnail.\n * Route attributes:\n *   scope: files.content.read\n * @function Dropbox#filesGetThumbnailBatch\n * @arg {FilesGetThumbnailBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesGetThumbnailBatchResult>, DropboxResponseError.<FilesGetThumbnailBatchError>>}\n */\nroutes.filesGetThumbnailBatch = function (arg) {\n  return this.request('files/get_thumbnail_batch', arg, 'user', 'content', 'rpc', 'files.content.read');\n};\n\n/**\n * Starts returning the contents of a folder. If the result's\n * ListFolderResult.has_more field is true, call list_folder/continue with the\n * returned ListFolderResult.cursor to retrieve more entries. If you're using\n * ListFolderArg.recursive set to true to keep a local cache of the contents of\n * a Dropbox account, iterate through each entry in order and process them as\n * follows to keep your local state in sync: For each FileMetadata, store the\n * new entry at the given path in your local state. If the required parent\n * folders don't exist yet, create them. If there's already something else at\n * the given path, replace it and remove all its children. For each\n * FolderMetadata, store the new entry at the given path in your local state. If\n * the required parent folders don't exist yet, create them. If there's already\n * something else at the given path, replace it but leave the children as they\n * are. Check the new entry's FolderSharingInfo.read_only and set all its\n * children's read-only statuses to match. For each DeletedMetadata, if your\n * local state has something at the given path, remove it and all its children.\n * If there's nothing at the given path, ignore this entry. Note:\n * auth.RateLimitError may be returned if multiple list_folder or\n * list_folder/continue calls with same parameters are made simultaneously by\n * same API app for same user. If your app implements retry logic, please hold\n * off the retry until the previous request finishes.\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#filesListFolder\n * @arg {FilesListFolderArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesListFolderResult>, DropboxResponseError.<FilesListFolderError>>}\n */\nroutes.filesListFolder = function (arg) {\n  return this.request('files/list_folder', arg, 'app, user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * Once a cursor has been retrieved from list_folder, use this to paginate\n * through all files and retrieve updates to the folder, following the same\n * rules as documented for list_folder.\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#filesListFolderContinue\n * @arg {FilesListFolderContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesListFolderResult>, DropboxResponseError.<FilesListFolderContinueError>>}\n */\nroutes.filesListFolderContinue = function (arg) {\n  return this.request('files/list_folder/continue', arg, 'app, user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * A way to quickly get a cursor for the folder's state. Unlike list_folder,\n * list_folder/get_latest_cursor doesn't return any entries. This endpoint is\n * for app which only needs to know about new files and modifications and\n * doesn't need to know about files that already exist in Dropbox.\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#filesListFolderGetLatestCursor\n * @arg {FilesListFolderArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesListFolderGetLatestCursorResult>, DropboxResponseError.<FilesListFolderError>>}\n */\nroutes.filesListFolderGetLatestCursor = function (arg) {\n  return this.request('files/list_folder/get_latest_cursor', arg, 'user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * A longpoll endpoint to wait for changes on an account. In conjunction with\n * list_folder/continue, this call gives you a low-latency way to monitor an\n * account for file changes. The connection will block until there are changes\n * available or a timeout occurs. This endpoint is useful mostly for client-side\n * apps. If you're looking for server-side notifications, check out our webhooks\n * documentation https://www.dropbox.com/developers/reference/webhooks.\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#filesListFolderLongpoll\n * @arg {FilesListFolderLongpollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesListFolderLongpollResult>, DropboxResponseError.<FilesListFolderLongpollError>>}\n */\nroutes.filesListFolderLongpoll = function (arg) {\n  return this.request('files/list_folder/longpoll', arg, 'noauth', 'notify', 'rpc', 'files.metadata.read');\n};\n\n/**\n * Returns revisions for files based on a file path or a file id. The file path\n * or file id is identified from the latest file entry at the given file path or\n * id. This end point allows your app to query either by file path or file id by\n * setting the mode parameter appropriately. In the ListRevisionsMode.path\n * (default) mode, all revisions at the same file path as the latest file entry\n * are returned. If revisions with the same file id are desired, then mode must\n * be set to ListRevisionsMode.id. The ListRevisionsMode.id mode is useful to\n * retrieve revisions for a given file across moves or renames.\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#filesListRevisions\n * @arg {FilesListRevisionsArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesListRevisionsResult>, DropboxResponseError.<FilesListRevisionsError>>}\n */\nroutes.filesListRevisions = function (arg) {\n  return this.request('files/list_revisions', arg, 'user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * Lock the files at the given paths. A locked file will be writable only by the\n * lock holder. A successful response indicates that the file has been locked.\n * Returns a list of the locked file paths and their metadata after this\n * operation.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesLockFileBatch\n * @arg {FilesLockFileBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesLockFileBatchResult>, DropboxResponseError.<FilesLockFileError>>}\n */\nroutes.filesLockFileBatch = function (arg) {\n  return this.request('files/lock_file_batch', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Move a file or folder to a different location in the user's Dropbox. If the\n * source path is a folder all its contents will be moved. Note that we do not\n * currently support case-only renaming.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesMoveV2\n * @arg {FilesRelocationArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesRelocationResult>, DropboxResponseError.<FilesRelocationError>>}\n */\nroutes.filesMoveV2 = function (arg) {\n  return this.request('files/move_v2', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Move a file or folder to a different location in the user's Dropbox. If the\n * source path is a folder all its contents will be moved.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesMove\n * @deprecated\n * @arg {FilesRelocationArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<(FilesFileMetadata|FilesFolderMetadata|FilesDeletedMetadata)>, DropboxResponseError.<FilesRelocationError>>}\n */\nroutes.filesMove = function (arg) {\n  return this.request('files/move', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Move multiple files or folders to different locations at once in the user's\n * Dropbox. Note that we do not currently support case-only renaming. This route\n * will replace move_batch. The main difference is this route will return status\n * for each entry, while move_batch raises failure if any entry fails. This\n * route will either finish synchronously, or return a job ID and do the async\n * move job in background. Please use move_batch/check_v2 to check the job\n * status.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesMoveBatchV2\n * @arg {FilesMoveBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesRelocationBatchV2Launch>, DropboxResponseError.<void>>}\n */\nroutes.filesMoveBatchV2 = function (arg) {\n  return this.request('files/move_batch_v2', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Move multiple files or folders to different locations at once in the user's\n * Dropbox. This route will return job ID immediately and do the async moving\n * job in background. Please use move_batch/check to check the job status.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesMoveBatch\n * @deprecated\n * @arg {FilesRelocationBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesRelocationBatchLaunch>, DropboxResponseError.<void>>}\n */\nroutes.filesMoveBatch = function (arg) {\n  return this.request('files/move_batch', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Returns the status of an asynchronous job for move_batch_v2. It returns list\n * of results for each entry.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesMoveBatchCheckV2\n * @arg {AsyncPollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesRelocationBatchV2JobStatus>, DropboxResponseError.<AsyncPollError>>}\n */\nroutes.filesMoveBatchCheckV2 = function (arg) {\n  return this.request('files/move_batch/check_v2', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Returns the status of an asynchronous job for move_batch. If success, it\n * returns list of results for each entry.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesMoveBatchCheck\n * @deprecated\n * @arg {AsyncPollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesRelocationBatchJobStatus>, DropboxResponseError.<AsyncPollError>>}\n */\nroutes.filesMoveBatchCheck = function (arg) {\n  return this.request('files/move_batch/check', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Creates a new Paper doc with the provided content.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesPaperCreate\n * @arg {FilesPaperCreateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesPaperCreateResult>, DropboxResponseError.<FilesPaperCreateError>>}\n */\nroutes.filesPaperCreate = function (arg) {\n  return this.request('files/paper/create', arg, 'user', 'api', 'upload', 'files.content.write');\n};\n\n/**\n * Updates an existing Paper doc with the provided content.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesPaperUpdate\n * @arg {FilesPaperUpdateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesPaperUpdateResult>, DropboxResponseError.<FilesPaperUpdateError>>}\n */\nroutes.filesPaperUpdate = function (arg) {\n  return this.request('files/paper/update', arg, 'user', 'api', 'upload', 'files.content.write');\n};\n\n/**\n * Permanently delete the file or folder at a given path (see\n * https://www.dropbox.com/en/help/40). If the given file or folder is not yet\n * deleted, this route will first delete it. It is possible for this route to\n * successfully delete, then fail to permanently delete. Note: This endpoint is\n * only available for Dropbox Business apps.\n * Route attributes:\n *   scope: files.permanent_delete\n * @function Dropbox#filesPermanentlyDelete\n * @arg {FilesDeleteArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<FilesDeleteError>>}\n */\nroutes.filesPermanentlyDelete = function (arg) {\n  return this.request('files/permanently_delete', arg, 'user', 'api', 'rpc', 'files.permanent_delete');\n};\n\n/**\n * Route attributes:\n *   scope: files.metadata.write\n * @function Dropbox#filesPropertiesAdd\n * @deprecated\n * @arg {FilePropertiesAddPropertiesArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<FilePropertiesAddPropertiesError>>}\n */\nroutes.filesPropertiesAdd = function (arg) {\n  return this.request('files/properties/add', arg, 'user', 'api', 'rpc', 'files.metadata.write');\n};\n\n/**\n * Route attributes:\n *   scope: files.metadata.write\n * @function Dropbox#filesPropertiesOverwrite\n * @deprecated\n * @arg {FilePropertiesOverwritePropertyGroupArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<FilePropertiesInvalidPropertyGroupError>>}\n */\nroutes.filesPropertiesOverwrite = function (arg) {\n  return this.request('files/properties/overwrite', arg, 'user', 'api', 'rpc', 'files.metadata.write');\n};\n\n/**\n * Route attributes:\n *   scope: files.metadata.write\n * @function Dropbox#filesPropertiesRemove\n * @deprecated\n * @arg {FilePropertiesRemovePropertiesArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<FilePropertiesRemovePropertiesError>>}\n */\nroutes.filesPropertiesRemove = function (arg) {\n  return this.request('files/properties/remove', arg, 'user', 'api', 'rpc', 'files.metadata.write');\n};\n\n/**\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#filesPropertiesTemplateGet\n * @deprecated\n * @arg {FilePropertiesGetTemplateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilePropertiesGetTemplateResult>, DropboxResponseError.<FilePropertiesTemplateError>>}\n */\nroutes.filesPropertiesTemplateGet = function (arg) {\n  return this.request('files/properties/template/get', arg, 'user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#filesPropertiesTemplateList\n * @deprecated\n * @returns {Promise.<DropboxResponse<FilePropertiesListTemplateResult>, DropboxResponseError.<FilePropertiesTemplateError>>}\n */\nroutes.filesPropertiesTemplateList = function () {\n  return this.request('files/properties/template/list', null, 'user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * Route attributes:\n *   scope: files.metadata.write\n * @function Dropbox#filesPropertiesUpdate\n * @deprecated\n * @arg {FilePropertiesUpdatePropertiesArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<FilePropertiesUpdatePropertiesError>>}\n */\nroutes.filesPropertiesUpdate = function (arg) {\n  return this.request('files/properties/update', arg, 'user', 'api', 'rpc', 'files.metadata.write');\n};\n\n/**\n * Restore a specific revision of a file to the given path.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesRestore\n * @arg {FilesRestoreArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesFileMetadata>, DropboxResponseError.<FilesRestoreError>>}\n */\nroutes.filesRestore = function (arg) {\n  return this.request('files/restore', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Save the data from a specified URL into a file in user's Dropbox. Note that\n * the transfer from the URL must complete within 5 minutes, or the operation\n * will time out and the job will fail. If the given path already exists, the\n * file will be renamed to avoid the conflict (e.g. myfile (1).txt).\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesSaveUrl\n * @arg {FilesSaveUrlArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesSaveUrlResult>, DropboxResponseError.<FilesSaveUrlError>>}\n */\nroutes.filesSaveUrl = function (arg) {\n  return this.request('files/save_url', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Check the status of a save_url job.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesSaveUrlCheckJobStatus\n * @arg {AsyncPollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesSaveUrlJobStatus>, DropboxResponseError.<AsyncPollError>>}\n */\nroutes.filesSaveUrlCheckJobStatus = function (arg) {\n  return this.request('files/save_url/check_job_status', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Searches for files and folders. Note: Recent changes will be reflected in\n * search results within a few seconds and older revisions of existing files may\n * still match your query for up to a few days.\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#filesSearch\n * @deprecated\n * @arg {FilesSearchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesSearchResult>, DropboxResponseError.<FilesSearchError>>}\n */\nroutes.filesSearch = function (arg) {\n  return this.request('files/search', arg, 'user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * Searches for files and folders. Note: search_v2 along with search/continue_v2\n * can only be used to retrieve a maximum of 10,000 matches. Recent changes may\n * not immediately be reflected in search results due to a short delay in\n * indexing. Duplicate results may be returned across pages. Some results may\n * not be returned.\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#filesSearchV2\n * @arg {FilesSearchV2Arg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesSearchV2Result>, DropboxResponseError.<FilesSearchError>>}\n */\nroutes.filesSearchV2 = function (arg) {\n  return this.request('files/search_v2', arg, 'user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * Fetches the next page of search results returned from search_v2. Note:\n * search_v2 along with search/continue_v2 can only be used to retrieve a\n * maximum of 10,000 matches. Recent changes may not immediately be reflected in\n * search results due to a short delay in indexing. Duplicate results may be\n * returned across pages. Some results may not be returned.\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#filesSearchContinueV2\n * @arg {FilesSearchV2ContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesSearchV2Result>, DropboxResponseError.<FilesSearchError>>}\n */\nroutes.filesSearchContinueV2 = function (arg) {\n  return this.request('files/search/continue_v2', arg, 'user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * Add a tag to an item. A tag is a string. The strings are automatically\n * converted to lowercase letters. No more than 20 tags can be added to a given\n * item.\n * Route attributes:\n *   scope: files.metadata.write\n * @function Dropbox#filesTagsAdd\n * @arg {FilesAddTagArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<FilesAddTagError>>}\n */\nroutes.filesTagsAdd = function (arg) {\n  return this.request('files/tags/add', arg, 'user', 'api', 'rpc', 'files.metadata.write');\n};\n\n/**\n * Get list of tags assigned to items.\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#filesTagsGet\n * @arg {FilesGetTagsArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesGetTagsResult>, DropboxResponseError.<FilesBaseTagError>>}\n */\nroutes.filesTagsGet = function (arg) {\n  return this.request('files/tags/get', arg, 'user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * Remove a tag from an item.\n * Route attributes:\n *   scope: files.metadata.write\n * @function Dropbox#filesTagsRemove\n * @arg {FilesRemoveTagArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<FilesRemoveTagError>>}\n */\nroutes.filesTagsRemove = function (arg) {\n  return this.request('files/tags/remove', arg, 'user', 'api', 'rpc', 'files.metadata.write');\n};\n\n/**\n * Unlock the files at the given paths. A locked file can only be unlocked by\n * the lock holder or, if a business account, a team admin. A successful\n * response indicates that the file has been unlocked. Returns a list of the\n * unlocked file paths and their metadata after this operation.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesUnlockFileBatch\n * @arg {FilesUnlockFileBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesLockFileBatchResult>, DropboxResponseError.<FilesLockFileError>>}\n */\nroutes.filesUnlockFileBatch = function (arg) {\n  return this.request('files/unlock_file_batch', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Create a new file with the contents provided in the request. Do not use this\n * to upload a file larger than 150 MB. Instead, create an upload session with\n * upload_session/start. Calls to this endpoint will count as data transport\n * calls for any Dropbox Business teams with a limit on the number of data\n * transport calls allowed per month. For more information, see the Data\n * transport limit page\n * https://www.dropbox.com/developers/reference/data-transport-limit.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesUpload\n * @arg {FilesUploadArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesFileMetadata>, DropboxResponseError.<FilesUploadError>>}\n */\nroutes.filesUpload = function (arg) {\n  return this.request('files/upload', arg, 'user', 'content', 'upload', 'files.content.write');\n};\n\n/**\n * Append more data to an upload session. When the parameter close is set, this\n * call will close the session. A single request should not upload more than 150\n * MB. The maximum size of a file one can upload to an upload session is 350 GB.\n * Calls to this endpoint will count as data transport calls for any Dropbox\n * Business teams with a limit on the number of data transport calls allowed per\n * month. For more information, see the Data transport limit page\n * https://www.dropbox.com/developers/reference/data-transport-limit.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesUploadSessionAppendV2\n * @arg {FilesUploadSessionAppendArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<FilesUploadSessionAppendError>>}\n */\nroutes.filesUploadSessionAppendV2 = function (arg) {\n  return this.request('files/upload_session/append_v2', arg, 'user', 'content', 'upload', 'files.content.write');\n};\n\n/**\n * Append more data to an upload session. A single request should not upload\n * more than 150 MB. The maximum size of a file one can upload to an upload\n * session is 350 GB. Calls to this endpoint will count as data transport calls\n * for any Dropbox Business teams with a limit on the number of data transport\n * calls allowed per month. For more information, see the Data transport limit\n * page https://www.dropbox.com/developers/reference/data-transport-limit.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesUploadSessionAppend\n * @deprecated\n * @arg {FilesUploadSessionCursor} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<FilesUploadSessionAppendError>>}\n */\nroutes.filesUploadSessionAppend = function (arg) {\n  return this.request('files/upload_session/append', arg, 'user', 'content', 'upload', 'files.content.write');\n};\n\n/**\n * Finish an upload session and save the uploaded data to the given file path. A\n * single request should not upload more than 150 MB. The maximum size of a file\n * one can upload to an upload session is 350 GB. Calls to this endpoint will\n * count as data transport calls for any Dropbox Business teams with a limit on\n * the number of data transport calls allowed per month. For more information,\n * see the Data transport limit page\n * https://www.dropbox.com/developers/reference/data-transport-limit.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesUploadSessionFinish\n * @arg {FilesUploadSessionFinishArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesFileMetadata>, DropboxResponseError.<FilesUploadSessionFinishError>>}\n */\nroutes.filesUploadSessionFinish = function (arg) {\n  return this.request('files/upload_session/finish', arg, 'user', 'content', 'upload', 'files.content.write');\n};\n\n/**\n * This route helps you commit many files at once into a user's Dropbox. Use\n * upload_session/start and upload_session/append_v2 to upload file contents. We\n * recommend uploading many files in parallel to increase throughput. Once the\n * file contents have been uploaded, rather than calling upload_session/finish,\n * use this route to finish all your upload sessions in a single request.\n * UploadSessionStartArg.close or UploadSessionAppendArg.close needs to be true\n * for the last upload_session/start or upload_session/append_v2 call. The\n * maximum size of a file one can upload to an upload session is 350 GB. This\n * route will return a job_id immediately and do the async commit job in\n * background. Use upload_session/finish_batch/check to check the job status.\n * For the same account, this route should be executed serially. That means you\n * should not start the next job before current job finishes. We allow up to\n * 1000 entries in a single request. Calls to this endpoint will count as data\n * transport calls for any Dropbox Business teams with a limit on the number of\n * data transport calls allowed per month. For more information, see the Data\n * transport limit page\n * https://www.dropbox.com/developers/reference/data-transport-limit.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesUploadSessionFinishBatch\n * @deprecated\n * @arg {FilesUploadSessionFinishBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesUploadSessionFinishBatchLaunch>, DropboxResponseError.<void>>}\n */\nroutes.filesUploadSessionFinishBatch = function (arg) {\n  return this.request('files/upload_session/finish_batch', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * This route helps you commit many files at once into a user's Dropbox. Use\n * upload_session/start and upload_session/append_v2 to upload file contents. We\n * recommend uploading many files in parallel to increase throughput. Once the\n * file contents have been uploaded, rather than calling upload_session/finish,\n * use this route to finish all your upload sessions in a single request.\n * UploadSessionStartArg.close or UploadSessionAppendArg.close needs to be true\n * for the last upload_session/start or upload_session/append_v2 call of each\n * upload session. The maximum size of a file one can upload to an upload\n * session is 350 GB. We allow up to 1000 entries in a single request. Calls to\n * this endpoint will count as data transport calls for any Dropbox Business\n * teams with a limit on the number of data transport calls allowed per month.\n * For more information, see the Data transport limit page\n * https://www.dropbox.com/developers/reference/data-transport-limit.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesUploadSessionFinishBatchV2\n * @arg {FilesUploadSessionFinishBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesUploadSessionFinishBatchResult>, DropboxResponseError.<void>>}\n */\nroutes.filesUploadSessionFinishBatchV2 = function (arg) {\n  return this.request('files/upload_session/finish_batch_v2', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Returns the status of an asynchronous job for upload_session/finish_batch. If\n * success, it returns list of result for each entry.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesUploadSessionFinishBatchCheck\n * @arg {AsyncPollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesUploadSessionFinishBatchJobStatus>, DropboxResponseError.<AsyncPollError>>}\n */\nroutes.filesUploadSessionFinishBatchCheck = function (arg) {\n  return this.request('files/upload_session/finish_batch/check', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Upload sessions allow you to upload a single file in one or more requests,\n * for example where the size of the file is greater than 150 MB.  This call\n * starts a new upload session with the given data. You can then use\n * upload_session/append_v2 to add more data and upload_session/finish to save\n * all the data to a file in Dropbox. A single request should not upload more\n * than 150 MB. The maximum size of a file one can upload to an upload session\n * is 350 GB. An upload session can be used for a maximum of 7 days. Attempting\n * to use an UploadSessionStartResult.session_id with upload_session/append_v2\n * or upload_session/finish more than 7 days after its creation will return a\n * UploadSessionLookupError.not_found. Calls to this endpoint will count as data\n * transport calls for any Dropbox Business teams with a limit on the number of\n * data transport calls allowed per month. For more information, see the Data\n * transport limit page\n * https://www.dropbox.com/developers/reference/data-transport-limit. By\n * default, upload sessions require you to send content of the file in\n * sequential order via consecutive upload_session/start,\n * upload_session/append_v2, upload_session/finish calls. For better\n * performance, you can instead optionally use a UploadSessionType.concurrent\n * upload session. To start a new concurrent session, set\n * UploadSessionStartArg.session_type to UploadSessionType.concurrent. After\n * that, you can send file data in concurrent upload_session/append_v2 requests.\n * Finally finish the session with upload_session/finish. There are couple of\n * constraints with concurrent sessions to make them work. You can not send data\n * with upload_session/start or upload_session/finish call, only with\n * upload_session/append_v2 call. Also data uploaded in upload_session/append_v2\n * call must be multiple of 4194304 bytes (except for last\n * upload_session/append_v2 with UploadSessionStartArg.close to true, that may\n * contain any remaining data).\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesUploadSessionStart\n * @arg {FilesUploadSessionStartArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesUploadSessionStartResult>, DropboxResponseError.<FilesUploadSessionStartError>>}\n */\nroutes.filesUploadSessionStart = function (arg) {\n  return this.request('files/upload_session/start', arg, 'user', 'content', 'upload', 'files.content.write');\n};\n\n/**\n * This route starts batch of upload_sessions. Please refer to\n * `upload_session/start` usage. Calls to this endpoint will count as data\n * transport calls for any Dropbox Business teams with a limit on the number of\n * data transport calls allowed per month. For more information, see the Data\n * transport limit page\n * https://www.dropbox.com/developers/reference/data-transport-limit.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#filesUploadSessionStartBatch\n * @arg {FilesUploadSessionStartBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilesUploadSessionStartBatchResult>, DropboxResponseError.<void>>}\n */\nroutes.filesUploadSessionStartBatch = function (arg) {\n  return this.request('files/upload_session/start_batch', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * This route is used for refreshing the info that is found in the id_token\n * during the OIDC flow. This route doesn't require any arguments and will use\n * the scopes approved for the given access token.\n * Route attributes:\n *   scope: openid\n * @function Dropbox#openidUserinfo\n * @arg {OpenidUserInfoArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<OpenidUserInfoResult>, DropboxResponseError.<OpenidUserInfoError>>}\n */\nroutes.openidUserinfo = function (arg) {\n  return this.request('openid/userinfo', arg, 'user', 'api', 'rpc', 'openid');\n};\n\n/**\n * Marks the given Paper doc as archived. This action can be performed or undone\n * by anyone with edit permissions to the doc. Note that this endpoint will\n * continue to work for content created by users on the older version of Paper.\n * To check which version of Paper a user is on, use /users/features/get_values.\n * If the paper_as_files feature is enabled, then the user is running the new\n * version of Paper. This endpoint will be retired in September 2020. Refer to\n * the Paper Migration Guide\n * https://www.dropbox.com/lp/developers/reference/paper-migration-guide for\n * more information.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#paperDocsArchive\n * @deprecated\n * @arg {PaperRefPaperDoc} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<PaperDocLookupError>>}\n */\nroutes.paperDocsArchive = function (arg) {\n  return this.request('paper/docs/archive', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Creates a new Paper doc with the provided content. Note that this endpoint\n * will continue to work for content created by users on the older version of\n * Paper. To check which version of Paper a user is on, use\n * /users/features/get_values. If the paper_as_files feature is enabled, then\n * the user is running the new version of Paper. This endpoint will be retired\n * in September 2020. Refer to the Paper Migration Guide\n * https://www.dropbox.com/lp/developers/reference/paper-migration-guide for\n * more information.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#paperDocsCreate\n * @deprecated\n * @arg {PaperPaperDocCreateArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<PaperPaperDocCreateUpdateResult>, DropboxResponseError.<PaperPaperDocCreateError>>}\n */\nroutes.paperDocsCreate = function (arg) {\n  return this.request('paper/docs/create', arg, 'user', 'api', 'upload', 'files.content.write');\n};\n\n/**\n * Exports and downloads Paper doc either as HTML or markdown. Note that this\n * endpoint will continue to work for content created by users on the older\n * version of Paper. To check which version of Paper a user is on, use\n * /users/features/get_values. If the paper_as_files feature is enabled, then\n * the user is running the new version of Paper. Refer to the Paper Migration\n * Guide https://www.dropbox.com/lp/developers/reference/paper-migration-guide\n * for migration information.\n * Route attributes:\n *   scope: files.content.read\n * @function Dropbox#paperDocsDownload\n * @deprecated\n * @arg {PaperPaperDocExport} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<PaperPaperDocExportResult>, DropboxResponseError.<PaperDocLookupError>>}\n */\nroutes.paperDocsDownload = function (arg) {\n  return this.request('paper/docs/download', arg, 'user', 'api', 'download', 'files.content.read');\n};\n\n/**\n * Lists the users who are explicitly invited to the Paper folder in which the\n * Paper doc is contained. For private folders all users (including owner)\n * shared on the folder are listed and for team folders all non-team users\n * shared on the folder are returned. Note that this endpoint will continue to\n * work for content created by users on the older version of Paper. To check\n * which version of Paper a user is on, use /users/features/get_values. If the\n * paper_as_files feature is enabled, then the user is running the new version\n * of Paper. Refer to the Paper Migration Guide\n * https://www.dropbox.com/lp/developers/reference/paper-migration-guide for\n * migration information.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#paperDocsFolderUsersList\n * @deprecated\n * @arg {PaperListUsersOnFolderArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<PaperListUsersOnFolderResponse>, DropboxResponseError.<PaperDocLookupError>>}\n */\nroutes.paperDocsFolderUsersList = function (arg) {\n  return this.request('paper/docs/folder_users/list', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Once a cursor has been retrieved from docs/folder_users/list, use this to\n * paginate through all users on the Paper folder. Note that this endpoint will\n * continue to work for content created by users on the older version of Paper.\n * To check which version of Paper a user is on, use /users/features/get_values.\n * If the paper_as_files feature is enabled, then the user is running the new\n * version of Paper. Refer to the Paper Migration Guide\n * https://www.dropbox.com/lp/developers/reference/paper-migration-guide for\n * migration information.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#paperDocsFolderUsersListContinue\n * @deprecated\n * @arg {PaperListUsersOnFolderContinueArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<PaperListUsersOnFolderResponse>, DropboxResponseError.<PaperListUsersCursorError>>}\n */\nroutes.paperDocsFolderUsersListContinue = function (arg) {\n  return this.request('paper/docs/folder_users/list/continue', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Retrieves folder information for the given Paper doc. This includes:   -\n * folder sharing policy; permissions for subfolders are set by the top-level\n * folder.   - full 'filepath', i.e. the list of folders (both folderId and\n * folderName) from     the root folder to the folder directly containing the\n * Paper doc.  If the Paper doc is not in any folder (aka unfiled) the response\n * will be empty. Note that this endpoint will continue to work for content\n * created by users on the older version of Paper. To check which version of\n * Paper a user is on, use /users/features/get_values. If the paper_as_files\n * feature is enabled, then the user is running the new version of Paper. Refer\n * to the Paper Migration Guide\n * https://www.dropbox.com/lp/developers/reference/paper-migration-guide for\n * migration information.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#paperDocsGetFolderInfo\n * @deprecated\n * @arg {PaperRefPaperDoc} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<PaperFoldersContainingPaperDoc>, DropboxResponseError.<PaperDocLookupError>>}\n */\nroutes.paperDocsGetFolderInfo = function (arg) {\n  return this.request('paper/docs/get_folder_info', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Return the list of all Paper docs according to the argument specifications.\n * To iterate over through the full pagination, pass the cursor to\n * docs/list/continue. Note that this endpoint will continue to work for content\n * created by users on the older version of Paper. To check which version of\n * Paper a user is on, use /users/features/get_values. If the paper_as_files\n * feature is enabled, then the user is running the new version of Paper. Refer\n * to the Paper Migration Guide\n * https://www.dropbox.com/lp/developers/reference/paper-migration-guide for\n * migration information.\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#paperDocsList\n * @deprecated\n * @arg {PaperListPaperDocsArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<PaperListPaperDocsResponse>, DropboxResponseError.<void>>}\n */\nroutes.paperDocsList = function (arg) {\n  return this.request('paper/docs/list', arg, 'user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * Once a cursor has been retrieved from docs/list, use this to paginate through\n * all Paper doc. Note that this endpoint will continue to work for content\n * created by users on the older version of Paper. To check which version of\n * Paper a user is on, use /users/features/get_values. If the paper_as_files\n * feature is enabled, then the user is running the new version of Paper. Refer\n * to the Paper Migration Guide\n * https://www.dropbox.com/lp/developers/reference/paper-migration-guide for\n * migration information.\n * Route attributes:\n *   scope: files.metadata.read\n * @function Dropbox#paperDocsListContinue\n * @deprecated\n * @arg {PaperListPaperDocsContinueArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<PaperListPaperDocsResponse>, DropboxResponseError.<PaperListDocsCursorError>>}\n */\nroutes.paperDocsListContinue = function (arg) {\n  return this.request('paper/docs/list/continue', arg, 'user', 'api', 'rpc', 'files.metadata.read');\n};\n\n/**\n * Permanently deletes the given Paper doc. This operation is final as the doc\n * cannot be recovered. This action can be performed only by the doc owner. Note\n * that this endpoint will continue to work for content created by users on the\n * older version of Paper. To check which version of Paper a user is on, use\n * /users/features/get_values. If the paper_as_files feature is enabled, then\n * the user is running the new version of Paper. Refer to the Paper Migration\n * Guide https://www.dropbox.com/lp/developers/reference/paper-migration-guide\n * for migration information.\n * Route attributes:\n *   scope: files.permanent_delete\n * @function Dropbox#paperDocsPermanentlyDelete\n * @deprecated\n * @arg {PaperRefPaperDoc} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<PaperDocLookupError>>}\n */\nroutes.paperDocsPermanentlyDelete = function (arg) {\n  return this.request('paper/docs/permanently_delete', arg, 'user', 'api', 'rpc', 'files.permanent_delete');\n};\n\n/**\n * Gets the default sharing policy for the given Paper doc. Note that this\n * endpoint will continue to work for content created by users on the older\n * version of Paper. To check which version of Paper a user is on, use\n * /users/features/get_values. If the paper_as_files feature is enabled, then\n * the user is running the new version of Paper. Refer to the Paper Migration\n * Guide https://www.dropbox.com/lp/developers/reference/paper-migration-guide\n * for migration information.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#paperDocsSharingPolicyGet\n * @deprecated\n * @arg {PaperRefPaperDoc} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<PaperSharingPolicy>, DropboxResponseError.<PaperDocLookupError>>}\n */\nroutes.paperDocsSharingPolicyGet = function (arg) {\n  return this.request('paper/docs/sharing_policy/get', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Sets the default sharing policy for the given Paper doc. The default\n * 'team_sharing_policy' can be changed only by teams, omit this field for\n * personal accounts. The 'public_sharing_policy' policy can't be set to the\n * value 'disabled' because this setting can be changed only via the team admin\n * console. Note that this endpoint will continue to work for content created by\n * users on the older version of Paper. To check which version of Paper a user\n * is on, use /users/features/get_values. If the paper_as_files feature is\n * enabled, then the user is running the new version of Paper. Refer to the\n * Paper Migration Guide\n * https://www.dropbox.com/lp/developers/reference/paper-migration-guide for\n * migration information.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#paperDocsSharingPolicySet\n * @deprecated\n * @arg {PaperPaperDocSharingPolicy} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<PaperDocLookupError>>}\n */\nroutes.paperDocsSharingPolicySet = function (arg) {\n  return this.request('paper/docs/sharing_policy/set', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Updates an existing Paper doc with the provided content. Note that this\n * endpoint will continue to work for content created by users on the older\n * version of Paper. To check which version of Paper a user is on, use\n * /users/features/get_values. If the paper_as_files feature is enabled, then\n * the user is running the new version of Paper. This endpoint will be retired\n * in September 2020. Refer to the Paper Migration Guide\n * https://www.dropbox.com/lp/developers/reference/paper-migration-guide for\n * more information.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#paperDocsUpdate\n * @deprecated\n * @arg {PaperPaperDocUpdateArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<PaperPaperDocCreateUpdateResult>, DropboxResponseError.<PaperPaperDocUpdateError>>}\n */\nroutes.paperDocsUpdate = function (arg) {\n  return this.request('paper/docs/update', arg, 'user', 'api', 'upload', 'files.content.write');\n};\n\n/**\n * Allows an owner or editor to add users to a Paper doc or change their\n * permissions using their email address or Dropbox account ID. The doc owner's\n * permissions cannot be changed. Note that this endpoint will continue to work\n * for content created by users on the older version of Paper. To check which\n * version of Paper a user is on, use /users/features/get_values. If the\n * paper_as_files feature is enabled, then the user is running the new version\n * of Paper. Refer to the Paper Migration Guide\n * https://www.dropbox.com/lp/developers/reference/paper-migration-guide for\n * migration information.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#paperDocsUsersAdd\n * @deprecated\n * @arg {PaperAddPaperDocUser} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<Array.<PaperAddPaperDocUserMemberResult>>, DropboxResponseError.<PaperDocLookupError>>}\n */\nroutes.paperDocsUsersAdd = function (arg) {\n  return this.request('paper/docs/users/add', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Lists all users who visited the Paper doc or users with explicit access. This\n * call excludes users who have been removed. The list is sorted by the date of\n * the visit or the share date. The list will include both users, the explicitly\n * shared ones as well as those who came in using the Paper url link. Note that\n * this endpoint will continue to work for content created by users on the older\n * version of Paper. To check which version of Paper a user is on, use\n * /users/features/get_values. If the paper_as_files feature is enabled, then\n * the user is running the new version of Paper. Refer to the Paper Migration\n * Guide https://www.dropbox.com/lp/developers/reference/paper-migration-guide\n * for migration information.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#paperDocsUsersList\n * @deprecated\n * @arg {PaperListUsersOnPaperDocArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<PaperListUsersOnPaperDocResponse>, DropboxResponseError.<PaperDocLookupError>>}\n */\nroutes.paperDocsUsersList = function (arg) {\n  return this.request('paper/docs/users/list', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Once a cursor has been retrieved from docs/users/list, use this to paginate\n * through all users on the Paper doc. Note that this endpoint will continue to\n * work for content created by users on the older version of Paper. To check\n * which version of Paper a user is on, use /users/features/get_values. If the\n * paper_as_files feature is enabled, then the user is running the new version\n * of Paper. Refer to the Paper Migration Guide\n * https://www.dropbox.com/lp/developers/reference/paper-migration-guide for\n * migration information.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#paperDocsUsersListContinue\n * @deprecated\n * @arg {PaperListUsersOnPaperDocContinueArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<PaperListUsersOnPaperDocResponse>, DropboxResponseError.<PaperListUsersCursorError>>}\n */\nroutes.paperDocsUsersListContinue = function (arg) {\n  return this.request('paper/docs/users/list/continue', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Allows an owner or editor to remove users from a Paper doc using their email\n * address or Dropbox account ID. The doc owner cannot be removed. Note that\n * this endpoint will continue to work for content created by users on the older\n * version of Paper. To check which version of Paper a user is on, use\n * /users/features/get_values. If the paper_as_files feature is enabled, then\n * the user is running the new version of Paper. Refer to the Paper Migration\n * Guide https://www.dropbox.com/lp/developers/reference/paper-migration-guide\n * for migration information.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#paperDocsUsersRemove\n * @deprecated\n * @arg {PaperRemovePaperDocUser} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<PaperDocLookupError>>}\n */\nroutes.paperDocsUsersRemove = function (arg) {\n  return this.request('paper/docs/users/remove', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Create a new Paper folder with the provided info. Note that this endpoint\n * will continue to work for content created by users on the older version of\n * Paper. To check which version of Paper a user is on, use\n * /users/features/get_values. If the paper_as_files feature is enabled, then\n * the user is running the new version of Paper. Refer to the Paper Migration\n * Guide https://www.dropbox.com/lp/developers/reference/paper-migration-guide\n * for migration information.\n * Route attributes:\n *   scope: files.content.write\n * @function Dropbox#paperFoldersCreate\n * @deprecated\n * @arg {PaperPaperFolderCreateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<PaperPaperFolderCreateResult>, DropboxResponseError.<PaperPaperFolderCreateError>>}\n */\nroutes.paperFoldersCreate = function (arg) {\n  return this.request('paper/folders/create', arg, 'user', 'api', 'rpc', 'files.content.write');\n};\n\n/**\n * Adds specified members to a file.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingAddFileMember\n * @arg {SharingAddFileMemberArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<Array.<SharingFileMemberActionResult>>, DropboxResponseError.<SharingAddFileMemberError>>}\n */\nroutes.sharingAddFileMember = function (arg) {\n  return this.request('sharing/add_file_member', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Allows an owner or editor (if the ACL update policy allows) of a shared\n * folder to add another member. For the new member to get access to all the\n * functionality for this folder, you will need to call mount_folder on their\n * behalf.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingAddFolderMember\n * @arg {SharingAddFolderMemberArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<SharingAddFolderMemberError>>}\n */\nroutes.sharingAddFolderMember = function (arg) {\n  return this.request('sharing/add_folder_member', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Returns the status of an asynchronous job.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingCheckJobStatus\n * @arg {AsyncPollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingJobStatus>, DropboxResponseError.<AsyncPollError>>}\n */\nroutes.sharingCheckJobStatus = function (arg) {\n  return this.request('sharing/check_job_status', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Returns the status of an asynchronous job for sharing a folder.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingCheckRemoveMemberJobStatus\n * @arg {AsyncPollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingRemoveMemberJobStatus>, DropboxResponseError.<AsyncPollError>>}\n */\nroutes.sharingCheckRemoveMemberJobStatus = function (arg) {\n  return this.request('sharing/check_remove_member_job_status', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Returns the status of an asynchronous job for sharing a folder.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingCheckShareJobStatus\n * @arg {AsyncPollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingShareFolderJobStatus>, DropboxResponseError.<AsyncPollError>>}\n */\nroutes.sharingCheckShareJobStatus = function (arg) {\n  return this.request('sharing/check_share_job_status', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Create a shared link. If a shared link already exists for the given path,\n * that link is returned. Previously, it was technically possible to break a\n * shared link by moving or renaming the corresponding file or folder. In the\n * future, this will no longer be the case, so your app shouldn't rely on this\n * behavior. Instead, if your app needs to revoke a shared link, use\n * revoke_shared_link.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingCreateSharedLink\n * @deprecated\n * @arg {SharingCreateSharedLinkArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingPathLinkMetadata>, DropboxResponseError.<SharingCreateSharedLinkError>>}\n */\nroutes.sharingCreateSharedLink = function (arg) {\n  return this.request('sharing/create_shared_link', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Create a shared link with custom settings. If no settings are given then the\n * default visibility is RequestedVisibility.public (The resolved visibility,\n * though, may depend on other aspects such as team and shared folder settings).\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingCreateSharedLinkWithSettings\n * @arg {SharingCreateSharedLinkWithSettingsArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<(SharingFileLinkMetadata|SharingFolderLinkMetadata|SharingSharedLinkMetadata)>, DropboxResponseError.<SharingCreateSharedLinkWithSettingsError>>}\n */\nroutes.sharingCreateSharedLinkWithSettings = function (arg) {\n  return this.request('sharing/create_shared_link_with_settings', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Returns shared file metadata.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingGetFileMetadata\n * @arg {SharingGetFileMetadataArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingSharedFileMetadata>, DropboxResponseError.<SharingGetFileMetadataError>>}\n */\nroutes.sharingGetFileMetadata = function (arg) {\n  return this.request('sharing/get_file_metadata', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Returns shared file metadata.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingGetFileMetadataBatch\n * @arg {SharingGetFileMetadataBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<Array.<SharingGetFileMetadataBatchResult>>, DropboxResponseError.<SharingSharingUserError>>}\n */\nroutes.sharingGetFileMetadataBatch = function (arg) {\n  return this.request('sharing/get_file_metadata/batch', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Returns shared folder metadata by its folder ID.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingGetFolderMetadata\n * @arg {SharingGetMetadataArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingSharedFolderMetadata>, DropboxResponseError.<SharingSharedFolderAccessError>>}\n */\nroutes.sharingGetFolderMetadata = function (arg) {\n  return this.request('sharing/get_folder_metadata', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Download the shared link's file from a user's Dropbox.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingGetSharedLinkFile\n * @arg {Object} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<(SharingFileLinkMetadata|SharingFolderLinkMetadata|SharingSharedLinkMetadata)>, DropboxResponseError.<SharingGetSharedLinkFileError>>}\n */\nroutes.sharingGetSharedLinkFile = function (arg) {\n  return this.request('sharing/get_shared_link_file', arg, 'user', 'content', 'download', 'sharing.read');\n};\n\n/**\n * Get the shared link's metadata.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingGetSharedLinkMetadata\n * @arg {SharingGetSharedLinkMetadataArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<(SharingFileLinkMetadata|SharingFolderLinkMetadata|SharingSharedLinkMetadata)>, DropboxResponseError.<SharingSharedLinkError>>}\n */\nroutes.sharingGetSharedLinkMetadata = function (arg) {\n  return this.request('sharing/get_shared_link_metadata', arg, 'app, user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Returns a list of LinkMetadata objects for this user, including collection\n * links. If no path is given, returns a list of all shared links for the\n * current user, including collection links, up to a maximum of 1000 links. If a\n * non-empty path is given, returns a list of all shared links that allow access\n * to the given path.  Collection links are never returned in this case.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingGetSharedLinks\n * @deprecated\n * @arg {SharingGetSharedLinksArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingGetSharedLinksResult>, DropboxResponseError.<SharingGetSharedLinksError>>}\n */\nroutes.sharingGetSharedLinks = function (arg) {\n  return this.request('sharing/get_shared_links', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Use to obtain the members who have been invited to a file, both inherited and\n * uninherited members.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingListFileMembers\n * @arg {SharingListFileMembersArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingSharedFileMembers>, DropboxResponseError.<SharingListFileMembersError>>}\n */\nroutes.sharingListFileMembers = function (arg) {\n  return this.request('sharing/list_file_members', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Get members of multiple files at once. The arguments to this route are more\n * limited, and the limit on query result size per file is more strict. To\n * customize the results more, use the individual file endpoint. Inherited users\n * and groups are not included in the result, and permissions are not returned\n * for this endpoint.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingListFileMembersBatch\n * @arg {SharingListFileMembersBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<Array.<SharingListFileMembersBatchResult>>, DropboxResponseError.<SharingSharingUserError>>}\n */\nroutes.sharingListFileMembersBatch = function (arg) {\n  return this.request('sharing/list_file_members/batch', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Once a cursor has been retrieved from list_file_members or\n * list_file_members/batch, use this to paginate through all shared file\n * members.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingListFileMembersContinue\n * @arg {SharingListFileMembersContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingSharedFileMembers>, DropboxResponseError.<SharingListFileMembersContinueError>>}\n */\nroutes.sharingListFileMembersContinue = function (arg) {\n  return this.request('sharing/list_file_members/continue', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Returns shared folder membership by its folder ID.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingListFolderMembers\n * @arg {SharingListFolderMembersArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingSharedFolderMembers>, DropboxResponseError.<SharingSharedFolderAccessError>>}\n */\nroutes.sharingListFolderMembers = function (arg) {\n  return this.request('sharing/list_folder_members', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Once a cursor has been retrieved from list_folder_members, use this to\n * paginate through all shared folder members.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingListFolderMembersContinue\n * @arg {SharingListFolderMembersContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingSharedFolderMembers>, DropboxResponseError.<SharingListFolderMembersContinueError>>}\n */\nroutes.sharingListFolderMembersContinue = function (arg) {\n  return this.request('sharing/list_folder_members/continue', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Return the list of all shared folders the current user has access to.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingListFolders\n * @arg {SharingListFoldersArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingListFoldersResult>, DropboxResponseError.<void>>}\n */\nroutes.sharingListFolders = function (arg) {\n  return this.request('sharing/list_folders', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Once a cursor has been retrieved from list_folders, use this to paginate\n * through all shared folders. The cursor must come from a previous call to\n * list_folders or list_folders/continue.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingListFoldersContinue\n * @arg {SharingListFoldersContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingListFoldersResult>, DropboxResponseError.<SharingListFoldersContinueError>>}\n */\nroutes.sharingListFoldersContinue = function (arg) {\n  return this.request('sharing/list_folders/continue', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Return the list of all shared folders the current user can mount or unmount.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingListMountableFolders\n * @arg {SharingListFoldersArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingListFoldersResult>, DropboxResponseError.<void>>}\n */\nroutes.sharingListMountableFolders = function (arg) {\n  return this.request('sharing/list_mountable_folders', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Once a cursor has been retrieved from list_mountable_folders, use this to\n * paginate through all mountable shared folders. The cursor must come from a\n * previous call to list_mountable_folders or list_mountable_folders/continue.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingListMountableFoldersContinue\n * @arg {SharingListFoldersContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingListFoldersResult>, DropboxResponseError.<SharingListFoldersContinueError>>}\n */\nroutes.sharingListMountableFoldersContinue = function (arg) {\n  return this.request('sharing/list_mountable_folders/continue', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Returns a list of all files shared with current user.  Does not include files\n * the user has received via shared folders, and does  not include unclaimed\n * invitations.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingListReceivedFiles\n * @arg {SharingListFilesArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingListFilesResult>, DropboxResponseError.<SharingSharingUserError>>}\n */\nroutes.sharingListReceivedFiles = function (arg) {\n  return this.request('sharing/list_received_files', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Get more results with a cursor from list_received_files.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingListReceivedFilesContinue\n * @arg {SharingListFilesContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingListFilesResult>, DropboxResponseError.<SharingListFilesContinueError>>}\n */\nroutes.sharingListReceivedFilesContinue = function (arg) {\n  return this.request('sharing/list_received_files/continue', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * List shared links of this user. If no path is given, returns a list of all\n * shared links for the current user. For members of business teams using team\n * space and member folders, returns all shared links in the team member's home\n * folder unless the team space ID is specified in the request header. For more\n * information, refer to the Namespace Guide\n * https://www.dropbox.com/developers/reference/namespace-guide. If a non-empty\n * path is given, returns a list of all shared links that allow access to the\n * given path - direct links to the given path and links to parent folders of\n * the given path. Links to parent folders can be suppressed by setting\n * direct_only to true.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#sharingListSharedLinks\n * @arg {SharingListSharedLinksArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingListSharedLinksResult>, DropboxResponseError.<SharingListSharedLinksError>>}\n */\nroutes.sharingListSharedLinks = function (arg) {\n  return this.request('sharing/list_shared_links', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Modify the shared link's settings. If the requested visibility conflict with\n * the shared links policy of the team or the shared folder (in case the linked\n * file is part of a shared folder) then the LinkPermissions.resolved_visibility\n * of the returned SharedLinkMetadata will reflect the actual visibility of the\n * shared link and the LinkPermissions.requested_visibility will reflect the\n * requested visibility.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingModifySharedLinkSettings\n * @arg {SharingModifySharedLinkSettingsArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<(SharingFileLinkMetadata|SharingFolderLinkMetadata|SharingSharedLinkMetadata)>, DropboxResponseError.<SharingModifySharedLinkSettingsError>>}\n */\nroutes.sharingModifySharedLinkSettings = function (arg) {\n  return this.request('sharing/modify_shared_link_settings', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * The current user mounts the designated folder. Mount a shared folder for a\n * user after they have been added as a member. Once mounted, the shared folder\n * will appear in their Dropbox.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingMountFolder\n * @arg {SharingMountFolderArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingSharedFolderMetadata>, DropboxResponseError.<SharingMountFolderError>>}\n */\nroutes.sharingMountFolder = function (arg) {\n  return this.request('sharing/mount_folder', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * The current user relinquishes their membership in the designated file. Note\n * that the current user may still have inherited access to this file through\n * the parent folder.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingRelinquishFileMembership\n * @arg {SharingRelinquishFileMembershipArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<SharingRelinquishFileMembershipError>>}\n */\nroutes.sharingRelinquishFileMembership = function (arg) {\n  return this.request('sharing/relinquish_file_membership', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * The current user relinquishes their membership in the designated shared\n * folder and will no longer have access to the folder.  A folder owner cannot\n * relinquish membership in their own folder. This will run synchronously if\n * leave_a_copy is false, and asynchronously if leave_a_copy is true.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingRelinquishFolderMembership\n * @arg {SharingRelinquishFolderMembershipArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<AsyncLaunchEmptyResult>, DropboxResponseError.<SharingRelinquishFolderMembershipError>>}\n */\nroutes.sharingRelinquishFolderMembership = function (arg) {\n  return this.request('sharing/relinquish_folder_membership', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Identical to remove_file_member_2 but with less information returned.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingRemoveFileMember\n * @deprecated\n * @arg {SharingRemoveFileMemberArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingFileMemberActionIndividualResult>, DropboxResponseError.<SharingRemoveFileMemberError>>}\n */\nroutes.sharingRemoveFileMember = function (arg) {\n  return this.request('sharing/remove_file_member', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Removes a specified member from the file.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingRemoveFileMember2\n * @arg {SharingRemoveFileMemberArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingFileMemberRemoveActionResult>, DropboxResponseError.<SharingRemoveFileMemberError>>}\n */\nroutes.sharingRemoveFileMember2 = function (arg) {\n  return this.request('sharing/remove_file_member_2', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Allows an owner or editor (if the ACL update policy allows) of a shared\n * folder to remove another member.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingRemoveFolderMember\n * @arg {SharingRemoveFolderMemberArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<AsyncLaunchResultBase>, DropboxResponseError.<SharingRemoveFolderMemberError>>}\n */\nroutes.sharingRemoveFolderMember = function (arg) {\n  return this.request('sharing/remove_folder_member', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Revoke a shared link. Note that even after revoking a shared link to a file,\n * the file may be accessible if there are shared links leading to any of the\n * file parent folders. To list all shared links that enable access to a\n * specific file, you can use the list_shared_links with the file as the\n * ListSharedLinksArg.path argument.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingRevokeSharedLink\n * @arg {SharingRevokeSharedLinkArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<SharingRevokeSharedLinkError>>}\n */\nroutes.sharingRevokeSharedLink = function (arg) {\n  return this.request('sharing/revoke_shared_link', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Change the inheritance policy of an existing Shared Folder. Only permitted\n * for shared folders in a shared team root. If a ShareFolderLaunch.async_job_id\n * is returned, you'll need to call check_share_job_status until the action\n * completes to get the metadata for the folder.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingSetAccessInheritance\n * @arg {SharingSetAccessInheritanceArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingShareFolderLaunch>, DropboxResponseError.<SharingSetAccessInheritanceError>>}\n */\nroutes.sharingSetAccessInheritance = function (arg) {\n  return this.request('sharing/set_access_inheritance', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Share a folder with collaborators. Most sharing will be completed\n * synchronously. Large folders will be completed asynchronously. To make\n * testing the async case repeatable, set `ShareFolderArg.force_async`. If a\n * ShareFolderLaunch.async_job_id is returned, you'll need to call\n * check_share_job_status until the action completes to get the metadata for the\n * folder.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingShareFolder\n * @arg {SharingShareFolderArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingShareFolderLaunch>, DropboxResponseError.<SharingShareFolderError>>}\n */\nroutes.sharingShareFolder = function (arg) {\n  return this.request('sharing/share_folder', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Transfer ownership of a shared folder to a member of the shared folder. User\n * must have AccessLevel.owner access to the shared folder to perform a\n * transfer.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingTransferFolder\n * @arg {SharingTransferFolderArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<SharingTransferFolderError>>}\n */\nroutes.sharingTransferFolder = function (arg) {\n  return this.request('sharing/transfer_folder', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * The current user unmounts the designated folder. They can re-mount the folder\n * at a later time using mount_folder.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingUnmountFolder\n * @arg {SharingUnmountFolderArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<SharingUnmountFolderError>>}\n */\nroutes.sharingUnmountFolder = function (arg) {\n  return this.request('sharing/unmount_folder', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Remove all members from this file. Does not remove inherited members.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingUnshareFile\n * @arg {SharingUnshareFileArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<SharingUnshareFileError>>}\n */\nroutes.sharingUnshareFile = function (arg) {\n  return this.request('sharing/unshare_file', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Allows a shared folder owner to unshare the folder. You'll need to call\n * check_job_status to determine if the action has completed successfully.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingUnshareFolder\n * @arg {SharingUnshareFolderArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<AsyncLaunchEmptyResult>, DropboxResponseError.<SharingUnshareFolderError>>}\n */\nroutes.sharingUnshareFolder = function (arg) {\n  return this.request('sharing/unshare_folder', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Changes a member's access on a shared file.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingUpdateFileMember\n * @arg {SharingUpdateFileMemberArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingMemberAccessLevelResult>, DropboxResponseError.<SharingFileMemberActionError>>}\n */\nroutes.sharingUpdateFileMember = function (arg) {\n  return this.request('sharing/update_file_member', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Allows an owner or editor of a shared folder to update another member's\n * permissions.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingUpdateFolderMember\n * @arg {SharingUpdateFolderMemberArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingMemberAccessLevelResult>, DropboxResponseError.<SharingUpdateFolderMemberError>>}\n */\nroutes.sharingUpdateFolderMember = function (arg) {\n  return this.request('sharing/update_folder_member', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * Update the sharing policies for a shared folder. User must have\n * AccessLevel.owner access to the shared folder to update its policies.\n * Route attributes:\n *   scope: sharing.write\n * @function Dropbox#sharingUpdateFolderPolicy\n * @arg {SharingUpdateFolderPolicyArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<SharingSharedFolderMetadata>, DropboxResponseError.<SharingUpdateFolderPolicyError>>}\n */\nroutes.sharingUpdateFolderPolicy = function (arg) {\n  return this.request('sharing/update_folder_policy', arg, 'user', 'api', 'rpc', 'sharing.write');\n};\n\n/**\n * List all device sessions of a team's member.\n * Route attributes:\n *   scope: sessions.list\n * @function Dropbox#teamDevicesListMemberDevices\n * @arg {TeamListMemberDevicesArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamListMemberDevicesResult>, DropboxResponseError.<TeamListMemberDevicesError>>}\n */\nroutes.teamDevicesListMemberDevices = function (arg) {\n  return this.request('team/devices/list_member_devices', arg, 'team', 'api', 'rpc', 'sessions.list');\n};\n\n/**\n * List all device sessions of a team. Permission : Team member file access.\n * Route attributes:\n *   scope: sessions.list\n * @function Dropbox#teamDevicesListMembersDevices\n * @arg {TeamListMembersDevicesArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamListMembersDevicesResult>, DropboxResponseError.<TeamListMembersDevicesError>>}\n */\nroutes.teamDevicesListMembersDevices = function (arg) {\n  return this.request('team/devices/list_members_devices', arg, 'team', 'api', 'rpc', 'sessions.list');\n};\n\n/**\n * List all device sessions of a team. Permission : Team member file access.\n * Route attributes:\n *   scope: sessions.list\n * @function Dropbox#teamDevicesListTeamDevices\n * @deprecated\n * @arg {TeamListTeamDevicesArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamListTeamDevicesResult>, DropboxResponseError.<TeamListTeamDevicesError>>}\n */\nroutes.teamDevicesListTeamDevices = function (arg) {\n  return this.request('team/devices/list_team_devices', arg, 'team', 'api', 'rpc', 'sessions.list');\n};\n\n/**\n * Revoke a device session of a team's member.\n * Route attributes:\n *   scope: sessions.modify\n * @function Dropbox#teamDevicesRevokeDeviceSession\n * @arg {TeamRevokeDeviceSessionArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<TeamRevokeDeviceSessionError>>}\n */\nroutes.teamDevicesRevokeDeviceSession = function (arg) {\n  return this.request('team/devices/revoke_device_session', arg, 'team', 'api', 'rpc', 'sessions.modify');\n};\n\n/**\n * Revoke a list of device sessions of team members.\n * Route attributes:\n *   scope: sessions.modify\n * @function Dropbox#teamDevicesRevokeDeviceSessionBatch\n * @arg {TeamRevokeDeviceSessionBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamRevokeDeviceSessionBatchResult>, DropboxResponseError.<TeamRevokeDeviceSessionBatchError>>}\n */\nroutes.teamDevicesRevokeDeviceSessionBatch = function (arg) {\n  return this.request('team/devices/revoke_device_session_batch', arg, 'team', 'api', 'rpc', 'sessions.modify');\n};\n\n/**\n * Get the values for one or more featues. This route allows you to check your\n * account's capability for what feature you can access or what value you have\n * for certain features. Permission : Team information.\n * Route attributes:\n *   scope: team_info.read\n * @function Dropbox#teamFeaturesGetValues\n * @arg {TeamFeaturesGetValuesBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamFeaturesGetValuesBatchResult>, DropboxResponseError.<TeamFeaturesGetValuesBatchError>>}\n */\nroutes.teamFeaturesGetValues = function (arg) {\n  return this.request('team/features/get_values', arg, 'team', 'api', 'rpc', 'team_info.read');\n};\n\n/**\n * Retrieves information about a team.\n * Route attributes:\n *   scope: team_info.read\n * @function Dropbox#teamGetInfo\n * @returns {Promise.<DropboxResponse<TeamTeamGetInfoResult>, DropboxResponseError.<void>>}\n */\nroutes.teamGetInfo = function () {\n  return this.request('team/get_info', null, 'team', 'api', 'rpc', 'team_info.read');\n};\n\n/**\n * Creates a new, empty group, with a requested name. Permission : Team member\n * management.\n * Route attributes:\n *   scope: groups.write\n * @function Dropbox#teamGroupsCreate\n * @arg {TeamGroupCreateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamGroupFullInfo>, DropboxResponseError.<TeamGroupCreateError>>}\n */\nroutes.teamGroupsCreate = function (arg) {\n  return this.request('team/groups/create', arg, 'team', 'api', 'rpc', 'groups.write');\n};\n\n/**\n * Deletes a group. The group is deleted immediately. However the revoking of\n * group-owned resources may take additional time. Use the groups/job_status/get\n * to determine whether this process has completed. Permission : Team member\n * management.\n * Route attributes:\n *   scope: groups.write\n * @function Dropbox#teamGroupsDelete\n * @arg {TeamGroupSelector} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<AsyncLaunchEmptyResult>, DropboxResponseError.<TeamGroupDeleteError>>}\n */\nroutes.teamGroupsDelete = function (arg) {\n  return this.request('team/groups/delete', arg, 'team', 'api', 'rpc', 'groups.write');\n};\n\n/**\n * Retrieves information about one or more groups. Note that the optional field\n * GroupFullInfo.members is not returned for system-managed groups. Permission :\n * Team Information.\n * Route attributes:\n *   scope: groups.read\n * @function Dropbox#teamGroupsGetInfo\n * @arg {TeamGroupsSelector} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<Object>, DropboxResponseError.<TeamGroupsGetInfoError>>}\n */\nroutes.teamGroupsGetInfo = function (arg) {\n  return this.request('team/groups/get_info', arg, 'team', 'api', 'rpc', 'groups.read');\n};\n\n/**\n * Once an async_job_id is returned from groups/delete, groups/members/add , or\n * groups/members/remove use this method to poll the status of granting/revoking\n * group members' access to group-owned resources. Permission : Team member\n * management.\n * Route attributes:\n *   scope: groups.write\n * @function Dropbox#teamGroupsJobStatusGet\n * @arg {AsyncPollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<AsyncPollEmptyResult>, DropboxResponseError.<TeamGroupsPollError>>}\n */\nroutes.teamGroupsJobStatusGet = function (arg) {\n  return this.request('team/groups/job_status/get', arg, 'team', 'api', 'rpc', 'groups.write');\n};\n\n/**\n * Lists groups on a team. Permission : Team Information.\n * Route attributes:\n *   scope: groups.read\n * @function Dropbox#teamGroupsList\n * @arg {TeamGroupsListArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamGroupsListResult>, DropboxResponseError.<void>>}\n */\nroutes.teamGroupsList = function (arg) {\n  return this.request('team/groups/list', arg, 'team', 'api', 'rpc', 'groups.read');\n};\n\n/**\n * Once a cursor has been retrieved from groups/list, use this to paginate\n * through all groups. Permission : Team Information.\n * Route attributes:\n *   scope: groups.read\n * @function Dropbox#teamGroupsListContinue\n * @arg {TeamGroupsListContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamGroupsListResult>, DropboxResponseError.<TeamGroupsListContinueError>>}\n */\nroutes.teamGroupsListContinue = function (arg) {\n  return this.request('team/groups/list/continue', arg, 'team', 'api', 'rpc', 'groups.read');\n};\n\n/**\n * Adds members to a group. The members are added immediately. However the\n * granting of group-owned resources may take additional time. Use the\n * groups/job_status/get to determine whether this process has completed.\n * Permission : Team member management.\n * Route attributes:\n *   scope: groups.write\n * @function Dropbox#teamGroupsMembersAdd\n * @arg {TeamGroupMembersAddArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamGroupMembersChangeResult>, DropboxResponseError.<TeamGroupMembersAddError>>}\n */\nroutes.teamGroupsMembersAdd = function (arg) {\n  return this.request('team/groups/members/add', arg, 'team', 'api', 'rpc', 'groups.write');\n};\n\n/**\n * Lists members of a group. Permission : Team Information.\n * Route attributes:\n *   scope: groups.read\n * @function Dropbox#teamGroupsMembersList\n * @arg {TeamGroupsMembersListArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamGroupsMembersListResult>, DropboxResponseError.<TeamGroupSelectorError>>}\n */\nroutes.teamGroupsMembersList = function (arg) {\n  return this.request('team/groups/members/list', arg, 'team', 'api', 'rpc', 'groups.read');\n};\n\n/**\n * Once a cursor has been retrieved from groups/members/list, use this to\n * paginate through all members of the group. Permission : Team information.\n * Route attributes:\n *   scope: groups.read\n * @function Dropbox#teamGroupsMembersListContinue\n * @arg {TeamGroupsMembersListContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamGroupsMembersListResult>, DropboxResponseError.<TeamGroupsMembersListContinueError>>}\n */\nroutes.teamGroupsMembersListContinue = function (arg) {\n  return this.request('team/groups/members/list/continue', arg, 'team', 'api', 'rpc', 'groups.read');\n};\n\n/**\n * Removes members from a group. The members are removed immediately. However\n * the revoking of group-owned resources may take additional time. Use the\n * groups/job_status/get to determine whether this process has completed. This\n * method permits removing the only owner of a group, even in cases where this\n * is not possible via the web client. Permission : Team member management.\n * Route attributes:\n *   scope: groups.write\n * @function Dropbox#teamGroupsMembersRemove\n * @arg {TeamGroupMembersRemoveArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamGroupMembersChangeResult>, DropboxResponseError.<TeamGroupMembersRemoveError>>}\n */\nroutes.teamGroupsMembersRemove = function (arg) {\n  return this.request('team/groups/members/remove', arg, 'team', 'api', 'rpc', 'groups.write');\n};\n\n/**\n * Sets a member's access type in a group. Permission : Team member management.\n * Route attributes:\n *   scope: groups.write\n * @function Dropbox#teamGroupsMembersSetAccessType\n * @arg {TeamGroupMembersSetAccessTypeArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<Object>, DropboxResponseError.<TeamGroupMemberSetAccessTypeError>>}\n */\nroutes.teamGroupsMembersSetAccessType = function (arg) {\n  return this.request('team/groups/members/set_access_type', arg, 'team', 'api', 'rpc', 'groups.write');\n};\n\n/**\n * Updates a group's name and/or external ID. Permission : Team member\n * management.\n * Route attributes:\n *   scope: groups.write\n * @function Dropbox#teamGroupsUpdate\n * @arg {TeamGroupUpdateArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamGroupFullInfo>, DropboxResponseError.<TeamGroupUpdateError>>}\n */\nroutes.teamGroupsUpdate = function (arg) {\n  return this.request('team/groups/update', arg, 'team', 'api', 'rpc', 'groups.write');\n};\n\n/**\n * Creates new legal hold policy. Note: Legal Holds is a paid add-on. Not all\n * teams have the feature. Permission : Team member file access.\n * Route attributes:\n *   scope: team_data.governance.write\n * @function Dropbox#teamLegalHoldsCreatePolicy\n * @arg {TeamLegalHoldsPolicyCreateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<Object>, DropboxResponseError.<TeamLegalHoldsPolicyCreateError>>}\n */\nroutes.teamLegalHoldsCreatePolicy = function (arg) {\n  return this.request('team/legal_holds/create_policy', arg, 'team', 'api', 'rpc', 'team_data.governance.write');\n};\n\n/**\n * Gets a legal hold by Id. Note: Legal Holds is a paid add-on. Not all teams\n * have the feature. Permission : Team member file access.\n * Route attributes:\n *   scope: team_data.governance.write\n * @function Dropbox#teamLegalHoldsGetPolicy\n * @arg {TeamLegalHoldsGetPolicyArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<Object>, DropboxResponseError.<TeamLegalHoldsGetPolicyError>>}\n */\nroutes.teamLegalHoldsGetPolicy = function (arg) {\n  return this.request('team/legal_holds/get_policy', arg, 'team', 'api', 'rpc', 'team_data.governance.write');\n};\n\n/**\n * List the file metadata that's under the hold. Note: Legal Holds is a paid\n * add-on. Not all teams have the feature. Permission : Team member file access.\n * Route attributes:\n *   scope: team_data.governance.write\n * @function Dropbox#teamLegalHoldsListHeldRevisions\n * @arg {TeamLegalHoldsListHeldRevisionsArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamLegalHoldsListHeldRevisionResult>, DropboxResponseError.<TeamLegalHoldsListHeldRevisionsError>>}\n */\nroutes.teamLegalHoldsListHeldRevisions = function (arg) {\n  return this.request('team/legal_holds/list_held_revisions', arg, 'team', 'api', 'rpc', 'team_data.governance.write');\n};\n\n/**\n * Continue listing the file metadata that's under the hold. Note: Legal Holds\n * is a paid add-on. Not all teams have the feature. Permission : Team member\n * file access.\n * Route attributes:\n *   scope: team_data.governance.write\n * @function Dropbox#teamLegalHoldsListHeldRevisionsContinue\n * @arg {TeamLegalHoldsListHeldRevisionsContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamLegalHoldsListHeldRevisionResult>, DropboxResponseError.<TeamLegalHoldsListHeldRevisionsError>>}\n */\nroutes.teamLegalHoldsListHeldRevisionsContinue = function (arg) {\n  return this.request('team/legal_holds/list_held_revisions_continue', arg, 'team', 'api', 'rpc', 'team_data.governance.write');\n};\n\n/**\n * Lists legal holds on a team. Note: Legal Holds is a paid add-on. Not all\n * teams have the feature. Permission : Team member file access.\n * Route attributes:\n *   scope: team_data.governance.write\n * @function Dropbox#teamLegalHoldsListPolicies\n * @arg {TeamLegalHoldsListPoliciesArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamLegalHoldsListPoliciesResult>, DropboxResponseError.<TeamLegalHoldsListPoliciesError>>}\n */\nroutes.teamLegalHoldsListPolicies = function (arg) {\n  return this.request('team/legal_holds/list_policies', arg, 'team', 'api', 'rpc', 'team_data.governance.write');\n};\n\n/**\n * Releases a legal hold by Id. Note: Legal Holds is a paid add-on. Not all\n * teams have the feature. Permission : Team member file access.\n * Route attributes:\n *   scope: team_data.governance.write\n * @function Dropbox#teamLegalHoldsReleasePolicy\n * @arg {TeamLegalHoldsPolicyReleaseArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<TeamLegalHoldsPolicyReleaseError>>}\n */\nroutes.teamLegalHoldsReleasePolicy = function (arg) {\n  return this.request('team/legal_holds/release_policy', arg, 'team', 'api', 'rpc', 'team_data.governance.write');\n};\n\n/**\n * Updates a legal hold. Note: Legal Holds is a paid add-on. Not all teams have\n * the feature. Permission : Team member file access.\n * Route attributes:\n *   scope: team_data.governance.write\n * @function Dropbox#teamLegalHoldsUpdatePolicy\n * @arg {TeamLegalHoldsPolicyUpdateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<Object>, DropboxResponseError.<TeamLegalHoldsPolicyUpdateError>>}\n */\nroutes.teamLegalHoldsUpdatePolicy = function (arg) {\n  return this.request('team/legal_holds/update_policy', arg, 'team', 'api', 'rpc', 'team_data.governance.write');\n};\n\n/**\n * List all linked applications of the team member. Note, this endpoint does not\n * list any team-linked applications.\n * Route attributes:\n *   scope: sessions.list\n * @function Dropbox#teamLinkedAppsListMemberLinkedApps\n * @arg {TeamListMemberAppsArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamListMemberAppsResult>, DropboxResponseError.<TeamListMemberAppsError>>}\n */\nroutes.teamLinkedAppsListMemberLinkedApps = function (arg) {\n  return this.request('team/linked_apps/list_member_linked_apps', arg, 'team', 'api', 'rpc', 'sessions.list');\n};\n\n/**\n * List all applications linked to the team members' accounts. Note, this\n * endpoint does not list any team-linked applications.\n * Route attributes:\n *   scope: sessions.list\n * @function Dropbox#teamLinkedAppsListMembersLinkedApps\n * @arg {TeamListMembersAppsArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamListMembersAppsResult>, DropboxResponseError.<TeamListMembersAppsError>>}\n */\nroutes.teamLinkedAppsListMembersLinkedApps = function (arg) {\n  return this.request('team/linked_apps/list_members_linked_apps', arg, 'team', 'api', 'rpc', 'sessions.list');\n};\n\n/**\n * List all applications linked to the team members' accounts. Note, this\n * endpoint doesn't list any team-linked applications.\n * Route attributes:\n *   scope: sessions.list\n * @function Dropbox#********************************\n * @deprecated\n * @arg {TeamListTeamAppsArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamListTeamAppsResult>, DropboxResponseError.<TeamListTeamAppsError>>}\n */\nroutes.******************************** = function (arg) {\n  return this.request('team/linked_apps/list_team_linked_apps', arg, 'team', 'api', 'rpc', 'sessions.list');\n};\n\n/**\n * Revoke a linked application of the team member.\n * Route attributes:\n *   scope: sessions.modify\n * @function Dropbox#teamLinkedAppsRevokeLinkedApp\n * @arg {TeamRevokeLinkedApiAppArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<TeamRevokeLinkedAppError>>}\n */\nroutes.teamLinkedAppsRevokeLinkedApp = function (arg) {\n  return this.request('team/linked_apps/revoke_linked_app', arg, 'team', 'api', 'rpc', 'sessions.modify');\n};\n\n/**\n * Revoke a list of linked applications of the team members.\n * Route attributes:\n *   scope: sessions.modify\n * @function Dropbox#teamLinkedAppsRevokeLinkedAppBatch\n * @arg {TeamRevokeLinkedApiAppBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamRevokeLinkedAppBatchResult>, DropboxResponseError.<TeamRevokeLinkedAppBatchError>>}\n */\nroutes.teamLinkedAppsRevokeLinkedAppBatch = function (arg) {\n  return this.request('team/linked_apps/revoke_linked_app_batch', arg, 'team', 'api', 'rpc', 'sessions.modify');\n};\n\n/**\n * Add users to member space limits excluded users list.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMemberSpaceLimitsExcludedUsersAdd\n * @arg {TeamExcludedUsersUpdateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamExcludedUsersUpdateResult>, DropboxResponseError.<TeamExcludedUsersUpdateError>>}\n */\nroutes.teamMemberSpaceLimitsExcludedUsersAdd = function (arg) {\n  return this.request('team/member_space_limits/excluded_users/add', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * List member space limits excluded users.\n * Route attributes:\n *   scope: members.read\n * @function Dropbox#teamMemberSpaceLimitsExcludedUsersList\n * @arg {TeamExcludedUsersListArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamExcludedUsersListResult>, DropboxResponseError.<TeamExcludedUsersListError>>}\n */\nroutes.teamMemberSpaceLimitsExcludedUsersList = function (arg) {\n  return this.request('team/member_space_limits/excluded_users/list', arg, 'team', 'api', 'rpc', 'members.read');\n};\n\n/**\n * Continue listing member space limits excluded users.\n * Route attributes:\n *   scope: members.read\n * @function Dropbox#teamMemberSpaceLimitsExcludedUsersListContinue\n * @arg {TeamExcludedUsersListContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamExcludedUsersListResult>, DropboxResponseError.<TeamExcludedUsersListContinueError>>}\n */\nroutes.teamMemberSpaceLimitsExcludedUsersListContinue = function (arg) {\n  return this.request('team/member_space_limits/excluded_users/list/continue', arg, 'team', 'api', 'rpc', 'members.read');\n};\n\n/**\n * Remove users from member space limits excluded users list.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMemberSpaceLimitsExcludedUsersRemove\n * @arg {TeamExcludedUsersUpdateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamExcludedUsersUpdateResult>, DropboxResponseError.<TeamExcludedUsersUpdateError>>}\n */\nroutes.teamMemberSpaceLimitsExcludedUsersRemove = function (arg) {\n  return this.request('team/member_space_limits/excluded_users/remove', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Get users custom quota. Returns none as the custom quota if none was set. A\n * maximum of 1000 members can be specified in a single call.\n * Route attributes:\n *   scope: members.read\n * @function Dropbox#teamMemberSpaceLimitsGetCustomQuota\n * @arg {TeamCustomQuotaUsersArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<Array.<TeamCustomQuotaResult>>, DropboxResponseError.<TeamCustomQuotaError>>}\n */\nroutes.teamMemberSpaceLimitsGetCustomQuota = function (arg) {\n  return this.request('team/member_space_limits/get_custom_quota', arg, 'team', 'api', 'rpc', 'members.read');\n};\n\n/**\n * Remove users custom quota. A maximum of 1000 members can be specified in a\n * single call.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMemberSpaceLimitsRemoveCustomQuota\n * @arg {TeamCustomQuotaUsersArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<Array.<TeamRemoveCustomQuotaResult>>, DropboxResponseError.<TeamCustomQuotaError>>}\n */\nroutes.teamMemberSpaceLimitsRemoveCustomQuota = function (arg) {\n  return this.request('team/member_space_limits/remove_custom_quota', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Set users custom quota. Custom quota has to be at least 15GB. A maximum of\n * 1000 members can be specified in a single call.\n * Route attributes:\n *   scope: members.read\n * @function Dropbox#teamMemberSpaceLimitsSetCustomQuota\n * @arg {TeamSetCustomQuotaArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<Array.<TeamCustomQuotaResult>>, DropboxResponseError.<TeamSetCustomQuotaError>>}\n */\nroutes.teamMemberSpaceLimitsSetCustomQuota = function (arg) {\n  return this.request('team/member_space_limits/set_custom_quota', arg, 'team', 'api', 'rpc', 'members.read');\n};\n\n/**\n * Adds members to a team. Permission : Team member management A maximum of 20\n * members can be specified in a single call. If no Dropbox account exists with\n * the email address specified, a new Dropbox account will be created with the\n * given email address, and that account will be invited to the team. If a\n * personal Dropbox account exists with the email address specified in the call,\n * this call will create a placeholder Dropbox account for the user on the team\n * and send an email inviting the user to migrate their existing personal\n * account onto the team. Team member management apps are required to set an\n * initial given_name and surname for a user to use in the team invitation and\n * for 'Perform as team member' actions taken on the user before they become\n * 'active'.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersAddV2\n * @arg {TeamMembersAddV2Arg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamMembersAddLaunchV2Result>, DropboxResponseError.<void>>}\n */\nroutes.teamMembersAddV2 = function (arg) {\n  return this.request('team/members/add_v2', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Adds members to a team. Permission : Team member management A maximum of 20\n * members can be specified in a single call. If no Dropbox account exists with\n * the email address specified, a new Dropbox account will be created with the\n * given email address, and that account will be invited to the team. If a\n * personal Dropbox account exists with the email address specified in the call,\n * this call will create a placeholder Dropbox account for the user on the team\n * and send an email inviting the user to migrate their existing personal\n * account onto the team. Team member management apps are required to set an\n * initial given_name and surname for a user to use in the team invitation and\n * for 'Perform as team member' actions taken on the user before they become\n * 'active'.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersAdd\n * @arg {TeamMembersAddArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamMembersAddLaunch>, DropboxResponseError.<void>>}\n */\nroutes.teamMembersAdd = function (arg) {\n  return this.request('team/members/add', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Once an async_job_id is returned from members/add_v2 , use this to poll the\n * status of the asynchronous request. Permission : Team member management.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersAddJobStatusGetV2\n * @arg {AsyncPollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamMembersAddJobStatusV2Result>, DropboxResponseError.<AsyncPollError>>}\n */\nroutes.teamMembersAddJobStatusGetV2 = function (arg) {\n  return this.request('team/members/add/job_status/get_v2', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Once an async_job_id is returned from members/add , use this to poll the\n * status of the asynchronous request. Permission : Team member management.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersAddJobStatusGet\n * @arg {AsyncPollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamMembersAddJobStatus>, DropboxResponseError.<AsyncPollError>>}\n */\nroutes.teamMembersAddJobStatusGet = function (arg) {\n  return this.request('team/members/add/job_status/get', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Deletes a team member's profile photo. Permission : Team member management.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersDeleteProfilePhotoV2\n * @arg {TeamMembersDeleteProfilePhotoArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamTeamMemberInfoV2Result>, DropboxResponseError.<TeamMembersDeleteProfilePhotoError>>}\n */\nroutes.teamMembersDeleteProfilePhotoV2 = function (arg) {\n  return this.request('team/members/delete_profile_photo_v2', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Deletes a team member's profile photo. Permission : Team member management.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersDeleteProfilePhoto\n * @arg {TeamMembersDeleteProfilePhotoArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamTeamMemberInfo>, DropboxResponseError.<TeamMembersDeleteProfilePhotoError>>}\n */\nroutes.teamMembersDeleteProfilePhoto = function (arg) {\n  return this.request('team/members/delete_profile_photo', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Get available TeamMemberRoles for the connected team. To be used with\n * members/set_admin_permissions_v2. Permission : Team member management.\n * Route attributes:\n *   scope: members.read\n * @function Dropbox#teamMembersGetAvailableTeamMemberRoles\n * @returns {Promise.<DropboxResponse<TeamMembersGetAvailableTeamMemberRolesResult>, DropboxResponseError.<void>>}\n */\nroutes.teamMembersGetAvailableTeamMemberRoles = function () {\n  return this.request('team/members/get_available_team_member_roles', null, 'team', 'api', 'rpc', 'members.read');\n};\n\n/**\n * Returns information about multiple team members. Permission : Team\n * information This endpoint will return MembersGetInfoItem.id_not_found, for\n * IDs (or emails) that cannot be matched to a valid team member.\n * Route attributes:\n *   scope: members.read\n * @function Dropbox#teamMembersGetInfoV2\n * @arg {TeamMembersGetInfoV2Arg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamMembersGetInfoV2Result>, DropboxResponseError.<TeamMembersGetInfoError>>}\n */\nroutes.teamMembersGetInfoV2 = function (arg) {\n  return this.request('team/members/get_info_v2', arg, 'team', 'api', 'rpc', 'members.read');\n};\n\n/**\n * Returns information about multiple team members. Permission : Team\n * information This endpoint will return MembersGetInfoItem.id_not_found, for\n * IDs (or emails) that cannot be matched to a valid team member.\n * Route attributes:\n *   scope: members.read\n * @function Dropbox#teamMembersGetInfo\n * @arg {TeamMembersGetInfoArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<Object>, DropboxResponseError.<TeamMembersGetInfoError>>}\n */\nroutes.teamMembersGetInfo = function (arg) {\n  return this.request('team/members/get_info', arg, 'team', 'api', 'rpc', 'members.read');\n};\n\n/**\n * Lists members of a team. Permission : Team information.\n * Route attributes:\n *   scope: members.read\n * @function Dropbox#teamMembersListV2\n * @arg {TeamMembersListArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamMembersListV2Result>, DropboxResponseError.<TeamMembersListError>>}\n */\nroutes.teamMembersListV2 = function (arg) {\n  return this.request('team/members/list_v2', arg, 'team', 'api', 'rpc', 'members.read');\n};\n\n/**\n * Lists members of a team. Permission : Team information.\n * Route attributes:\n *   scope: members.read\n * @function Dropbox#teamMembersList\n * @arg {TeamMembersListArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamMembersListResult>, DropboxResponseError.<TeamMembersListError>>}\n */\nroutes.teamMembersList = function (arg) {\n  return this.request('team/members/list', arg, 'team', 'api', 'rpc', 'members.read');\n};\n\n/**\n * Once a cursor has been retrieved from members/list_v2, use this to paginate\n * through all team members. Permission : Team information.\n * Route attributes:\n *   scope: members.read\n * @function Dropbox#teamMembersListContinueV2\n * @arg {TeamMembersListContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamMembersListV2Result>, DropboxResponseError.<TeamMembersListContinueError>>}\n */\nroutes.teamMembersListContinueV2 = function (arg) {\n  return this.request('team/members/list/continue_v2', arg, 'team', 'api', 'rpc', 'members.read');\n};\n\n/**\n * Once a cursor has been retrieved from members/list, use this to paginate\n * through all team members. Permission : Team information.\n * Route attributes:\n *   scope: members.read\n * @function Dropbox#teamMembersListContinue\n * @arg {TeamMembersListContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamMembersListResult>, DropboxResponseError.<TeamMembersListContinueError>>}\n */\nroutes.teamMembersListContinue = function (arg) {\n  return this.request('team/members/list/continue', arg, 'team', 'api', 'rpc', 'members.read');\n};\n\n/**\n * Moves removed member's files to a different member. This endpoint initiates\n * an asynchronous job. To obtain the final result of the job, the client should\n * periodically poll members/move_former_member_files/job_status/check.\n * Permission : Team member management.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersMoveFormerMemberFiles\n * @arg {TeamMembersDataTransferArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<AsyncLaunchEmptyResult>, DropboxResponseError.<TeamMembersTransferFormerMembersFilesError>>}\n */\nroutes.teamMembersMoveFormerMemberFiles = function (arg) {\n  return this.request('team/members/move_former_member_files', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Once an async_job_id is returned from members/move_former_member_files , use\n * this to poll the status of the asynchronous request. Permission : Team member\n * management.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersMoveFormerMemberFilesJobStatusCheck\n * @arg {AsyncPollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<AsyncPollEmptyResult>, DropboxResponseError.<AsyncPollError>>}\n */\nroutes.teamMembersMoveFormerMemberFilesJobStatusCheck = function (arg) {\n  return this.request('team/members/move_former_member_files/job_status/check', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Recover a deleted member. Permission : Team member management Exactly one of\n * team_member_id, email, or external_id must be provided to identify the user\n * account.\n * Route attributes:\n *   scope: members.delete\n * @function Dropbox#teamMembersRecover\n * @arg {TeamMembersRecoverArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<TeamMembersRecoverError>>}\n */\nroutes.teamMembersRecover = function (arg) {\n  return this.request('team/members/recover', arg, 'team', 'api', 'rpc', 'members.delete');\n};\n\n/**\n * Removes a member from a team. Permission : Team member management Exactly one\n * of team_member_id, email, or external_id must be provided to identify the\n * user account. Accounts can be recovered via members/recover for a 7 day\n * period or until the account has been permanently deleted or transferred to\n * another account (whichever comes first). Calling members/add while a user is\n * still recoverable on your team will return with\n * MemberAddResult.user_already_on_team. Accounts can have their files\n * transferred via the admin console for a limited time, based on the version\n * history length associated with the team (180 days for most teams). This\n * endpoint may initiate an asynchronous job. To obtain the final result of the\n * job, the client should periodically poll members/remove/job_status/get.\n * Route attributes:\n *   scope: members.delete\n * @function Dropbox#teamMembersRemove\n * @arg {TeamMembersRemoveArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<AsyncLaunchEmptyResult>, DropboxResponseError.<TeamMembersRemoveError>>}\n */\nroutes.teamMembersRemove = function (arg) {\n  return this.request('team/members/remove', arg, 'team', 'api', 'rpc', 'members.delete');\n};\n\n/**\n * Once an async_job_id is returned from members/remove , use this to poll the\n * status of the asynchronous request. Permission : Team member management.\n * Route attributes:\n *   scope: members.delete\n * @function Dropbox#teamMembersRemoveJobStatusGet\n * @arg {AsyncPollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<AsyncPollEmptyResult>, DropboxResponseError.<AsyncPollError>>}\n */\nroutes.teamMembersRemoveJobStatusGet = function (arg) {\n  return this.request('team/members/remove/job_status/get', arg, 'team', 'api', 'rpc', 'members.delete');\n};\n\n/**\n * Add secondary emails to users. Permission : Team member management. Emails\n * that are on verified domains will be verified automatically. For each email\n * address not on a verified domain a verification email will be sent.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersSecondaryEmailsAdd\n * @arg {TeamAddSecondaryEmailsArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamAddSecondaryEmailsResult>, DropboxResponseError.<TeamAddSecondaryEmailsError>>}\n */\nroutes.teamMembersSecondaryEmailsAdd = function (arg) {\n  return this.request('team/members/secondary_emails/add', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Delete secondary emails from users Permission : Team member management. Users\n * will be notified of deletions of verified secondary emails at both the\n * secondary email and their primary email.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersSecondaryEmailsDelete\n * @arg {TeamDeleteSecondaryEmailsArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamDeleteSecondaryEmailsResult>, DropboxResponseError.<void>>}\n */\nroutes.teamMembersSecondaryEmailsDelete = function (arg) {\n  return this.request('team/members/secondary_emails/delete', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Resend secondary email verification emails. Permission : Team member\n * management.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersSecondaryEmailsResendVerificationEmails\n * @arg {TeamResendVerificationEmailArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamResendVerificationEmailResult>, DropboxResponseError.<void>>}\n */\nroutes.teamMembersSecondaryEmailsResendVerificationEmails = function (arg) {\n  return this.request('team/members/secondary_emails/resend_verification_emails', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Sends welcome email to pending team member. Permission : Team member\n * management Exactly one of team_member_id, email, or external_id must be\n * provided to identify the user account. No-op if team member is not pending.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersSendWelcomeEmail\n * @arg {TeamUserSelectorArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<TeamMembersSendWelcomeError>>}\n */\nroutes.teamMembersSendWelcomeEmail = function (arg) {\n  return this.request('team/members/send_welcome_email', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Updates a team member's permissions. Permission : Team member management.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#********************************\n * @arg {TeamMembersSetPermissions2Arg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamMembersSetPermissions2Result>, DropboxResponseError.<TeamMembersSetPermissions2Error>>}\n */\nroutes.******************************** = function (arg) {\n  return this.request('team/members/set_admin_permissions_v2', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Updates a team member's permissions. Permission : Team member management.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersSetAdminPermissions\n * @arg {TeamMembersSetPermissionsArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamMembersSetPermissionsResult>, DropboxResponseError.<TeamMembersSetPermissionsError>>}\n */\nroutes.teamMembersSetAdminPermissions = function (arg) {\n  return this.request('team/members/set_admin_permissions', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Updates a team member's profile. Permission : Team member management.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersSetProfileV2\n * @arg {TeamMembersSetProfileArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamTeamMemberInfoV2Result>, DropboxResponseError.<TeamMembersSetProfileError>>}\n */\nroutes.teamMembersSetProfileV2 = function (arg) {\n  return this.request('team/members/set_profile_v2', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Updates a team member's profile. Permission : Team member management.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersSetProfile\n * @arg {TeamMembersSetProfileArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamTeamMemberInfo>, DropboxResponseError.<TeamMembersSetProfileError>>}\n */\nroutes.teamMembersSetProfile = function (arg) {\n  return this.request('team/members/set_profile', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Updates a team member's profile photo. Permission : Team member management.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersSetProfilePhotoV2\n * @arg {TeamMembersSetProfilePhotoArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamTeamMemberInfoV2Result>, DropboxResponseError.<TeamMembersSetProfilePhotoError>>}\n */\nroutes.teamMembersSetProfilePhotoV2 = function (arg) {\n  return this.request('team/members/set_profile_photo_v2', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Updates a team member's profile photo. Permission : Team member management.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersSetProfilePhoto\n * @arg {TeamMembersSetProfilePhotoArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamTeamMemberInfo>, DropboxResponseError.<TeamMembersSetProfilePhotoError>>}\n */\nroutes.teamMembersSetProfilePhoto = function (arg) {\n  return this.request('team/members/set_profile_photo', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Suspend a member from a team. Permission : Team member management Exactly one\n * of team_member_id, email, or external_id must be provided to identify the\n * user account.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersSuspend\n * @arg {TeamMembersDeactivateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<TeamMembersSuspendError>>}\n */\nroutes.teamMembersSuspend = function (arg) {\n  return this.request('team/members/suspend', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Unsuspend a member from a team. Permission : Team member management Exactly\n * one of team_member_id, email, or external_id must be provided to identify the\n * user account.\n * Route attributes:\n *   scope: members.write\n * @function Dropbox#teamMembersUnsuspend\n * @arg {TeamMembersUnsuspendArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<TeamMembersUnsuspendError>>}\n */\nroutes.teamMembersUnsuspend = function (arg) {\n  return this.request('team/members/unsuspend', arg, 'team', 'api', 'rpc', 'members.write');\n};\n\n/**\n * Returns a list of all team-accessible namespaces. This list includes team\n * folders, shared folders containing team members, team members' home\n * namespaces, and team members' app folders. Home namespaces and app folders\n * are always owned by this team or members of the team, but shared folders may\n * be owned by other users or other teams. Duplicates may occur in the list.\n * Route attributes:\n *   scope: team_data.member\n * @function Dropbox#teamNamespacesList\n * @arg {TeamTeamNamespacesListArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamTeamNamespacesListResult>, DropboxResponseError.<TeamTeamNamespacesListError>>}\n */\nroutes.teamNamespacesList = function (arg) {\n  return this.request('team/namespaces/list', arg, 'team', 'api', 'rpc', 'team_data.member');\n};\n\n/**\n * Once a cursor has been retrieved from namespaces/list, use this to paginate\n * through all team-accessible namespaces. Duplicates may occur in the list.\n * Route attributes:\n *   scope: team_data.member\n * @function Dropbox#teamNamespacesListContinue\n * @arg {TeamTeamNamespacesListContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamTeamNamespacesListResult>, DropboxResponseError.<TeamTeamNamespacesListContinueError>>}\n */\nroutes.teamNamespacesListContinue = function (arg) {\n  return this.request('team/namespaces/list/continue', arg, 'team', 'api', 'rpc', 'team_data.member');\n};\n\n/**\n * Permission : Team member file access.\n * Route attributes:\n *   scope: files.team_metadata.write\n * @function Dropbox#teamPropertiesTemplateAdd\n * @deprecated\n * @arg {FilePropertiesAddTemplateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilePropertiesAddTemplateResult>, DropboxResponseError.<FilePropertiesModifyTemplateError>>}\n */\nroutes.teamPropertiesTemplateAdd = function (arg) {\n  return this.request('team/properties/template/add', arg, 'team', 'api', 'rpc', 'files.team_metadata.write');\n};\n\n/**\n * Permission : Team member file access. The scope for the route is\n * files.team_metadata.write.\n * Route attributes:\n *   scope: files.team_metadata.write\n * @function Dropbox#teamPropertiesTemplateGet\n * @deprecated\n * @arg {FilePropertiesGetTemplateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilePropertiesGetTemplateResult>, DropboxResponseError.<FilePropertiesTemplateError>>}\n */\nroutes.teamPropertiesTemplateGet = function (arg) {\n  return this.request('team/properties/template/get', arg, 'team', 'api', 'rpc', 'files.team_metadata.write');\n};\n\n/**\n * Permission : Team member file access. The scope for the route is\n * files.team_metadata.write.\n * Route attributes:\n *   scope: files.team_metadata.write\n * @function Dropbox#teamPropertiesTemplateList\n * @deprecated\n * @returns {Promise.<DropboxResponse<FilePropertiesListTemplateResult>, DropboxResponseError.<FilePropertiesTemplateError>>}\n */\nroutes.teamPropertiesTemplateList = function () {\n  return this.request('team/properties/template/list', null, 'team', 'api', 'rpc', 'files.team_metadata.write');\n};\n\n/**\n * Permission : Team member file access.\n * Route attributes:\n *   scope: files.team_metadata.write\n * @function Dropbox#teamPropertiesTemplateUpdate\n * @deprecated\n * @arg {FilePropertiesUpdateTemplateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<FilePropertiesUpdateTemplateResult>, DropboxResponseError.<FilePropertiesModifyTemplateError>>}\n */\nroutes.teamPropertiesTemplateUpdate = function (arg) {\n  return this.request('team/properties/template/update', arg, 'team', 'api', 'rpc', 'files.team_metadata.write');\n};\n\n/**\n * Retrieves reporting data about a team's user activity. Deprecated: Will be\n * removed on July 1st 2021.\n * Route attributes:\n *   scope: team_info.read\n * @function Dropbox#teamReportsGetActivity\n * @deprecated\n * @arg {TeamDateRange} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamGetActivityReport>, DropboxResponseError.<TeamDateRangeError>>}\n */\nroutes.teamReportsGetActivity = function (arg) {\n  return this.request('team/reports/get_activity', arg, 'team', 'api', 'rpc', 'team_info.read');\n};\n\n/**\n * Retrieves reporting data about a team's linked devices. Deprecated: Will be\n * removed on July 1st 2021.\n * Route attributes:\n *   scope: team_info.read\n * @function Dropbox#teamReportsGetDevices\n * @deprecated\n * @arg {TeamDateRange} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamGetDevicesReport>, DropboxResponseError.<TeamDateRangeError>>}\n */\nroutes.teamReportsGetDevices = function (arg) {\n  return this.request('team/reports/get_devices', arg, 'team', 'api', 'rpc', 'team_info.read');\n};\n\n/**\n * Retrieves reporting data about a team's membership. Deprecated: Will be\n * removed on July 1st 2021.\n * Route attributes:\n *   scope: team_info.read\n * @function Dropbox#teamReportsGetMembership\n * @deprecated\n * @arg {TeamDateRange} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamGetMembershipReport>, DropboxResponseError.<TeamDateRangeError>>}\n */\nroutes.teamReportsGetMembership = function (arg) {\n  return this.request('team/reports/get_membership', arg, 'team', 'api', 'rpc', 'team_info.read');\n};\n\n/**\n * Retrieves reporting data about a team's storage usage. Deprecated: Will be\n * removed on July 1st 2021.\n * Route attributes:\n *   scope: team_info.read\n * @function Dropbox#teamReportsGetStorage\n * @deprecated\n * @arg {TeamDateRange} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamGetStorageReport>, DropboxResponseError.<TeamDateRangeError>>}\n */\nroutes.teamReportsGetStorage = function (arg) {\n  return this.request('team/reports/get_storage', arg, 'team', 'api', 'rpc', 'team_info.read');\n};\n\n/**\n * Endpoint adds Approve List entries. Changes are effective immediately.\n * Changes are committed in transaction. In case of single validation error -\n * all entries are rejected. Valid domains (RFC-1034/5) and emails\n * (RFC-5322/822) are accepted. Added entries cannot overflow limit of 10000\n * entries per team. Maximum 100 entries per call is allowed.\n * Route attributes:\n *   scope: team_info.write\n * @function Dropbox#teamSharingAllowlistAdd\n * @arg {TeamSharingAllowlistAddArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamSharingAllowlistAddResponse>, DropboxResponseError.<TeamSharingAllowlistAddError>>}\n */\nroutes.teamSharingAllowlistAdd = function (arg) {\n  return this.request('team/sharing_allowlist/add', arg, 'team', 'api', 'rpc', 'team_info.write');\n};\n\n/**\n * Lists Approve List entries for given team, from newest to oldest, returning\n * up to `limit` entries at a time. If there are more than `limit` entries\n * associated with the current team, more can be fetched by passing the returned\n * `cursor` to sharing_allowlist/list/continue.\n * Route attributes:\n *   scope: team_info.read\n * @function Dropbox#teamSharingAllowlistList\n * @arg {TeamSharingAllowlistListArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamSharingAllowlistListResponse>, DropboxResponseError.<TeamSharingAllowlistListError>>}\n */\nroutes.teamSharingAllowlistList = function (arg) {\n  return this.request('team/sharing_allowlist/list', arg, 'team', 'api', 'rpc', 'team_info.read');\n};\n\n/**\n * Lists entries associated with given team, starting from a the cursor. See\n * sharing_allowlist/list.\n * Route attributes:\n *   scope: team_info.read\n * @function Dropbox#teamSharingAllowlistListContinue\n * @arg {TeamSharingAllowlistListContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamSharingAllowlistListResponse>, DropboxResponseError.<TeamSharingAllowlistListContinueError>>}\n */\nroutes.teamSharingAllowlistListContinue = function (arg) {\n  return this.request('team/sharing_allowlist/list/continue', arg, 'team', 'api', 'rpc', 'team_info.read');\n};\n\n/**\n * Endpoint removes Approve List entries. Changes are effective immediately.\n * Changes are committed in transaction. In case of single validation error -\n * all entries are rejected. Valid domains (RFC-1034/5) and emails\n * (RFC-5322/822) are accepted. Entries being removed have to be present on the\n * list. Maximum 1000 entries per call is allowed.\n * Route attributes:\n *   scope: team_info.write\n * @function Dropbox#teamSharingAllowlistRemove\n * @arg {TeamSharingAllowlistRemoveArgs} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamSharingAllowlistRemoveResponse>, DropboxResponseError.<TeamSharingAllowlistRemoveError>>}\n */\nroutes.teamSharingAllowlistRemove = function (arg) {\n  return this.request('team/sharing_allowlist/remove', arg, 'team', 'api', 'rpc', 'team_info.write');\n};\n\n/**\n * Sets an archived team folder's status to active. Permission : Team member\n * file access.\n * Route attributes:\n *   scope: team_data.content.write\n * @function Dropbox#teamTeamFolderActivate\n * @arg {TeamTeamFolderIdArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamTeamFolderMetadata>, DropboxResponseError.<TeamTeamFolderActivateError>>}\n */\nroutes.teamTeamFolderActivate = function (arg) {\n  return this.request('team/team_folder/activate', arg, 'team', 'api', 'rpc', 'team_data.content.write');\n};\n\n/**\n * Sets an active team folder's status to archived and removes all folder and\n * file members. This endpoint cannot be used for teams that have a shared team\n * space. Permission : Team member file access.\n * Route attributes:\n *   scope: team_data.content.write\n * @function Dropbox#teamTeamFolderArchive\n * @arg {TeamTeamFolderArchiveArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamTeamFolderArchiveLaunch>, DropboxResponseError.<TeamTeamFolderArchiveError>>}\n */\nroutes.teamTeamFolderArchive = function (arg) {\n  return this.request('team/team_folder/archive', arg, 'team', 'api', 'rpc', 'team_data.content.write');\n};\n\n/**\n * Returns the status of an asynchronous job for archiving a team folder.\n * Permission : Team member file access.\n * Route attributes:\n *   scope: team_data.content.write\n * @function Dropbox#teamTeamFolderArchiveCheck\n * @arg {AsyncPollArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamTeamFolderArchiveJobStatus>, DropboxResponseError.<AsyncPollError>>}\n */\nroutes.teamTeamFolderArchiveCheck = function (arg) {\n  return this.request('team/team_folder/archive/check', arg, 'team', 'api', 'rpc', 'team_data.content.write');\n};\n\n/**\n * Creates a new, active, team folder with no members. This endpoint can only be\n * used for teams that do not already have a shared team space. Permission :\n * Team member file access.\n * Route attributes:\n *   scope: team_data.content.write\n * @function Dropbox#teamTeamFolderCreate\n * @arg {TeamTeamFolderCreateArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamTeamFolderMetadata>, DropboxResponseError.<TeamTeamFolderCreateError>>}\n */\nroutes.teamTeamFolderCreate = function (arg) {\n  return this.request('team/team_folder/create', arg, 'team', 'api', 'rpc', 'team_data.content.write');\n};\n\n/**\n * Retrieves metadata for team folders. Permission : Team member file access.\n * Route attributes:\n *   scope: team_data.content.read\n * @function Dropbox#teamTeamFolderGetInfo\n * @arg {TeamTeamFolderIdListArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<Array.<TeamTeamFolderGetInfoItem>>, DropboxResponseError.<void>>}\n */\nroutes.teamTeamFolderGetInfo = function (arg) {\n  return this.request('team/team_folder/get_info', arg, 'team', 'api', 'rpc', 'team_data.content.read');\n};\n\n/**\n * Lists all team folders. Permission : Team member file access.\n * Route attributes:\n *   scope: team_data.content.read\n * @function Dropbox#teamTeamFolderList\n * @arg {TeamTeamFolderListArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamTeamFolderListResult>, DropboxResponseError.<TeamTeamFolderListError>>}\n */\nroutes.teamTeamFolderList = function (arg) {\n  return this.request('team/team_folder/list', arg, 'team', 'api', 'rpc', 'team_data.content.read');\n};\n\n/**\n * Once a cursor has been retrieved from team_folder/list, use this to paginate\n * through all team folders. Permission : Team member file access.\n * Route attributes:\n *   scope: team_data.content.read\n * @function Dropbox#teamTeamFolderListContinue\n * @arg {TeamTeamFolderListContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamTeamFolderListResult>, DropboxResponseError.<TeamTeamFolderListContinueError>>}\n */\nroutes.teamTeamFolderListContinue = function (arg) {\n  return this.request('team/team_folder/list/continue', arg, 'team', 'api', 'rpc', 'team_data.content.read');\n};\n\n/**\n * Permanently deletes an archived team folder. This endpoint cannot be used for\n * teams that have a shared team space. Permission : Team member file access.\n * Route attributes:\n *   scope: team_data.content.write\n * @function Dropbox#teamTeamFolderPermanentlyDelete\n * @arg {TeamTeamFolderIdArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<void>, DropboxResponseError.<TeamTeamFolderPermanentlyDeleteError>>}\n */\nroutes.teamTeamFolderPermanentlyDelete = function (arg) {\n  return this.request('team/team_folder/permanently_delete', arg, 'team', 'api', 'rpc', 'team_data.content.write');\n};\n\n/**\n * Changes an active team folder's name. Permission : Team member file access.\n * Route attributes:\n *   scope: team_data.content.write\n * @function Dropbox#teamTeamFolderRename\n * @arg {TeamTeamFolderRenameArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamTeamFolderMetadata>, DropboxResponseError.<TeamTeamFolderRenameError>>}\n */\nroutes.teamTeamFolderRename = function (arg) {\n  return this.request('team/team_folder/rename', arg, 'team', 'api', 'rpc', 'team_data.content.write');\n};\n\n/**\n * Updates the sync settings on a team folder or its contents.  Use of this\n * endpoint requires that the team has team selective sync enabled.\n * Route attributes:\n *   scope: team_data.content.write\n * @function Dropbox#teamTeamFolderUpdateSyncSettings\n * @arg {TeamTeamFolderUpdateSyncSettingsArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamTeamFolderMetadata>, DropboxResponseError.<TeamTeamFolderUpdateSyncSettingsError>>}\n */\nroutes.teamTeamFolderUpdateSyncSettings = function (arg) {\n  return this.request('team/team_folder/update_sync_settings', arg, 'team', 'api', 'rpc', 'team_data.content.write');\n};\n\n/**\n * Returns the member profile of the admin who generated the team access token\n * used to make the call.\n * Route attributes:\n *   scope: team_info.read\n * @function Dropbox#teamTokenGetAuthenticatedAdmin\n * @returns {Promise.<DropboxResponse<TeamTokenGetAuthenticatedAdminResult>, DropboxResponseError.<TeamTokenGetAuthenticatedAdminError>>}\n */\nroutes.teamTokenGetAuthenticatedAdmin = function () {\n  return this.request('team/token/get_authenticated_admin', null, 'team', 'api', 'rpc', 'team_info.read');\n};\n\n/**\n * Retrieves team events. If the result's GetTeamEventsResult.has_more field is\n * true, call get_events/continue with the returned cursor to retrieve more\n * entries. If end_time is not specified in your request, you may use the\n * returned cursor to poll get_events/continue for new events. Many attributes\n * note 'may be missing due to historical data gap'. Note that the\n * file_operations category and & analogous paper events are not available on\n * all Dropbox Business plans /business/plans-comparison. Use\n * features/get_values\n * /developers/documentation/http/teams#team-features-get_values to check for\n * this feature. Permission : Team Auditing.\n * Route attributes:\n *   scope: events.read\n * @function Dropbox#teamLogGetEvents\n * @arg {TeamLogGetTeamEventsArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamLogGetTeamEventsResult>, DropboxResponseError.<TeamLogGetTeamEventsError>>}\n */\nroutes.teamLogGetEvents = function (arg) {\n  return this.request('team_log/get_events', arg, 'team', 'api', 'rpc', 'events.read');\n};\n\n/**\n * Once a cursor has been retrieved from get_events, use this to paginate\n * through all events. Permission : Team Auditing.\n * Route attributes:\n *   scope: events.read\n * @function Dropbox#teamLogGetEventsContinue\n * @arg {TeamLogGetTeamEventsContinueArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<TeamLogGetTeamEventsResult>, DropboxResponseError.<TeamLogGetTeamEventsContinueError>>}\n */\nroutes.teamLogGetEventsContinue = function (arg) {\n  return this.request('team_log/get_events/continue', arg, 'team', 'api', 'rpc', 'events.read');\n};\n\n/**\n * Get a list of feature values that may be configured for the current account.\n * Route attributes:\n *   scope: account_info.read\n * @function Dropbox#usersFeaturesGetValues\n * @arg {UsersUserFeaturesGetValuesBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<UsersUserFeaturesGetValuesBatchResult>, DropboxResponseError.<UsersUserFeaturesGetValuesBatchError>>}\n */\nroutes.usersFeaturesGetValues = function (arg) {\n  return this.request('users/features/get_values', arg, 'user', 'api', 'rpc', 'account_info.read');\n};\n\n/**\n * Get information about a user's account.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#usersGetAccount\n * @arg {UsersGetAccountArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<UsersBasicAccount>, DropboxResponseError.<UsersGetAccountError>>}\n */\nroutes.usersGetAccount = function (arg) {\n  return this.request('users/get_account', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Get information about multiple user accounts.  At most 300 accounts may be\n * queried per request.\n * Route attributes:\n *   scope: sharing.read\n * @function Dropbox#usersGetAccountBatch\n * @arg {UsersGetAccountBatchArg} arg - The request parameters.\n * @returns {Promise.<DropboxResponse<Object>, DropboxResponseError.<UsersGetAccountBatchError>>}\n */\nroutes.usersGetAccountBatch = function (arg) {\n  return this.request('users/get_account_batch', arg, 'user', 'api', 'rpc', 'sharing.read');\n};\n\n/**\n * Get information about the current user's account.\n * Route attributes:\n *   scope: account_info.read\n * @function Dropbox#usersGetCurrentAccount\n * @returns {Promise.<DropboxResponse<UsersFullAccount>, DropboxResponseError.<void>>}\n */\nroutes.usersGetCurrentAccount = function () {\n  return this.request('users/get_current_account', null, 'user', 'api', 'rpc', 'account_info.read');\n};\n\n/**\n * Get the space usage information for the current user's account.\n * Route attributes:\n *   scope: account_info.read\n * @function Dropbox#usersGetSpaceUsage\n * @returns {Promise.<DropboxResponse<UsersSpaceUsage>, DropboxResponseError.<void>>}\n */\nroutes.usersGetSpaceUsage = function () {\n  return this.request('users/get_space_usage', null, 'user', 'api', 'rpc', 'account_info.read');\n};\n\nexport { routes };\n", "import { DEFAULT_API_DOMAIN, DEFAULT_DOMAIN, TEST_DOMAIN_MAPPINGS } from './constants';\n\nfunction getSafeUnicode(c) {\n  const unicode = `000${c.charCodeAt(0).toString(16)}`.slice(-4);\n  return `\\\\u${unicode}`;\n}\n\nexport const baseApiUrl = (subdomain, domain = DEFAULT_API_DOMAIN, domainDelimiter = '.') => {\n  if (!domainDelimiter) {\n    return `https://${domain}/2/`;\n  }\n  if (domain !== DEFAULT_API_DOMAIN && TEST_DOMAIN_MAPPINGS[subdomain] !== undefined) {\n    subdomain = TEST_DOMAIN_MAPPINGS[subdomain];\n    domainDelimiter = '-';\n  }\n  return `https://${subdomain}${domainDelimiter}${domain}/2/`;\n};\nexport const OAuth2AuthorizationUrl = (domain = DEFAULT_DOMAIN) => {\n  if (domain !== DEFAULT_DOMAIN) {\n    domain = `meta-${domain}`;\n  }\n  return `https://${domain}/oauth2/authorize`;\n};\nexport const OAuth2TokenUrl = (domain = DEFAULT_API_DOMAIN, domainDelimiter = '.') => {\n  let subdomain = 'api';\n  if (domain !== DEFAULT_API_DOMAIN) {\n    subdomain = TEST_DOMAIN_MAPPINGS[subdomain];\n    domainDelimiter = '-';\n  }\n  return `https://${subdomain}${domainDelimiter}${domain}/oauth2/token`;\n};\n\n// source https://www.dropboxforum.com/t5/API-support/HTTP-header-quot-Dropbox-API-Arg-quot-could-not-decode-input-as/m-p/173823/highlight/true#M6786\nexport function httpHeaderSafeJson(args) {\n  return JSON.stringify(args).replace(/[\\u007f-\\uffff]/g, getSafeUnicode);\n}\n\nexport function getTokenExpiresAtDate(expiresIn) {\n  return new Date(Date.now() + (expiresIn * 1000));\n}\n\n/* global WorkerGlobalScope */\nexport function isWindowOrWorker() {\n  return (\n    (\n      typeof WorkerGlobalScope !== 'undefined'\n            && self instanceof WorkerGlobalScope // eslint-disable-line no-restricted-globals\n    )\n        || (\n          typeof module === 'undefined'\n            || typeof window !== 'undefined'\n        )\n  );\n}\n\nexport function isBrowserEnv() {\n  return typeof window !== 'undefined';\n}\n\nexport function isWorkerEnv() {\n  return typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope; // eslint-disable-line no-restricted-globals\n}\n\nexport function createBrowserSafeString(toBeConverted) {\n  const convertedString = toBeConverted.toString('base64')\n    .replace(/\\+/g, '-')\n    .replace(/\\//g, '_')\n    .replace(/=/g, '');\n  return convertedString;\n}\n", "/**\n * The response class of HTTP errors from API calls using the Dropbox SDK.\n * @class DropboxResponseError\n * @classdesc The response class of HTTP errors from API calls using the Dropbox SDK.\n * @arg {number} status - HTTP Status code of the call\n * @arg {Object} headers - Headers returned from the call\n * @arg {Object} error - Serialized Error of the call\n */\nexport class DropboxResponseError extends Error {\n  constructor(status, headers, error) {\n    super(`Response failed with a ${status} code`);\n    this.name = 'DropboxResponseError';\n    this.status = status;\n    this.headers = headers;\n    this.error = error;\n  }\n}\n", "import { isWindowOrWorker } from './utils.js';\nimport { DropboxResponseError } from './error.js';\n\nexport class DropboxResponse {\n  constructor(status, headers, result) {\n    this.status = status;\n    this.headers = headers;\n    this.result = result;\n  }\n}\n\nfunction throwAsError(res) {\n  return res.text()\n    .then((data) => {\n      let errorObject;\n      try {\n        errorObject = JSON.parse(data);\n      } catch (error) {\n        errorObject = data;\n      }\n\n      throw new DropboxResponseError(res.status, res.headers, errorObject);\n    });\n}\n\nexport function parseResponse(res) {\n  if (!res.ok) {\n    return throwAsError(res);\n  }\n  return res.text()\n    .then((data) => {\n      let responseObject;\n      try {\n        responseObject = JSON.parse(data);\n      } catch (error) {\n        responseObject = data;\n      }\n\n      return new DropboxResponse(res.status, res.headers, responseObject);\n    });\n}\n\nexport function parseDownloadResponse(res) {\n  if (!res.ok) {\n    return throwAsError(res);\n  }\n  return new Promise((resolve) => {\n    if (isWindowOrWorker()) {\n      res.blob().then((data) => resolve(data));\n    } else {\n      res.buffer().then((data) => resolve(data));\n    }\n  }).then((data) => {\n    const result = JSON.parse(res.headers.get('dropbox-api-result'));\n\n    if (isWindowOrWorker()) {\n      result.fileBlob = data;\n    } else {\n      result.fileBinary = data;\n    }\n\n    return new DropboxResponse(res.status, res.headers, result);\n  });\n}\n", "import {\n  getTokenExpiresAtDate,\n  isBrowserEnv,\n  createBrowser<PERSON>afeString,\n  OAuth2AuthorizationUrl,\n  OAuth2TokenUrl,\n  isWorkerEnv,\n} from './utils.js';\nimport { parseResponse } from './response.js';\n\nlet fetch;\nlet crypto;\nlet Encoder;\n\n// Expiration is 300 seconds but needs to be in milliseconds for Date object\nconst TokenExpirationBuffer = 300 * 1000;\nconst PKCELength = 128;\nconst TokenAccessTypes = ['legacy', 'offline', 'online'];\nconst GrantTypes = ['code', 'token'];\nconst IncludeGrantedScopes = ['none', 'user', 'team'];\n\n/**\n * @class DropboxAuth\n * @classdesc The DropboxAuth class that provides methods to manage, acquire, and refresh tokens.\n * @arg {Object} options\n * @arg {Function} [options.fetch] - fetch library for making requests.\n * @arg {String} [options.accessToken] - An access token for making authenticated\n * requests.\n * @arg {Date} [options.AccessTokenExpiresAt] - Date of the current access token's\n * expiration (if available)\n * @arg {String} [options.refreshToken] - A refresh token for retrieving access tokens\n * @arg {String} [options.clientId] - The client id for your app. Used to create\n * authentication URL.\n * @arg {String} [options.clientSecret] - The client secret for your app. Used to create\n * authentication URL and refresh access tokens.\n * @arg {String} [options.domain] - A custom domain to use when making api requests. This\n * should only be used for testing as scaffolding to avoid making network requests.\n * @arg {String} [options.domainDelimiter] - A custom delimiter to use when separating domain from\n * subdomain. This should only be used for testing as scaffolding.\n * @arg {Object} [options.customHeaders] - An object (in the form of header: value) designed to set\n * custom headers to use during a request.\n * @arg {Boolean} [options.dataOnBody] - Whether request data is sent on body or as URL params.\n  * Defaults to false.\n*/\nexport default class DropboxAuth {\n  constructor(options) {\n    options = options || {};\n\n    if (isBrowserEnv()) {\n      fetch = window.fetch.bind(window);\n      crypto = window.crypto || window.msCrypto; // for IE11\n    } else if (isWorkerEnv()) {\n      /* eslint-disable no-restricted-globals */\n      fetch = self.fetch.bind(self);\n      crypto = self.crypto;\n      /* eslint-enable no-restricted-globals */\n    } else {\n      fetch = require('node-fetch'); // eslint-disable-line global-require\n      crypto = require('crypto'); // eslint-disable-line global-require\n    }\n\n    if (typeof TextEncoder === 'undefined') {\n      Encoder = require('util').TextEncoder; // eslint-disable-line global-require\n    } else {\n      Encoder = TextEncoder;\n    }\n\n    this.fetch = options.fetch || fetch;\n    this.accessToken = options.accessToken;\n    this.accessTokenExpiresAt = options.accessTokenExpiresAt;\n    this.refreshToken = options.refreshToken;\n    this.clientId = options.clientId;\n    this.clientSecret = options.clientSecret;\n\n    this.domain = options.domain;\n    this.domainDelimiter = options.domainDelimiter;\n    this.customHeaders = options.customHeaders;\n    this.dataOnBody = options.dataOnBody;\n  }\n\n  /**\n     * Set the access token used to authenticate requests to the API.\n     * @arg {String} accessToken - An access token\n     * @returns {undefined}\n     */\n  setAccessToken(accessToken) {\n    this.accessToken = accessToken;\n  }\n\n  /**\n     * Get the access token\n     * @returns {String} Access token\n     */\n  getAccessToken() {\n    return this.accessToken;\n  }\n\n  /**\n     * Set the client id, which is used to help gain an access token.\n     * @arg {String} clientId - Your apps client id\n     * @returns {undefined}\n     */\n  setClientId(clientId) {\n    this.clientId = clientId;\n  }\n\n  /**\n     * Get the client id\n     * @returns {String} Client id\n     */\n  getClientId() {\n    return this.clientId;\n  }\n\n  /**\n     * Set the client secret\n     * @arg {String} clientSecret - Your app's client secret\n     * @returns {undefined}\n     */\n  setClientSecret(clientSecret) {\n    this.clientSecret = clientSecret;\n  }\n\n  /**\n     * Get the client secret\n     * @returns {String} Client secret\n     */\n  getClientSecret() {\n    return this.clientSecret;\n  }\n\n  /**\n     * Gets the refresh token\n     * @returns {String} Refresh token\n     */\n  getRefreshToken() {\n    return this.refreshToken;\n  }\n\n  /**\n     * Sets the refresh token\n     * @param refreshToken - A refresh token\n     */\n  setRefreshToken(refreshToken) {\n    this.refreshToken = refreshToken;\n  }\n\n  /**\n     * Gets the access token's expiration date\n     * @returns {Date} date of token expiration\n     */\n  getAccessTokenExpiresAt() {\n    return this.accessTokenExpiresAt;\n  }\n\n  /**\n     * Sets the access token's expiration date\n     * @param accessTokenExpiresAt - new expiration date\n     */\n  setAccessTokenExpiresAt(accessTokenExpiresAt) {\n    this.accessTokenExpiresAt = accessTokenExpiresAt;\n  }\n\n  /**\n     * Sets the code verifier for PKCE flow\n     * @param {String} codeVerifier - new code verifier\n     */\n  setCodeVerifier(codeVerifier) {\n    this.codeVerifier = codeVerifier;\n  }\n\n  /**\n     * Gets the code verifier for PKCE flow\n     * @returns {String} - code verifier for PKCE\n     */\n  getCodeVerifier() {\n    return this.codeVerifier;\n  }\n\n  generateCodeChallenge() {\n    const encoder = new Encoder();\n    const codeData = encoder.encode(this.codeVerifier);\n    let codeChallenge;\n    if (isBrowserEnv() || isWorkerEnv()) {\n      return crypto.subtle.digest('SHA-256', codeData)\n        .then((digestedHash) => {\n          const base64String = btoa(String.fromCharCode.apply(null, new Uint8Array(digestedHash)));\n          codeChallenge = createBrowserSafeString(base64String).substr(0, 128);\n          this.codeChallenge = codeChallenge;\n        });\n    }\n    const digestedHash = crypto.createHash('sha256').update(codeData).digest();\n    codeChallenge = createBrowserSafeString(digestedHash);\n    this.codeChallenge = codeChallenge;\n    return Promise.resolve();\n  }\n\n  generatePKCECodes() {\n    let codeVerifier;\n    if (isBrowserEnv() || isWorkerEnv()) {\n      const array = new Uint8Array(PKCELength);\n      const randomValueArray = crypto.getRandomValues(array);\n      const base64String = btoa(randomValueArray);\n      codeVerifier = createBrowserSafeString(base64String).substr(0, 128);\n    } else {\n      const randomBytes = crypto.randomBytes(PKCELength);\n      codeVerifier = createBrowserSafeString(randomBytes).substr(0, 128);\n    }\n    this.codeVerifier = codeVerifier;\n\n    return this.generateCodeChallenge();\n  }\n\n  /**\n     * Get a URL that can be used to authenticate users for the Dropbox API.\n     * @arg {String} redirectUri - A URL to redirect the user to after\n     * authenticating. This must be added to your app through the admin interface.\n     * @arg {String} [state] - State that will be returned in the redirect URL to help\n     * prevent cross site scripting attacks.\n     * @arg {String} [authType] - auth type, defaults to 'token', other option is 'code'\n     * @arg {String} [tokenAccessType] - type of token to request.  From the following:\n     * null - creates a token with the app default (either legacy or online)\n     * legacy - creates one long-lived token with no expiration\n     * online - create one short-lived token with an expiration\n     * offline - create one short-lived token with an expiration with a refresh token\n     * @arg {Array<String>} [scope] - scopes to request for the grant\n     * @arg {String} [includeGrantedScopes] - whether or not to include previously granted scopes.\n     * From the following:\n     * user - include user scopes in the grant\n     * team - include team scopes in the grant\n     * Note: if this user has never linked the app, include_granted_scopes must be None\n     * @arg {boolean} [usePKCE] - Whether or not to use Sha256 based PKCE. PKCE should be only use\n     * on client apps which doesn't call your server. It is less secure than non-PKCE flow but\n     * can be used if you are unable to safely retrieve your app secret\n     * @returns {Promise<String>} - Url to send user to for Dropbox API authentication\n     * returned in a promise\n     */\n  getAuthenticationUrl(redirectUri, state, authType = 'token', tokenAccessType = null, scope = null, includeGrantedScopes = 'none', usePKCE = false) {\n    const clientId = this.getClientId();\n    const baseUrl = OAuth2AuthorizationUrl(this.domain);\n\n    if (!clientId) {\n      throw new Error('A client id is required. You can set the client id using .setClientId().');\n    }\n    if (authType !== 'code' && !redirectUri) {\n      throw new Error('A redirect uri is required.');\n    }\n    if (!GrantTypes.includes(authType)) {\n      throw new Error('Authorization type must be code or token');\n    }\n    if (tokenAccessType && !TokenAccessTypes.includes(tokenAccessType)) {\n      throw new Error('Token Access Type must be legacy, offline, or online');\n    }\n    if (scope && !(scope instanceof Array)) {\n      throw new Error('Scope must be an array of strings');\n    }\n    if (!IncludeGrantedScopes.includes(includeGrantedScopes)) {\n      throw new Error('includeGrantedScopes must be none, user, or team');\n    }\n\n    let authUrl;\n    if (authType === 'code') {\n      authUrl = `${baseUrl}?response_type=code&client_id=${clientId}`;\n    } else {\n      authUrl = `${baseUrl}?response_type=token&client_id=${clientId}`;\n    }\n\n    if (redirectUri) {\n      authUrl += `&redirect_uri=${redirectUri}`;\n    }\n    if (state) {\n      authUrl += `&state=${state}`;\n    }\n    if (tokenAccessType) {\n      authUrl += `&token_access_type=${tokenAccessType}`;\n    }\n    if (scope) {\n      authUrl += `&scope=${scope.join(' ')}`;\n    }\n    if (includeGrantedScopes !== 'none') {\n      authUrl += `&include_granted_scopes=${includeGrantedScopes}`;\n    }\n    if (usePKCE) {\n      return this.generatePKCECodes()\n        .then(() => {\n          authUrl += '&code_challenge_method=S256';\n          authUrl += `&code_challenge=${this.codeChallenge}`;\n          return authUrl;\n        });\n    }\n    return Promise.resolve(authUrl);\n  }\n\n  /**\n     * Get an OAuth2 access token from an OAuth2 Code.\n     * @arg {String} redirectUri - A URL to redirect the user to after\n     * authenticating. This must be added to your app through the admin interface.\n     * @arg {String} code - An OAuth2 code.\n     * @returns {Object} An object containing the token and related info (if applicable)\n     */\n  getAccessTokenFromCode(redirectUri, code) {\n    const clientId = this.getClientId();\n    const clientSecret = this.getClientSecret();\n\n    if (!clientId) {\n      throw new Error('A client id is required. You can set the client id using .setClientId().');\n    }\n    let path = OAuth2TokenUrl(this.domain, this.domainDelimiter);\n    path += '?grant_type=authorization_code';\n    path += `&code=${code}`;\n    path += `&client_id=${clientId}`;\n\n    if (clientSecret) {\n      path += `&client_secret=${clientSecret}`;\n    } else {\n      if (!this.codeVerifier) {\n        throw new Error('You must use PKCE when generating the authorization URL to not include a client secret');\n      }\n      path += `&code_verifier=${this.codeVerifier}`;\n    }\n    if (redirectUri) {\n      path += `&redirect_uri=${redirectUri}`;\n    }\n\n    const fetchOptions = {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n      },\n    };\n    return this.fetch(path, fetchOptions)\n      .then((res) => parseResponse(res));\n  }\n\n  /**\n     * Checks if a token is needed, can be refreshed and if the token is expired.\n     * If so, attempts to refresh access token\n     * @returns {Promise<*>}\n     */\n  checkAndRefreshAccessToken() {\n    const canRefresh = this.getRefreshToken() && this.getClientId();\n    const needsRefresh = !this.getAccessTokenExpiresAt()\n            || (new Date(Date.now() + TokenExpirationBuffer)) >= this.getAccessTokenExpiresAt();\n    const needsToken = !this.getAccessToken();\n    if ((needsRefresh || needsToken) && canRefresh) {\n      return this.refreshAccessToken();\n    }\n    return Promise.resolve();\n  }\n\n  /**\n     * Refreshes the access token using the refresh token, if available\n     * @arg {Array<String>} scope - a subset of scopes from the original\n     * refresh to acquire with an access token\n     * @returns {Promise<*>}\n     */\n  refreshAccessToken(scope = null) {\n    const clientId = this.getClientId();\n    const clientSecret = this.getClientSecret();\n\n    if (!clientId) {\n      throw new Error('A client id is required. You can set the client id using .setClientId().');\n    }\n    if (scope && !(scope instanceof Array)) {\n      throw new Error('Scope must be an array of strings');\n    }\n\n    let refreshUrl = OAuth2TokenUrl(this.domain, this.domainDelimiter);\n    const fetchOptions = {\n      headers: { 'Content-Type': 'application/json' },\n      method: 'POST',\n    };\n\n    if (this.dataOnBody) {\n      const body = { grant_type: 'refresh_token', client_id: clientId, refresh_token: this.getRefreshToken() };\n\n      if (clientSecret) {\n        body.client_secret = clientSecret;\n      }\n      if (scope) {\n        body.scope = scope.join(' ');\n      }\n\n      fetchOptions.body = body;\n    } else {\n      refreshUrl += `?grant_type=refresh_token&refresh_token=${this.getRefreshToken()}`;\n      refreshUrl += `&client_id=${clientId}`;\n      if (clientSecret) {\n        refreshUrl += `&client_secret=${clientSecret}`;\n      }\n      if (scope) {\n        refreshUrl += `&scope=${scope.join(' ')}`;\n      }\n    }\n\n    return this.fetch(refreshUrl, fetchOptions)\n      .then((res) => parseResponse(res))\n      .then((res) => {\n        this.setAccessToken(res.result.access_token);\n        this.setAccessTokenExpiresAt(getTokenExpiresAtDate(res.result.expires_in));\n      });\n  }\n}\n", "import {\n  UPLOAD,\n  DOWNLOAD,\n  RPC,\n  APP_AUTH,\n  TEAM_AUTH,\n  USER_AUTH,\n  NO_AUTH,\n  COOKIE,\n} from './constants.js';\nimport { routes } from '../lib/routes.js';\nimport DropboxAuth from './auth.js';\nimport { baseApiUrl, httpHeaderSafeJson } from './utils.js';\nimport { parseDownloadResponse, parseResponse } from './response.js';\n\nconst b64 = typeof btoa === 'undefined'\n  ? (str) => Buffer.from(str).toString('base64')\n  : btoa;\n\n/**\n * @class Dropbox\n * @classdesc The Dropbox SDK class that provides methods to read, write and\n * create files or folders in a user or team's Dropbox.\n * @arg {Object} options\n * @arg {Function} [options.fetch] - fetch library for making requests.\n * @arg {String} [options.selectUser] - Select user is only used for team functionality.\n * It specifies which user the team access token should be acting as.\n * @arg {String} [options.pathRoot] - root path to access other namespaces\n * Use to access team folders for example\n * @arg {String} [options.selectAdmin] - Select admin is only used by team functionality.\n * It specifies which team admin the team access token should be acting as.\n * @arg {DropboxAuth} [options.auth] - The DropboxAuth object used to authenticate requests.\n * If this is set, the remaining parameters will be ignored.\n * @arg {String} [options.accessToken] - An access token for making authenticated\n * requests.\n * @arg {Date} [options.accessTokenExpiresAt] - Date of the current access token's\n * expiration (if available)\n * @arg {String} [options.refreshToken] - A refresh token for retrieving access tokens\n * @arg {String} [options.clientId] - The client id for your app. Used to create\n * authentication URL.\n * @arg {String} [options.clientSecret] - The client secret for your app. Used to create\n * authentication URL and refresh access tokens.\n * @arg {String} [options.domain] - A custom domain to use when making api requests. This\n * should only be used for testing as scaffolding to avoid making network requests.\n * @arg {String} [options.domainDelimiter] - A custom delimiter to use when separating domain from\n * subdomain. This should only be used for testing as scaffolding.\n * @arg {Object} [options.customHeaders] - An object (in the form of header: value) designed to set\n * custom headers to use during a request.\n */\nexport default class Dropbox {\n  constructor(options) {\n    options = options || {};\n\n    if (options.auth) {\n      this.auth = options.auth;\n    } else {\n      this.auth = new DropboxAuth(options);\n    }\n\n    this.fetch = options.fetch || this.auth.fetch;\n    this.selectUser = options.selectUser;\n    this.selectAdmin = options.selectAdmin;\n    this.pathRoot = options.pathRoot;\n\n    this.domain = options.domain || this.auth.domain;\n    this.domainDelimiter = options.domainDelimiter || this.auth.domainDelimiter;\n    this.customHeaders = options.customHeaders || this.auth.customHeaders;\n\n    Object.assign(this, routes);\n  }\n\n  request(path, args, auth, host, style) {\n    // scope is provided after \"style\", but unused in requests, so it's not in parameters\n    switch (style) {\n      case RPC:\n        return this.rpcRequest(path, args, auth, host);\n      case DOWNLOAD:\n        return this.downloadRequest(path, args, auth, host);\n      case UPLOAD:\n        return this.uploadRequest(path, args, auth, host);\n      default:\n        throw new Error(`Invalid request style: ${style}`);\n    }\n  }\n\n  rpcRequest(path, body, auth, host) {\n    return this.auth.checkAndRefreshAccessToken()\n      .then(() => {\n        const fetchOptions = {\n          method: 'POST',\n          body: (body) ? JSON.stringify(body) : null,\n          headers: {},\n        };\n\n        if (body) {\n          fetchOptions.headers['Content-Type'] = 'application/json';\n        }\n\n        this.setAuthHeaders(auth, fetchOptions);\n        this.setCommonHeaders(fetchOptions);\n\n        return fetchOptions;\n      })\n      .then((fetchOptions) => this.fetch(\n        baseApiUrl(host, this.domain, this.domainDelimiter) + path,\n        fetchOptions,\n      ))\n      .then((res) => parseResponse(res));\n  }\n\n  downloadRequest(path, args, auth, host) {\n    return this.auth.checkAndRefreshAccessToken()\n      .then(() => {\n        const fetchOptions = {\n          method: 'POST',\n          headers: {\n            'Dropbox-API-Arg': httpHeaderSafeJson(args),\n          },\n        };\n\n        this.setAuthHeaders(auth, fetchOptions);\n        this.setCommonHeaders(fetchOptions);\n\n        return fetchOptions;\n      })\n      .then((fetchOptions) => this.fetch(\n        baseApiUrl(host, this.domain, this.domainDelimiter) + path,\n        fetchOptions,\n      ))\n      .then((res) => parseDownloadResponse(res));\n  }\n\n  uploadRequest(path, args, auth, host) {\n    return this.auth.checkAndRefreshAccessToken()\n      .then(() => {\n        const { contents } = args;\n        delete args.contents;\n\n        const fetchOptions = {\n          body: contents,\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/octet-stream',\n            'Dropbox-API-Arg': httpHeaderSafeJson(args),\n          },\n        };\n\n        this.setAuthHeaders(auth, fetchOptions);\n        this.setCommonHeaders(fetchOptions);\n\n        return fetchOptions;\n      })\n      .then((fetchOptions) => this.fetch(\n        baseApiUrl(host, this.domain, this.domainDelimiter) + path,\n        fetchOptions,\n      ))\n      .then((res) => parseResponse(res));\n  }\n\n  setAuthHeaders(auth, fetchOptions) {\n    // checks for multiauth and assigns auth based on priority to create header in switch case\n    if (auth.split(',').length > 1) {\n      const authTypes = auth.replace(' ', '').split(',');\n      if (authTypes.includes(USER_AUTH) && this.auth.getAccessToken()) {\n        auth = USER_AUTH;\n      } else if (authTypes.includes(TEAM_AUTH) && this.auth.getAccessToken()) {\n        auth = TEAM_AUTH;\n      } else if (authTypes.includes(APP_AUTH)) {\n        auth = APP_AUTH;\n      }\n    }\n\n    switch (auth) {\n      case APP_AUTH:\n        if (this.auth.clientId && this.auth.clientSecret) {\n          const authHeader = b64(`${this.auth.clientId}:${this.auth.clientSecret}`);\n          fetchOptions.headers.Authorization = `Basic ${authHeader}`;\n        }\n        break;\n      case TEAM_AUTH:\n      case USER_AUTH:\n        if (this.auth.getAccessToken()) {\n          fetchOptions.headers.Authorization = `Bearer ${this.auth.getAccessToken()}`;\n        }\n        break;\n      case NO_AUTH:\n      case COOKIE:\n        break;\n      default:\n        throw new Error(`Unhandled auth type: ${auth}`);\n    }\n  }\n\n  setCommonHeaders(options) {\n    if (this.selectUser) {\n      options.headers['Dropbox-API-Select-User'] = this.selectUser;\n    }\n    if (this.selectAdmin) {\n      options.headers['Dropbox-API-Select-Admin'] = this.selectAdmin;\n    }\n    if (this.pathRoot) {\n      options.headers['Dropbox-API-Path-Root'] = this.pathRoot;\n    }\n    if (this.customHeaders) {\n      const headerKeys = Object.keys(this.customHeaders);\n      headerKeys.forEach((header) => {\n        options.headers[header] = this.customHeaders[header];\n      });\n    }\n  }\n}\n"], "names": ["RPC", "UPLOAD", "DOWNLOAD", "APP_AUTH", "USER_AUTH", "TEAM_AUTH", "NO_AUTH", "COOKIE", "DEFAULT_API_DOMAIN", "DEFAULT_DOMAIN", "TEST_DOMAIN_MAPPINGS", "api", "notify", "content", "routes", "accountSetProfilePhoto", "arg", "request", "authTokenFromOauth1", "authTokenRevoke", "checkApp", "checkUser", "contactsDeleteManualContacts", "contactsDeleteManualContactsBatch", "filePropertiesPropertiesAdd", "filePropertiesPropertiesOverwrite", "filePropertiesPropertiesRemove", "filePropertiesPropertiesSearch", "filePropertiesPropertiesSearchContinue", "filePropertiesPropertiesUpdate", "filePropertiesTemplatesAddForTeam", "filePropertiesTemplatesAddForUser", "filePropertiesTemplatesGetForTeam", "filePropertiesTemplatesGetForUser", "filePropertiesTemplatesListForTeam", "filePropertiesTemplatesListForUser", "filePropertiesTemplatesRemoveForTeam", "filePropertiesTemplatesRemoveForUser", "filePropertiesTemplatesUpdateForTeam", "filePropertiesTemplatesUpdateForUser", "fileRequestsCount", "fileRequestsCreate", "fileRequestsDelete", "fileRequestsDeleteAllClosed", "fileRequestsGet", "fileRequestsListV2", "fileRequestsList", "fileRequestsListContinue", "fileRequestsUpdate", "filesAlphaGetMetadata", "filesAlphaUpload", "filesCopyV2", "filesCopy", "filesCopyBatchV2", "filesCopyBatch", "filesCopyBatchCheckV2", "filesCopyBatchCheck", "filesCopyReferenceGet", "filesCopyReferenceSave", "filesCreateFolderV2", "filesCreateFolder", "filesCreateFolderBatch", "filesCreateFolderBatchCheck", "filesDeleteV2", "filesDelete", "filesDeleteBatch", "filesDeleteBatchCheck", "filesDownload", "filesDownloadZip", "filesExport", "filesGetFileLockBatch", "filesGetMetadata", "filesGetPreview", "filesGetTemporaryLink", "filesGetTemporaryUploadLink", "filesGetThumbnail", "filesGetThumbnailV2", "filesGetThumbnailBatch", "filesListFolder", "filesListFolderContinue", "filesListFolderGetLatestCursor", "filesListFolderLongpoll", "filesListRevisions", "filesLockFileBatch", "filesMoveV2", "filesMove", "filesMoveBatchV2", "filesMoveBatch", "filesMoveBatchCheckV2", "filesMoveBatchCheck", "filesPaperCreate", "filesPaperUpdate", "filesPermanentlyDelete", "filesPropertiesAdd", "filesPropertiesOverwrite", "filesPropertiesRemove", "filesPropertiesTemplateGet", "filesPropertiesTemplateList", "filesPropertiesUpdate", "filesRestore", "filesSaveUrl", "filesSaveUrlCheckJobStatus", "filesSearch", "filesSearchV2", "filesSearchContinueV2", "filesTagsAdd", "filesTagsGet", "filesTagsRemove", "filesUnlockFileBatch", "filesUpload", "filesUploadSessionAppendV2", "filesUploadSessionAppend", "filesUploadSessionFinish", "filesUploadSessionFinishBatch", "filesUploadSessionFinishBatchV2", "filesUploadSessionFinishBatchCheck", "filesUploadSessionStart", "filesUploadSessionStartBatch", "openidUserinfo", "paperDocsArchive", "paperDocsCreate", "paperDocsDownload", "paperDocsFolderUsersList", "paperDocsFolderUsersListContinue", "paperDocsGetFolderInfo", "paperDocsList", "paperDocsListContinue", "paperDocsPermanentlyDelete", "paperDocsSharingPolicyGet", "paperDocsSharingPolicySet", "paperDocsUpdate", "paperDocsUsersAdd", "paperDocsUsersList", "paperDocsUsersListContinue", "paperDocsUsersRemove", "paperFoldersCreate", "sharingAddFileMember", "sharingAddFolderMember", "sharingCheckJobStatus", "sharingCheckRemoveMemberJobStatus", "sharingCheckShareJobStatus", "sharingCreateSharedLink", "sharingCreateSharedLinkWithSettings", "sharingGetFileMetadata", "sharingGetFileMetadataBatch", "sharingGetFolderMetadata", "sharingGetSharedLinkFile", "sharingGetSharedLinkMetadata", "sharingGetSharedLinks", "sharingListFileMembers", "sharingListFileMembersBatch", "sharingListFileMembersContinue", "sharingListFolderMembers", "sharingListFolderMembersContinue", "sharingListFolders", "sharingListFoldersContinue", "sharingListMountableFolders", "sharingListMountableFoldersContinue", "sharingListReceivedFiles", "sharingListReceivedFilesContinue", "sharingListSharedLinks", "sharingModifySharedLinkSettings", "sharingMountFolder", "sharingRelinquishFileMembership", "sharingRelinquishFolderMembership", "sharingRemoveFileMember", "sharingRemoveFileMember2", "sharingRemoveFolderMember", "sharingRevokeSharedLink", "sharingSetAccessInheritance", "sharingShareFolder", "sharingTransferFolder", "sharingUnmountFolder", "sharingUnshareFile", "sharingUnshareFolder", "sharingUpdateFileMember", "sharingUpdateFolderMember", "sharingUpdateFolderPolicy", "teamDevicesListMemberDevices", "teamDevicesListMembersDevices", "teamDevicesListTeamDevices", "teamDevicesRevokeDeviceSession", "teamDevicesRevokeDeviceSessionBatch", "teamFeaturesGetValues", "teamGetInfo", "teamGroupsCreate", "teamGroupsDelete", "teamGroupsGetInfo", "teamGroupsJobStatusGet", "teamGroupsList", "teamGroupsListContinue", "teamGroupsMembersAdd", "teamGroupsMembersList", "teamGroupsMembersListContinue", "teamGroupsMembersRemove", "teamGroupsMembersSetAccessType", "teamGroupsUpdate", "teamLegalHoldsCreatePolicy", "teamLegalHoldsGetPolicy", "teamLegalHoldsListHeldRevisions", "teamLegalHoldsListHeldRevisionsContinue", "teamLegalHoldsListPolicies", "teamLegalHoldsReleasePolicy", "teamLegalHoldsUpdatePolicy", "teamLinkedAppsListMemberLinkedApps", "teamLinkedAppsListMembersLinkedApps", "********************************", "teamLinkedAppsRevokeLinkedApp", "teamLinkedAppsRevokeLinkedAppBatch", "teamMemberSpaceLimitsExcludedUsersAdd", "teamMemberSpaceLimitsExcludedUsersList", "teamMemberSpaceLimitsExcludedUsersListContinue", "teamMemberSpaceLimitsExcludedUsersRemove", "teamMemberSpaceLimitsGetCustomQuota", "teamMemberSpaceLimitsRemoveCustomQuota", "teamMemberSpaceLimitsSetCustomQuota", "teamMembersAddV2", "teamMembersAdd", "teamMembersAddJobStatusGetV2", "teamMembersAddJobStatusGet", "teamMembersDeleteProfilePhotoV2", "teamMembersDeleteProfilePhoto", "teamMembersGetAvailableTeamMemberRoles", "teamMembersGetInfoV2", "teamMembersGetInfo", "teamMembersListV2", "teamMembersList", "teamMembersListContinueV2", "teamMembersListContinue", "teamMembersMoveFormerMemberFiles", "teamMembersMoveFormerMemberFilesJobStatusCheck", "teamMembersRecover", "teamMembersRemove", "teamMembersRemoveJobStatusGet", "teamMembersSecondaryEmailsAdd", "teamMembersSecondaryEmailsDelete", "teamMembersSecondaryEmailsResendVerificationEmails", "teamMembersSendWelcomeEmail", "********************************", "teamMembersSetAdminPermissions", "teamMembersSetProfileV2", "teamMembersSetProfile", "teamMembersSetProfilePhotoV2", "teamMembersSetProfilePhoto", "teamMembersSuspend", "teamMembersUnsuspend", "teamNamespacesList", "teamNamespacesListContinue", "teamPropertiesTemplateAdd", "teamPropertiesTemplateGet", "teamPropertiesTemplateList", "teamPropertiesTemplateUpdate", "teamReportsGetActivity", "teamReportsGetDevices", "teamReportsGetMembership", "teamReportsGetStorage", "teamSharingAllowlistAdd", "teamSharingAllowlistList", "teamSharingAllowlistListContinue", "teamSharingAllowlistRemove", "teamTeamFolderActivate", "teamTeamFolderArchive", "teamTeamFolderArchiveCheck", "teamTeamFolderCreate", "teamTeamFolderGetInfo", "teamTeamFolderList", "teamTeamFolderListContinue", "teamTeamFolderPermanentlyDelete", "teamTeamFolderRename", "teamTeamFolderUpdateSyncSettings", "teamTokenGetAuthenticatedAdmin", "teamLogGetEvents", "teamLogGetEventsContinue", "usersFeaturesGetValues", "usersGetAccount", "usersGetAccountBatch", "usersGetCurrentAccount", "usersGetSpaceUsage", "getSafeUnicode", "c", "unicode", "charCodeAt", "toString", "slice", "baseApiUrl", "subdomain", "domain", "domainDelimiter", "undefined", "OAuth2AuthorizationUrl", "OAuth2TokenUrl", "httpHeaderSafeJson", "args", "JSON", "stringify", "replace", "getTokenExpiresAtDate", "expiresIn", "Date", "now", "isWindowOrWorker", "WorkerGlobalScope", "self", "module", "window", "isBrowserEnv", "isWorkerEnv", "createBrowserSafeString", "toBeConverted", "convertedString", "DropboxResponseError", "status", "headers", "error", "name", "Error", "DropboxResponse", "result", "throwAsError", "res", "text", "then", "data", "errorObject", "parse", "parseResponse", "ok", "responseObject", "parseDownloadResponse", "Promise", "resolve", "blob", "buffer", "get", "fileBlob", "fileBinary", "fetch", "crypto", "Encoder", "TokenExpirationBuffer", "PKCELength", "TokenAccessTypes", "GrantTypes", "IncludeGrantedScopes", "DropboxAuth", "options", "bind", "msCrypto", "require", "TextEncoder", "accessToken", "accessTokenExpiresAt", "refreshToken", "clientId", "clientSecret", "customHeaders", "dataOnBody", "codeVerifier", "encoder", "codeData", "encode", "codeChallenge", "subtle", "digest", "digestedHash", "base64String", "btoa", "String", "fromCharCode", "apply", "Uint8Array", "substr", "createHash", "update", "array", "randomValueArray", "getRandomValues", "randomBytes", "generateCodeChallenge", "redirectUri", "state", "authType", "tokenAccessType", "scope", "includeGrantedScopes", "usePKCE", "getClientId", "baseUrl", "includes", "Array", "authUrl", "join", "generatePKCECodes", "code", "getClientSecret", "path", "fetchOptions", "method", "canRefresh", "getRefreshToken", "needsRefresh", "getAccessTokenExpiresAt", "needsToken", "getAccessToken", "refreshAccessToken", "refreshUrl", "body", "grant_type", "client_id", "refresh_token", "client_secret", "setAccessToken", "access_token", "setAccessTokenExpiresAt", "expires_in", "b64", "str", "<PERSON><PERSON><PERSON>", "from", "Dropbox", "auth", "selectUser", "selectAdmin", "pathRoot", "Object", "assign", "host", "style", "rpcRequest", "downloadRequest", "uploadRequest", "checkAndRefreshAccessToken", "setAuthHeaders", "setCommonHeaders", "contents", "split", "length", "authTypes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Authorization", "header<PERSON><PERSON><PERSON>", "keys", "for<PERSON>ach", "header"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAO,IAAMA,GAAG,GAAG,KAAZ;EACA,IAAMC,MAAM,GAAG,QAAf;EACA,IAAMC,QAAQ,GAAG,UAAjB;EAEA,IAAMC,QAAQ,GAAG,KAAjB;EACA,IAAMC,SAAS,GAAG,MAAlB;EACA,IAAMC,SAAS,GAAG,MAAlB;EACA,IAAMC,OAAO,GAAG,QAAhB;EACA,IAAMC,MAAM,GAAG,QAAf;EAEA,IAAMC,kBAAkB,GAAG,gBAA3B;EACA,IAAMC,cAAc,GAAG,aAAvB;EAEA,IAAMC,oBAAoB,GAAG;EAClCC,EAAAA,GAAG,EAAE,KAD6B;EAElCC,EAAAA,MAAM,EAAE,MAF0B;EAGlCC,EAAAA,OAAO,EAAE;EAHyB,CAA7B;;ECbP;EACA,IAAIC,MAAM,GAAG,EAAb;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACAA,MAAM,CAACC,sBAAP,GAAgC,UAAUC,GAAV,EAAe;EAC7C,SAAO,KAAKC,OAAL,CAAa,2BAAb,EAA0CD,GAA1C,EAA+C,MAA/C,EAAuD,KAAvD,EAA8D,KAA9D,EAAqE,oBAArE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACI,mBAAP,GAA6B,UAAUF,GAAV,EAAe;EAC1C,SAAO,KAAKC,OAAL,CAAa,wBAAb,EAAuCD,GAAvC,EAA4C,KAA5C,EAAmD,KAAnD,EAA0D,KAA1D,EAAiE,IAAjE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACK,eAAP,GAAyB,YAAY;EACnC,SAAO,KAAKF,OAAL,CAAa,mBAAb,EAAkC,IAAlC,EAAwC,MAAxC,EAAgD,KAAhD,EAAuD,KAAvD,EAA8D,IAA9D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAH,MAAM,CAACM,QAAP,GAAkB,UAAUJ,GAAV,EAAe;EAC/B,SAAO,KAAKC,OAAL,CAAa,WAAb,EAA0BD,GAA1B,EAA+B,KAA/B,EAAsC,KAAtC,EAA6C,KAA7C,EAAoD,IAApD,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACO,SAAP,GAAmB,UAAUL,GAAV,EAAe;EAChC,SAAO,KAAKC,OAAL,CAAa,YAAb,EAA2BD,GAA3B,EAAgC,MAAhC,EAAwC,KAAxC,EAA+C,KAA/C,EAAsD,mBAAtD,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACQ,4BAAP,GAAsC,YAAY;EAChD,SAAO,KAAKL,OAAL,CAAa,iCAAb,EAAgD,IAAhD,EAAsD,MAAtD,EAA8D,KAA9D,EAAqE,KAArE,EAA4E,gBAA5E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAH,MAAM,CAACS,iCAAP,GAA2C,UAAUP,GAAV,EAAe;EACxD,SAAO,KAAKC,OAAL,CAAa,uCAAb,EAAsDD,GAAtD,EAA2D,MAA3D,EAAmE,KAAnE,EAA0E,KAA1E,EAAiF,gBAAjF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACU,2BAAP,GAAqC,UAAUR,GAAV,EAAe;EAClD,SAAO,KAAKC,OAAL,CAAa,gCAAb,EAA+CD,GAA/C,EAAoD,MAApD,EAA4D,KAA5D,EAAmE,KAAnE,EAA0E,sBAA1E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACW,iCAAP,GAA2C,UAAUT,GAAV,EAAe;EACxD,SAAO,KAAKC,OAAL,CAAa,sCAAb,EAAqDD,GAArD,EAA0D,MAA1D,EAAkE,KAAlE,EAAyE,KAAzE,EAAgF,sBAAhF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACY,8BAAP,GAAwC,UAAUV,GAAV,EAAe;EACrD,SAAO,KAAKC,OAAL,CAAa,mCAAb,EAAkDD,GAAlD,EAAuD,MAAvD,EAA+D,KAA/D,EAAsE,KAAtE,EAA6E,sBAA7E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACa,8BAAP,GAAwC,UAAUX,GAAV,EAAe;EACrD,SAAO,KAAKC,OAAL,CAAa,mCAAb,EAAkDD,GAAlD,EAAuD,MAAvD,EAA+D,KAA/D,EAAsE,KAAtE,EAA6E,qBAA7E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACc,sCAAP,GAAgD,UAAUZ,GAAV,EAAe;EAC7D,SAAO,KAAKC,OAAL,CAAa,4CAAb,EAA2DD,GAA3D,EAAgE,MAAhE,EAAwE,KAAxE,EAA+E,KAA/E,EAAsF,qBAAtF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACe,8BAAP,GAAwC,UAAUb,GAAV,EAAe;EACrD,SAAO,KAAKC,OAAL,CAAa,mCAAb,EAAkDD,GAAlD,EAAuD,MAAvD,EAA+D,KAA/D,EAAsE,KAAtE,EAA6E,sBAA7E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACgB,iCAAP,GAA2C,UAAUd,GAAV,EAAe;EACxD,SAAO,KAAKC,OAAL,CAAa,wCAAb,EAAuDD,GAAvD,EAA4D,MAA5D,EAAoE,KAApE,EAA2E,KAA3E,EAAkF,2BAAlF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACiB,iCAAP,GAA2C,UAAUf,GAAV,EAAe;EACxD,SAAO,KAAKC,OAAL,CAAa,wCAAb,EAAuDD,GAAvD,EAA4D,MAA5D,EAAoE,KAApE,EAA2E,KAA3E,EAAkF,sBAAlF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACkB,iCAAP,GAA2C,UAAUhB,GAAV,EAAe;EACxD,SAAO,KAAKC,OAAL,CAAa,wCAAb,EAAuDD,GAAvD,EAA4D,MAA5D,EAAoE,KAApE,EAA2E,KAA3E,EAAkF,2BAAlF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACmB,iCAAP,GAA2C,UAAUjB,GAAV,EAAe;EACxD,SAAO,KAAKC,OAAL,CAAa,wCAAb,EAAuDD,GAAvD,EAA4D,MAA5D,EAAoE,KAApE,EAA2E,KAA3E,EAAkF,qBAAlF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACoB,kCAAP,GAA4C,YAAY;EACtD,SAAO,KAAKjB,OAAL,CAAa,yCAAb,EAAwD,IAAxD,EAA8D,MAA9D,EAAsE,KAAtE,EAA6E,KAA7E,EAAoF,2BAApF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAH,MAAM,CAACqB,kCAAP,GAA4C,YAAY;EACtD,SAAO,KAAKlB,OAAL,CAAa,yCAAb,EAAwD,IAAxD,EAA8D,MAA9D,EAAsE,KAAtE,EAA6E,KAA7E,EAAoF,qBAApF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAH,MAAM,CAACsB,oCAAP,GAA8C,UAAUpB,GAAV,EAAe;EAC3D,SAAO,KAAKC,OAAL,CAAa,2CAAb,EAA0DD,GAA1D,EAA+D,MAA/D,EAAuE,KAAvE,EAA8E,KAA9E,EAAqF,2BAArF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACuB,oCAAP,GAA8C,UAAUrB,GAAV,EAAe;EAC3D,SAAO,KAAKC,OAAL,CAAa,2CAAb,EAA0DD,GAA1D,EAA+D,MAA/D,EAAuE,KAAvE,EAA8E,KAA9E,EAAqF,sBAArF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACwB,oCAAP,GAA8C,UAAUtB,GAAV,EAAe;EAC3D,SAAO,KAAKC,OAAL,CAAa,2CAAb,EAA0DD,GAA1D,EAA+D,MAA/D,EAAuE,KAAvE,EAA8E,KAA9E,EAAqF,2BAArF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACyB,oCAAP,GAA8C,UAAUvB,GAAV,EAAe;EAC3D,SAAO,KAAKC,OAAL,CAAa,2CAAb,EAA0DD,GAA1D,EAA+D,MAA/D,EAAuE,KAAvE,EAA8E,KAA9E,EAAqF,sBAArF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC0B,iBAAP,GAA2B,YAAY;EACrC,SAAO,KAAKvB,OAAL,CAAa,qBAAb,EAAoC,IAApC,EAA0C,MAA1C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,oBAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAH,MAAM,CAAC2B,kBAAP,GAA4B,UAAUzB,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,sBAAb,EAAqCD,GAArC,EAA0C,MAA1C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,qBAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC4B,kBAAP,GAA4B,UAAU1B,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,sBAAb,EAAqCD,GAArC,EAA0C,MAA1C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,qBAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC6B,2BAAP,GAAqC,YAAY;EAC/C,SAAO,KAAK1B,OAAL,CAAa,iCAAb,EAAgD,IAAhD,EAAsD,MAAtD,EAA8D,KAA9D,EAAqE,KAArE,EAA4E,qBAA5E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAH,MAAM,CAAC8B,eAAP,GAAyB,UAAU5B,GAAV,EAAe;EACtC,SAAO,KAAKC,OAAL,CAAa,mBAAb,EAAkCD,GAAlC,EAAuC,MAAvC,EAA+C,KAA/C,EAAsD,KAAtD,EAA6D,oBAA7D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC+B,kBAAP,GAA4B,UAAU7B,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,uBAAb,EAAsCD,GAAtC,EAA2C,MAA3C,EAAmD,KAAnD,EAA0D,KAA1D,EAAiE,oBAAjE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACgC,gBAAP,GAA0B,YAAY;EACpC,SAAO,KAAK7B,OAAL,CAAa,oBAAb,EAAmC,IAAnC,EAAyC,MAAzC,EAAiD,KAAjD,EAAwD,KAAxD,EAA+D,oBAA/D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAH,MAAM,CAACiC,wBAAP,GAAkC,UAAU/B,GAAV,EAAe;EAC/C,SAAO,KAAKC,OAAL,CAAa,6BAAb,EAA4CD,GAA5C,EAAiD,MAAjD,EAAyD,KAAzD,EAAgE,KAAhE,EAAuE,oBAAvE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACkC,kBAAP,GAA4B,UAAUhC,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,sBAAb,EAAqCD,GAArC,EAA0C,MAA1C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,qBAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACmC,qBAAP,GAA+B,UAAUjC,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,0BAAb,EAAyCD,GAAzC,EAA8C,MAA9C,EAAsD,KAAtD,EAA6D,KAA7D,EAAoE,qBAApE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACoC,gBAAP,GAA0B,UAAUlC,GAAV,EAAe;EACvC,SAAO,KAAKC,OAAL,CAAa,oBAAb,EAAmCD,GAAnC,EAAwC,MAAxC,EAAgD,SAAhD,EAA2D,QAA3D,EAAqE,qBAArE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACqC,WAAP,GAAqB,UAAUnC,GAAV,EAAe;EAClC,SAAO,KAAKC,OAAL,CAAa,eAAb,EAA8BD,GAA9B,EAAmC,MAAnC,EAA2C,KAA3C,EAAkD,KAAlD,EAAyD,qBAAzD,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACsC,SAAP,GAAmB,UAAUpC,GAAV,EAAe;EAChC,SAAO,KAAKC,OAAL,CAAa,YAAb,EAA2BD,GAA3B,EAAgC,MAAhC,EAAwC,KAAxC,EAA+C,KAA/C,EAAsD,qBAAtD,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACuC,gBAAP,GAA0B,UAAUrC,GAAV,EAAe;EACvC,SAAO,KAAKC,OAAL,CAAa,qBAAb,EAAoCD,GAApC,EAAyC,MAAzC,EAAiD,KAAjD,EAAwD,KAAxD,EAA+D,qBAA/D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACwC,cAAP,GAAwB,UAAUtC,GAAV,EAAe;EACrC,SAAO,KAAKC,OAAL,CAAa,kBAAb,EAAiCD,GAAjC,EAAsC,MAAtC,EAA8C,KAA9C,EAAqD,KAArD,EAA4D,qBAA5D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACyC,qBAAP,GAA+B,UAAUvC,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,2BAAb,EAA0CD,GAA1C,EAA+C,MAA/C,EAAuD,KAAvD,EAA8D,KAA9D,EAAqE,qBAArE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC0C,mBAAP,GAA6B,UAAUxC,GAAV,EAAe;EAC1C,SAAO,KAAKC,OAAL,CAAa,wBAAb,EAAuCD,GAAvC,EAA4C,MAA5C,EAAoD,KAApD,EAA2D,KAA3D,EAAkE,qBAAlE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC2C,qBAAP,GAA+B,UAAUzC,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,0BAAb,EAAyCD,GAAzC,EAA8C,MAA9C,EAAsD,KAAtD,EAA6D,KAA7D,EAAoE,qBAApE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC4C,sBAAP,GAAgC,UAAU1C,GAAV,EAAe;EAC7C,SAAO,KAAKC,OAAL,CAAa,2BAAb,EAA0CD,GAA1C,EAA+C,MAA/C,EAAuD,KAAvD,EAA8D,KAA9D,EAAqE,qBAArE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC6C,mBAAP,GAA6B,UAAU3C,GAAV,EAAe;EAC1C,SAAO,KAAKC,OAAL,CAAa,wBAAb,EAAuCD,GAAvC,EAA4C,MAA5C,EAAoD,KAApD,EAA2D,KAA3D,EAAkE,qBAAlE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC8C,iBAAP,GAA2B,UAAU5C,GAAV,EAAe;EACxC,SAAO,KAAKC,OAAL,CAAa,qBAAb,EAAoCD,GAApC,EAAyC,MAAzC,EAAiD,KAAjD,EAAwD,KAAxD,EAA+D,qBAA/D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC+C,sBAAP,GAAgC,UAAU7C,GAAV,EAAe;EAC7C,SAAO,KAAKC,OAAL,CAAa,2BAAb,EAA0CD,GAA1C,EAA+C,MAA/C,EAAuD,KAAvD,EAA8D,KAA9D,EAAqE,qBAArE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACgD,2BAAP,GAAqC,UAAU9C,GAAV,EAAe;EAClD,SAAO,KAAKC,OAAL,CAAa,iCAAb,EAAgDD,GAAhD,EAAqD,MAArD,EAA6D,KAA7D,EAAoE,KAApE,EAA2E,qBAA3E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACiD,aAAP,GAAuB,UAAU/C,GAAV,EAAe;EACpC,SAAO,KAAKC,OAAL,CAAa,iBAAb,EAAgCD,GAAhC,EAAqC,MAArC,EAA6C,KAA7C,EAAoD,KAApD,EAA2D,qBAA3D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACkD,WAAP,GAAqB,UAAUhD,GAAV,EAAe;EAClC,SAAO,KAAKC,OAAL,CAAa,cAAb,EAA6BD,GAA7B,EAAkC,MAAlC,EAA0C,KAA1C,EAAiD,KAAjD,EAAwD,qBAAxD,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACmD,gBAAP,GAA0B,UAAUjD,GAAV,EAAe;EACvC,SAAO,KAAKC,OAAL,CAAa,oBAAb,EAAmCD,GAAnC,EAAwC,MAAxC,EAAgD,KAAhD,EAAuD,KAAvD,EAA8D,qBAA9D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACoD,qBAAP,GAA+B,UAAUlD,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,0BAAb,EAAyCD,GAAzC,EAA8C,MAA9C,EAAsD,KAAtD,EAA6D,KAA7D,EAAoE,qBAApE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACqD,aAAP,GAAuB,UAAUnD,GAAV,EAAe;EACpC,SAAO,KAAKC,OAAL,CAAa,gBAAb,EAA+BD,GAA/B,EAAoC,MAApC,EAA4C,SAA5C,EAAuD,UAAvD,EAAmE,oBAAnE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACsD,gBAAP,GAA0B,UAAUpD,GAAV,EAAe;EACvC,SAAO,KAAKC,OAAL,CAAa,oBAAb,EAAmCD,GAAnC,EAAwC,MAAxC,EAAgD,SAAhD,EAA2D,UAA3D,EAAuE,oBAAvE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACuD,WAAP,GAAqB,UAAUrD,GAAV,EAAe;EAClC,SAAO,KAAKC,OAAL,CAAa,cAAb,EAA6BD,GAA7B,EAAkC,MAAlC,EAA0C,SAA1C,EAAqD,UAArD,EAAiE,oBAAjE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACwD,qBAAP,GAA+B,UAAUtD,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,2BAAb,EAA0CD,GAA1C,EAA+C,MAA/C,EAAuD,KAAvD,EAA8D,KAA9D,EAAqE,oBAArE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACyD,gBAAP,GAA0B,UAAUvD,GAAV,EAAe;EACvC,SAAO,KAAKC,OAAL,CAAa,oBAAb,EAAmCD,GAAnC,EAAwC,MAAxC,EAAgD,KAAhD,EAAuD,KAAvD,EAA8D,qBAA9D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC0D,eAAP,GAAyB,UAAUxD,GAAV,EAAe;EACtC,SAAO,KAAKC,OAAL,CAAa,mBAAb,EAAkCD,GAAlC,EAAuC,MAAvC,EAA+C,SAA/C,EAA0D,UAA1D,EAAsE,oBAAtE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC2D,qBAAP,GAA+B,UAAUzD,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,0BAAb,EAAyCD,GAAzC,EAA8C,MAA9C,EAAsD,KAAtD,EAA6D,KAA7D,EAAoE,oBAApE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC4D,2BAAP,GAAqC,UAAU1D,GAAV,EAAe;EAClD,SAAO,KAAKC,OAAL,CAAa,iCAAb,EAAgDD,GAAhD,EAAqD,MAArD,EAA6D,KAA7D,EAAoE,KAApE,EAA2E,qBAA3E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC6D,iBAAP,GAA2B,UAAU3D,GAAV,EAAe;EACxC,SAAO,KAAKC,OAAL,CAAa,qBAAb,EAAoCD,GAApC,EAAyC,MAAzC,EAAiD,SAAjD,EAA4D,UAA5D,EAAwE,oBAAxE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC8D,mBAAP,GAA6B,UAAU5D,GAAV,EAAe;EAC1C,SAAO,KAAKC,OAAL,CAAa,wBAAb,EAAuCD,GAAvC,EAA4C,WAA5C,EAAyD,SAAzD,EAAoE,UAApE,EAAgF,oBAAhF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC+D,sBAAP,GAAgC,UAAU7D,GAAV,EAAe;EAC7C,SAAO,KAAKC,OAAL,CAAa,2BAAb,EAA0CD,GAA1C,EAA+C,MAA/C,EAAuD,SAAvD,EAAkE,KAAlE,EAAyE,oBAAzE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACgE,eAAP,GAAyB,UAAU9D,GAAV,EAAe;EACtC,SAAO,KAAKC,OAAL,CAAa,mBAAb,EAAkCD,GAAlC,EAAuC,WAAvC,EAAoD,KAApD,EAA2D,KAA3D,EAAkE,qBAAlE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACiE,uBAAP,GAAiC,UAAU/D,GAAV,EAAe;EAC9C,SAAO,KAAKC,OAAL,CAAa,4BAAb,EAA2CD,GAA3C,EAAgD,WAAhD,EAA6D,KAA7D,EAAoE,KAApE,EAA2E,qBAA3E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACkE,8BAAP,GAAwC,UAAUhE,GAAV,EAAe;EACrD,SAAO,KAAKC,OAAL,CAAa,qCAAb,EAAoDD,GAApD,EAAyD,MAAzD,EAAiE,KAAjE,EAAwE,KAAxE,EAA+E,qBAA/E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACmE,uBAAP,GAAiC,UAAUjE,GAAV,EAAe;EAC9C,SAAO,KAAKC,OAAL,CAAa,4BAAb,EAA2CD,GAA3C,EAAgD,QAAhD,EAA0D,QAA1D,EAAoE,KAApE,EAA2E,qBAA3E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACoE,kBAAP,GAA4B,UAAUlE,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,sBAAb,EAAqCD,GAArC,EAA0C,MAA1C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,qBAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACqE,kBAAP,GAA4B,UAAUnE,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,uBAAb,EAAsCD,GAAtC,EAA2C,MAA3C,EAAmD,KAAnD,EAA0D,KAA1D,EAAiE,qBAAjE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACsE,WAAP,GAAqB,UAAUpE,GAAV,EAAe;EAClC,SAAO,KAAKC,OAAL,CAAa,eAAb,EAA8BD,GAA9B,EAAmC,MAAnC,EAA2C,KAA3C,EAAkD,KAAlD,EAAyD,qBAAzD,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACuE,SAAP,GAAmB,UAAUrE,GAAV,EAAe;EAChC,SAAO,KAAKC,OAAL,CAAa,YAAb,EAA2BD,GAA3B,EAAgC,MAAhC,EAAwC,KAAxC,EAA+C,KAA/C,EAAsD,qBAAtD,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACwE,gBAAP,GAA0B,UAAUtE,GAAV,EAAe;EACvC,SAAO,KAAKC,OAAL,CAAa,qBAAb,EAAoCD,GAApC,EAAyC,MAAzC,EAAiD,KAAjD,EAAwD,KAAxD,EAA+D,qBAA/D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACyE,cAAP,GAAwB,UAAUvE,GAAV,EAAe;EACrC,SAAO,KAAKC,OAAL,CAAa,kBAAb,EAAiCD,GAAjC,EAAsC,MAAtC,EAA8C,KAA9C,EAAqD,KAArD,EAA4D,qBAA5D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC0E,qBAAP,GAA+B,UAAUxE,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,2BAAb,EAA0CD,GAA1C,EAA+C,MAA/C,EAAuD,KAAvD,EAA8D,KAA9D,EAAqE,qBAArE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC2E,mBAAP,GAA6B,UAAUzE,GAAV,EAAe;EAC1C,SAAO,KAAKC,OAAL,CAAa,wBAAb,EAAuCD,GAAvC,EAA4C,MAA5C,EAAoD,KAApD,EAA2D,KAA3D,EAAkE,qBAAlE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC4E,gBAAP,GAA0B,UAAU1E,GAAV,EAAe;EACvC,SAAO,KAAKC,OAAL,CAAa,oBAAb,EAAmCD,GAAnC,EAAwC,MAAxC,EAAgD,KAAhD,EAAuD,QAAvD,EAAiE,qBAAjE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC6E,gBAAP,GAA0B,UAAU3E,GAAV,EAAe;EACvC,SAAO,KAAKC,OAAL,CAAa,oBAAb,EAAmCD,GAAnC,EAAwC,MAAxC,EAAgD,KAAhD,EAAuD,QAAvD,EAAiE,qBAAjE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC8E,sBAAP,GAAgC,UAAU5E,GAAV,EAAe;EAC7C,SAAO,KAAKC,OAAL,CAAa,0BAAb,EAAyCD,GAAzC,EAA8C,MAA9C,EAAsD,KAAtD,EAA6D,KAA7D,EAAoE,wBAApE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC+E,kBAAP,GAA4B,UAAU7E,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,sBAAb,EAAqCD,GAArC,EAA0C,MAA1C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,sBAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACgF,wBAAP,GAAkC,UAAU9E,GAAV,EAAe;EAC/C,SAAO,KAAKC,OAAL,CAAa,4BAAb,EAA2CD,GAA3C,EAAgD,MAAhD,EAAwD,KAAxD,EAA+D,KAA/D,EAAsE,sBAAtE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACiF,qBAAP,GAA+B,UAAU/E,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,yBAAb,EAAwCD,GAAxC,EAA6C,MAA7C,EAAqD,KAArD,EAA4D,KAA5D,EAAmE,sBAAnE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACkF,0BAAP,GAAoC,UAAUhF,GAAV,EAAe;EACjD,SAAO,KAAKC,OAAL,CAAa,+BAAb,EAA8CD,GAA9C,EAAmD,MAAnD,EAA2D,KAA3D,EAAkE,KAAlE,EAAyE,qBAAzE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACmF,2BAAP,GAAqC,YAAY;EAC/C,SAAO,KAAKhF,OAAL,CAAa,gCAAb,EAA+C,IAA/C,EAAqD,MAArD,EAA6D,KAA7D,EAAoE,KAApE,EAA2E,qBAA3E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAH,MAAM,CAACoF,qBAAP,GAA+B,UAAUlF,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,yBAAb,EAAwCD,GAAxC,EAA6C,MAA7C,EAAqD,KAArD,EAA4D,KAA5D,EAAmE,sBAAnE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACqF,YAAP,GAAsB,UAAUnF,GAAV,EAAe;EACnC,SAAO,KAAKC,OAAL,CAAa,eAAb,EAA8BD,GAA9B,EAAmC,MAAnC,EAA2C,KAA3C,EAAkD,KAAlD,EAAyD,qBAAzD,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACsF,YAAP,GAAsB,UAAUpF,GAAV,EAAe;EACnC,SAAO,KAAKC,OAAL,CAAa,gBAAb,EAA+BD,GAA/B,EAAoC,MAApC,EAA4C,KAA5C,EAAmD,KAAnD,EAA0D,qBAA1D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACuF,0BAAP,GAAoC,UAAUrF,GAAV,EAAe;EACjD,SAAO,KAAKC,OAAL,CAAa,iCAAb,EAAgDD,GAAhD,EAAqD,MAArD,EAA6D,KAA7D,EAAoE,KAApE,EAA2E,qBAA3E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACwF,WAAP,GAAqB,UAAUtF,GAAV,EAAe;EAClC,SAAO,KAAKC,OAAL,CAAa,cAAb,EAA6BD,GAA7B,EAAkC,MAAlC,EAA0C,KAA1C,EAAiD,KAAjD,EAAwD,qBAAxD,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACyF,aAAP,GAAuB,UAAUvF,GAAV,EAAe;EACpC,SAAO,KAAKC,OAAL,CAAa,iBAAb,EAAgCD,GAAhC,EAAqC,MAArC,EAA6C,KAA7C,EAAoD,KAApD,EAA2D,qBAA3D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC0F,qBAAP,GAA+B,UAAUxF,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,0BAAb,EAAyCD,GAAzC,EAA8C,MAA9C,EAAsD,KAAtD,EAA6D,KAA7D,EAAoE,qBAApE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC2F,YAAP,GAAsB,UAAUzF,GAAV,EAAe;EACnC,SAAO,KAAKC,OAAL,CAAa,gBAAb,EAA+BD,GAA/B,EAAoC,MAApC,EAA4C,KAA5C,EAAmD,KAAnD,EAA0D,sBAA1D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC4F,YAAP,GAAsB,UAAU1F,GAAV,EAAe;EACnC,SAAO,KAAKC,OAAL,CAAa,gBAAb,EAA+BD,GAA/B,EAAoC,MAApC,EAA4C,KAA5C,EAAmD,KAAnD,EAA0D,qBAA1D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC6F,eAAP,GAAyB,UAAU3F,GAAV,EAAe;EACtC,SAAO,KAAKC,OAAL,CAAa,mBAAb,EAAkCD,GAAlC,EAAuC,MAAvC,EAA+C,KAA/C,EAAsD,KAAtD,EAA6D,sBAA7D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC8F,oBAAP,GAA8B,UAAU5F,GAAV,EAAe;EAC3C,SAAO,KAAKC,OAAL,CAAa,yBAAb,EAAwCD,GAAxC,EAA6C,MAA7C,EAAqD,KAArD,EAA4D,KAA5D,EAAmE,qBAAnE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC+F,WAAP,GAAqB,UAAU7F,GAAV,EAAe;EAClC,SAAO,KAAKC,OAAL,CAAa,cAAb,EAA6BD,GAA7B,EAAkC,MAAlC,EAA0C,SAA1C,EAAqD,QAArD,EAA+D,qBAA/D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACgG,0BAAP,GAAoC,UAAU9F,GAAV,EAAe;EACjD,SAAO,KAAKC,OAAL,CAAa,gCAAb,EAA+CD,GAA/C,EAAoD,MAApD,EAA4D,SAA5D,EAAuE,QAAvE,EAAiF,qBAAjF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACiG,wBAAP,GAAkC,UAAU/F,GAAV,EAAe;EAC/C,SAAO,KAAKC,OAAL,CAAa,6BAAb,EAA4CD,GAA5C,EAAiD,MAAjD,EAAyD,SAAzD,EAAoE,QAApE,EAA8E,qBAA9E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACkG,wBAAP,GAAkC,UAAUhG,GAAV,EAAe;EAC/C,SAAO,KAAKC,OAAL,CAAa,6BAAb,EAA4CD,GAA5C,EAAiD,MAAjD,EAAyD,SAAzD,EAAoE,QAApE,EAA8E,qBAA9E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACmG,6BAAP,GAAuC,UAAUjG,GAAV,EAAe;EACpD,SAAO,KAAKC,OAAL,CAAa,mCAAb,EAAkDD,GAAlD,EAAuD,MAAvD,EAA+D,KAA/D,EAAsE,KAAtE,EAA6E,qBAA7E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACoG,+BAAP,GAAyC,UAAUlG,GAAV,EAAe;EACtD,SAAO,KAAKC,OAAL,CAAa,sCAAb,EAAqDD,GAArD,EAA0D,MAA1D,EAAkE,KAAlE,EAAyE,KAAzE,EAAgF,qBAAhF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACqG,kCAAP,GAA4C,UAAUnG,GAAV,EAAe;EACzD,SAAO,KAAKC,OAAL,CAAa,yCAAb,EAAwDD,GAAxD,EAA6D,MAA7D,EAAqE,KAArE,EAA4E,KAA5E,EAAmF,qBAAnF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACsG,uBAAP,GAAiC,UAAUpG,GAAV,EAAe;EAC9C,SAAO,KAAKC,OAAL,CAAa,4BAAb,EAA2CD,GAA3C,EAAgD,MAAhD,EAAwD,SAAxD,EAAmE,QAAnE,EAA6E,qBAA7E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACuG,4BAAP,GAAsC,UAAUrG,GAAV,EAAe;EACnD,SAAO,KAAKC,OAAL,CAAa,kCAAb,EAAiDD,GAAjD,EAAsD,MAAtD,EAA8D,KAA9D,EAAqE,KAArE,EAA4E,qBAA5E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACwG,cAAP,GAAwB,UAAUtG,GAAV,EAAe;EACrC,SAAO,KAAKC,OAAL,CAAa,iBAAb,EAAgCD,GAAhC,EAAqC,MAArC,EAA6C,KAA7C,EAAoD,KAApD,EAA2D,QAA3D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACyG,gBAAP,GAA0B,UAAUvG,GAAV,EAAe;EACvC,SAAO,KAAKC,OAAL,CAAa,oBAAb,EAAmCD,GAAnC,EAAwC,MAAxC,EAAgD,KAAhD,EAAuD,KAAvD,EAA8D,qBAA9D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC0G,eAAP,GAAyB,UAAUxG,GAAV,EAAe;EACtC,SAAO,KAAKC,OAAL,CAAa,mBAAb,EAAkCD,GAAlC,EAAuC,MAAvC,EAA+C,KAA/C,EAAsD,QAAtD,EAAgE,qBAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC2G,iBAAP,GAA2B,UAAUzG,GAAV,EAAe;EACxC,SAAO,KAAKC,OAAL,CAAa,qBAAb,EAAoCD,GAApC,EAAyC,MAAzC,EAAiD,KAAjD,EAAwD,UAAxD,EAAoE,oBAApE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC4G,wBAAP,GAAkC,UAAU1G,GAAV,EAAe;EAC/C,SAAO,KAAKC,OAAL,CAAa,8BAAb,EAA6CD,GAA7C,EAAkD,MAAlD,EAA0D,KAA1D,EAAiE,KAAjE,EAAwE,cAAxE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC6G,gCAAP,GAA0C,UAAU3G,GAAV,EAAe;EACvD,SAAO,KAAKC,OAAL,CAAa,uCAAb,EAAsDD,GAAtD,EAA2D,MAA3D,EAAmE,KAAnE,EAA0E,KAA1E,EAAiF,cAAjF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC8G,sBAAP,GAAgC,UAAU5G,GAAV,EAAe;EAC7C,SAAO,KAAKC,OAAL,CAAa,4BAAb,EAA2CD,GAA3C,EAAgD,MAAhD,EAAwD,KAAxD,EAA+D,KAA/D,EAAsE,cAAtE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC+G,aAAP,GAAuB,UAAU7G,GAAV,EAAe;EACpC,SAAO,KAAKC,OAAL,CAAa,iBAAb,EAAgCD,GAAhC,EAAqC,MAArC,EAA6C,KAA7C,EAAoD,KAApD,EAA2D,qBAA3D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACgH,qBAAP,GAA+B,UAAU9G,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,0BAAb,EAAyCD,GAAzC,EAA8C,MAA9C,EAAsD,KAAtD,EAA6D,KAA7D,EAAoE,qBAApE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACiH,0BAAP,GAAoC,UAAU/G,GAAV,EAAe;EACjD,SAAO,KAAKC,OAAL,CAAa,+BAAb,EAA8CD,GAA9C,EAAmD,MAAnD,EAA2D,KAA3D,EAAkE,KAAlE,EAAyE,wBAAzE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACkH,yBAAP,GAAmC,UAAUhH,GAAV,EAAe;EAChD,SAAO,KAAKC,OAAL,CAAa,+BAAb,EAA8CD,GAA9C,EAAmD,MAAnD,EAA2D,KAA3D,EAAkE,KAAlE,EAAyE,cAAzE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACmH,yBAAP,GAAmC,UAAUjH,GAAV,EAAe;EAChD,SAAO,KAAKC,OAAL,CAAa,+BAAb,EAA8CD,GAA9C,EAAmD,MAAnD,EAA2D,KAA3D,EAAkE,KAAlE,EAAyE,eAAzE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACoH,eAAP,GAAyB,UAAUlH,GAAV,EAAe;EACtC,SAAO,KAAKC,OAAL,CAAa,mBAAb,EAAkCD,GAAlC,EAAuC,MAAvC,EAA+C,KAA/C,EAAsD,QAAtD,EAAgE,qBAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACqH,iBAAP,GAA2B,UAAUnH,GAAV,EAAe;EACxC,SAAO,KAAKC,OAAL,CAAa,sBAAb,EAAqCD,GAArC,EAA0C,MAA1C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,eAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACsH,kBAAP,GAA4B,UAAUpH,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,uBAAb,EAAsCD,GAAtC,EAA2C,MAA3C,EAAmD,KAAnD,EAA0D,KAA1D,EAAiE,cAAjE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACuH,0BAAP,GAAoC,UAAUrH,GAAV,EAAe;EACjD,SAAO,KAAKC,OAAL,CAAa,gCAAb,EAA+CD,GAA/C,EAAoD,MAApD,EAA4D,KAA5D,EAAmE,KAAnE,EAA0E,cAA1E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACwH,oBAAP,GAA8B,UAAUtH,GAAV,EAAe;EAC3C,SAAO,KAAKC,OAAL,CAAa,yBAAb,EAAwCD,GAAxC,EAA6C,MAA7C,EAAqD,KAArD,EAA4D,KAA5D,EAAmE,eAAnE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACyH,kBAAP,GAA4B,UAAUvH,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,sBAAb,EAAqCD,GAArC,EAA0C,MAA1C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,qBAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC0H,oBAAP,GAA8B,UAAUxH,GAAV,EAAe;EAC3C,SAAO,KAAKC,OAAL,CAAa,yBAAb,EAAwCD,GAAxC,EAA6C,MAA7C,EAAqD,KAArD,EAA4D,KAA5D,EAAmE,eAAnE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC2H,sBAAP,GAAgC,UAAUzH,GAAV,EAAe;EAC7C,SAAO,KAAKC,OAAL,CAAa,2BAAb,EAA0CD,GAA1C,EAA+C,MAA/C,EAAuD,KAAvD,EAA8D,KAA9D,EAAqE,eAArE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC4H,qBAAP,GAA+B,UAAU1H,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,0BAAb,EAAyCD,GAAzC,EAA8C,MAA9C,EAAsD,KAAtD,EAA6D,KAA7D,EAAoE,eAApE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC6H,iCAAP,GAA2C,UAAU3H,GAAV,EAAe;EACxD,SAAO,KAAKC,OAAL,CAAa,wCAAb,EAAuDD,GAAvD,EAA4D,MAA5D,EAAoE,KAApE,EAA2E,KAA3E,EAAkF,eAAlF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC8H,0BAAP,GAAoC,UAAU5H,GAAV,EAAe;EACjD,SAAO,KAAKC,OAAL,CAAa,gCAAb,EAA+CD,GAA/C,EAAoD,MAApD,EAA4D,KAA5D,EAAmE,KAAnE,EAA0E,eAA1E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC+H,uBAAP,GAAiC,UAAU7H,GAAV,EAAe;EAC9C,SAAO,KAAKC,OAAL,CAAa,4BAAb,EAA2CD,GAA3C,EAAgD,MAAhD,EAAwD,KAAxD,EAA+D,KAA/D,EAAsE,eAAtE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACgI,mCAAP,GAA6C,UAAU9H,GAAV,EAAe;EAC1D,SAAO,KAAKC,OAAL,CAAa,0CAAb,EAAyDD,GAAzD,EAA8D,MAA9D,EAAsE,KAAtE,EAA6E,KAA7E,EAAoF,eAApF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACiI,sBAAP,GAAgC,UAAU/H,GAAV,EAAe;EAC7C,SAAO,KAAKC,OAAL,CAAa,2BAAb,EAA0CD,GAA1C,EAA+C,MAA/C,EAAuD,KAAvD,EAA8D,KAA9D,EAAqE,cAArE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACkI,2BAAP,GAAqC,UAAUhI,GAAV,EAAe;EAClD,SAAO,KAAKC,OAAL,CAAa,iCAAb,EAAgDD,GAAhD,EAAqD,MAArD,EAA6D,KAA7D,EAAoE,KAApE,EAA2E,cAA3E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACmI,wBAAP,GAAkC,UAAUjI,GAAV,EAAe;EAC/C,SAAO,KAAKC,OAAL,CAAa,6BAAb,EAA4CD,GAA5C,EAAiD,MAAjD,EAAyD,KAAzD,EAAgE,KAAhE,EAAuE,cAAvE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACoI,wBAAP,GAAkC,UAAUlI,GAAV,EAAe;EAC/C,SAAO,KAAKC,OAAL,CAAa,8BAAb,EAA6CD,GAA7C,EAAkD,MAAlD,EAA0D,SAA1D,EAAqE,UAArE,EAAiF,cAAjF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACqI,4BAAP,GAAsC,UAAUnI,GAAV,EAAe;EACnD,SAAO,KAAKC,OAAL,CAAa,kCAAb,EAAiDD,GAAjD,EAAsD,WAAtD,EAAmE,KAAnE,EAA0E,KAA1E,EAAiF,cAAjF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACsI,qBAAP,GAA+B,UAAUpI,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,0BAAb,EAAyCD,GAAzC,EAA8C,MAA9C,EAAsD,KAAtD,EAA6D,KAA7D,EAAoE,cAApE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACuI,sBAAP,GAAgC,UAAUrI,GAAV,EAAe;EAC7C,SAAO,KAAKC,OAAL,CAAa,2BAAb,EAA0CD,GAA1C,EAA+C,MAA/C,EAAuD,KAAvD,EAA8D,KAA9D,EAAqE,cAArE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACwI,2BAAP,GAAqC,UAAUtI,GAAV,EAAe;EAClD,SAAO,KAAKC,OAAL,CAAa,iCAAb,EAAgDD,GAAhD,EAAqD,MAArD,EAA6D,KAA7D,EAAoE,KAApE,EAA2E,cAA3E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACyI,8BAAP,GAAwC,UAAUvI,GAAV,EAAe;EACrD,SAAO,KAAKC,OAAL,CAAa,oCAAb,EAAmDD,GAAnD,EAAwD,MAAxD,EAAgE,KAAhE,EAAuE,KAAvE,EAA8E,cAA9E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC0I,wBAAP,GAAkC,UAAUxI,GAAV,EAAe;EAC/C,SAAO,KAAKC,OAAL,CAAa,6BAAb,EAA4CD,GAA5C,EAAiD,MAAjD,EAAyD,KAAzD,EAAgE,KAAhE,EAAuE,cAAvE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC2I,gCAAP,GAA0C,UAAUzI,GAAV,EAAe;EACvD,SAAO,KAAKC,OAAL,CAAa,sCAAb,EAAqDD,GAArD,EAA0D,MAA1D,EAAkE,KAAlE,EAAyE,KAAzE,EAAgF,cAAhF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC4I,kBAAP,GAA4B,UAAU1I,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,sBAAb,EAAqCD,GAArC,EAA0C,MAA1C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,cAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC6I,0BAAP,GAAoC,UAAU3I,GAAV,EAAe;EACjD,SAAO,KAAKC,OAAL,CAAa,+BAAb,EAA8CD,GAA9C,EAAmD,MAAnD,EAA2D,KAA3D,EAAkE,KAAlE,EAAyE,cAAzE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC8I,2BAAP,GAAqC,UAAU5I,GAAV,EAAe;EAClD,SAAO,KAAKC,OAAL,CAAa,gCAAb,EAA+CD,GAA/C,EAAoD,MAApD,EAA4D,KAA5D,EAAmE,KAAnE,EAA0E,cAA1E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC+I,mCAAP,GAA6C,UAAU7I,GAAV,EAAe;EAC1D,SAAO,KAAKC,OAAL,CAAa,yCAAb,EAAwDD,GAAxD,EAA6D,MAA7D,EAAqE,KAArE,EAA4E,KAA5E,EAAmF,cAAnF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACgJ,wBAAP,GAAkC,UAAU9I,GAAV,EAAe;EAC/C,SAAO,KAAKC,OAAL,CAAa,6BAAb,EAA4CD,GAA5C,EAAiD,MAAjD,EAAyD,KAAzD,EAAgE,KAAhE,EAAuE,cAAvE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACiJ,gCAAP,GAA0C,UAAU/I,GAAV,EAAe;EACvD,SAAO,KAAKC,OAAL,CAAa,sCAAb,EAAqDD,GAArD,EAA0D,MAA1D,EAAkE,KAAlE,EAAyE,KAAzE,EAAgF,cAAhF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACkJ,sBAAP,GAAgC,UAAUhJ,GAAV,EAAe;EAC7C,SAAO,KAAKC,OAAL,CAAa,2BAAb,EAA0CD,GAA1C,EAA+C,MAA/C,EAAuD,KAAvD,EAA8D,KAA9D,EAAqE,cAArE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACmJ,+BAAP,GAAyC,UAAUjJ,GAAV,EAAe;EACtD,SAAO,KAAKC,OAAL,CAAa,qCAAb,EAAoDD,GAApD,EAAyD,MAAzD,EAAiE,KAAjE,EAAwE,KAAxE,EAA+E,eAA/E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACoJ,kBAAP,GAA4B,UAAUlJ,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,sBAAb,EAAqCD,GAArC,EAA0C,MAA1C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,eAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACqJ,+BAAP,GAAyC,UAAUnJ,GAAV,EAAe;EACtD,SAAO,KAAKC,OAAL,CAAa,oCAAb,EAAmDD,GAAnD,EAAwD,MAAxD,EAAgE,KAAhE,EAAuE,KAAvE,EAA8E,eAA9E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACsJ,iCAAP,GAA2C,UAAUpJ,GAAV,EAAe;EACxD,SAAO,KAAKC,OAAL,CAAa,sCAAb,EAAqDD,GAArD,EAA0D,MAA1D,EAAkE,KAAlE,EAAyE,KAAzE,EAAgF,eAAhF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACuJ,uBAAP,GAAiC,UAAUrJ,GAAV,EAAe;EAC9C,SAAO,KAAKC,OAAL,CAAa,4BAAb,EAA2CD,GAA3C,EAAgD,MAAhD,EAAwD,KAAxD,EAA+D,KAA/D,EAAsE,eAAtE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACwJ,wBAAP,GAAkC,UAAUtJ,GAAV,EAAe;EAC/C,SAAO,KAAKC,OAAL,CAAa,8BAAb,EAA6CD,GAA7C,EAAkD,MAAlD,EAA0D,KAA1D,EAAiE,KAAjE,EAAwE,eAAxE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACyJ,yBAAP,GAAmC,UAAUvJ,GAAV,EAAe;EAChD,SAAO,KAAKC,OAAL,CAAa,8BAAb,EAA6CD,GAA7C,EAAkD,MAAlD,EAA0D,KAA1D,EAAiE,KAAjE,EAAwE,eAAxE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC0J,uBAAP,GAAiC,UAAUxJ,GAAV,EAAe;EAC9C,SAAO,KAAKC,OAAL,CAAa,4BAAb,EAA2CD,GAA3C,EAAgD,MAAhD,EAAwD,KAAxD,EAA+D,KAA/D,EAAsE,eAAtE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC2J,2BAAP,GAAqC,UAAUzJ,GAAV,EAAe;EAClD,SAAO,KAAKC,OAAL,CAAa,gCAAb,EAA+CD,GAA/C,EAAoD,MAApD,EAA4D,KAA5D,EAAmE,KAAnE,EAA0E,eAA1E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC4J,kBAAP,GAA4B,UAAU1J,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,sBAAb,EAAqCD,GAArC,EAA0C,MAA1C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,eAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC6J,qBAAP,GAA+B,UAAU3J,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,yBAAb,EAAwCD,GAAxC,EAA6C,MAA7C,EAAqD,KAArD,EAA4D,KAA5D,EAAmE,eAAnE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC8J,oBAAP,GAA8B,UAAU5J,GAAV,EAAe;EAC3C,SAAO,KAAKC,OAAL,CAAa,wBAAb,EAAuCD,GAAvC,EAA4C,MAA5C,EAAoD,KAApD,EAA2D,KAA3D,EAAkE,eAAlE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC+J,kBAAP,GAA4B,UAAU7J,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,sBAAb,EAAqCD,GAArC,EAA0C,MAA1C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,eAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACgK,oBAAP,GAA8B,UAAU9J,GAAV,EAAe;EAC3C,SAAO,KAAKC,OAAL,CAAa,wBAAb,EAAuCD,GAAvC,EAA4C,MAA5C,EAAoD,KAApD,EAA2D,KAA3D,EAAkE,eAAlE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACiK,uBAAP,GAAiC,UAAU/J,GAAV,EAAe;EAC9C,SAAO,KAAKC,OAAL,CAAa,4BAAb,EAA2CD,GAA3C,EAAgD,MAAhD,EAAwD,KAAxD,EAA+D,KAA/D,EAAsE,eAAtE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACkK,yBAAP,GAAmC,UAAUhK,GAAV,EAAe;EAChD,SAAO,KAAKC,OAAL,CAAa,8BAAb,EAA6CD,GAA7C,EAAkD,MAAlD,EAA0D,KAA1D,EAAiE,KAAjE,EAAwE,eAAxE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACmK,yBAAP,GAAmC,UAAUjK,GAAV,EAAe;EAChD,SAAO,KAAKC,OAAL,CAAa,8BAAb,EAA6CD,GAA7C,EAAkD,MAAlD,EAA0D,KAA1D,EAAiE,KAAjE,EAAwE,eAAxE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACoK,4BAAP,GAAsC,UAAUlK,GAAV,EAAe;EACnD,SAAO,KAAKC,OAAL,CAAa,kCAAb,EAAiDD,GAAjD,EAAsD,MAAtD,EAA8D,KAA9D,EAAqE,KAArE,EAA4E,eAA5E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACqK,6BAAP,GAAuC,UAAUnK,GAAV,EAAe;EACpD,SAAO,KAAKC,OAAL,CAAa,mCAAb,EAAkDD,GAAlD,EAAuD,MAAvD,EAA+D,KAA/D,EAAsE,KAAtE,EAA6E,eAA7E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACsK,0BAAP,GAAoC,UAAUpK,GAAV,EAAe;EACjD,SAAO,KAAKC,OAAL,CAAa,gCAAb,EAA+CD,GAA/C,EAAoD,MAApD,EAA4D,KAA5D,EAAmE,KAAnE,EAA0E,eAA1E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACuK,8BAAP,GAAwC,UAAUrK,GAAV,EAAe;EACrD,SAAO,KAAKC,OAAL,CAAa,oCAAb,EAAmDD,GAAnD,EAAwD,MAAxD,EAAgE,KAAhE,EAAuE,KAAvE,EAA8E,iBAA9E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACwK,mCAAP,GAA6C,UAAUtK,GAAV,EAAe;EAC1D,SAAO,KAAKC,OAAL,CAAa,0CAAb,EAAyDD,GAAzD,EAA8D,MAA9D,EAAsE,KAAtE,EAA6E,KAA7E,EAAoF,iBAApF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACyK,qBAAP,GAA+B,UAAUvK,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,0BAAb,EAAyCD,GAAzC,EAA8C,MAA9C,EAAsD,KAAtD,EAA6D,KAA7D,EAAoE,gBAApE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC0K,WAAP,GAAqB,YAAY;EAC/B,SAAO,KAAKvK,OAAL,CAAa,eAAb,EAA8B,IAA9B,EAAoC,MAApC,EAA4C,KAA5C,EAAmD,KAAnD,EAA0D,gBAA1D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAH,MAAM,CAAC2K,gBAAP,GAA0B,UAAUzK,GAAV,EAAe;EACvC,SAAO,KAAKC,OAAL,CAAa,oBAAb,EAAmCD,GAAnC,EAAwC,MAAxC,EAAgD,KAAhD,EAAuD,KAAvD,EAA8D,cAA9D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC4K,gBAAP,GAA0B,UAAU1K,GAAV,EAAe;EACvC,SAAO,KAAKC,OAAL,CAAa,oBAAb,EAAmCD,GAAnC,EAAwC,MAAxC,EAAgD,KAAhD,EAAuD,KAAvD,EAA8D,cAA9D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC6K,iBAAP,GAA2B,UAAU3K,GAAV,EAAe;EACxC,SAAO,KAAKC,OAAL,CAAa,sBAAb,EAAqCD,GAArC,EAA0C,MAA1C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,aAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC8K,sBAAP,GAAgC,UAAU5K,GAAV,EAAe;EAC7C,SAAO,KAAKC,OAAL,CAAa,4BAAb,EAA2CD,GAA3C,EAAgD,MAAhD,EAAwD,KAAxD,EAA+D,KAA/D,EAAsE,cAAtE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC+K,cAAP,GAAwB,UAAU7K,GAAV,EAAe;EACrC,SAAO,KAAKC,OAAL,CAAa,kBAAb,EAAiCD,GAAjC,EAAsC,MAAtC,EAA8C,KAA9C,EAAqD,KAArD,EAA4D,aAA5D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACgL,sBAAP,GAAgC,UAAU9K,GAAV,EAAe;EAC7C,SAAO,KAAKC,OAAL,CAAa,2BAAb,EAA0CD,GAA1C,EAA+C,MAA/C,EAAuD,KAAvD,EAA8D,KAA9D,EAAqE,aAArE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACiL,oBAAP,GAA8B,UAAU/K,GAAV,EAAe;EAC3C,SAAO,KAAKC,OAAL,CAAa,yBAAb,EAAwCD,GAAxC,EAA6C,MAA7C,EAAqD,KAArD,EAA4D,KAA5D,EAAmE,cAAnE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACkL,qBAAP,GAA+B,UAAUhL,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,0BAAb,EAAyCD,GAAzC,EAA8C,MAA9C,EAAsD,KAAtD,EAA6D,KAA7D,EAAoE,aAApE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACmL,6BAAP,GAAuC,UAAUjL,GAAV,EAAe;EACpD,SAAO,KAAKC,OAAL,CAAa,mCAAb,EAAkDD,GAAlD,EAAuD,MAAvD,EAA+D,KAA/D,EAAsE,KAAtE,EAA6E,aAA7E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACoL,uBAAP,GAAiC,UAAUlL,GAAV,EAAe;EAC9C,SAAO,KAAKC,OAAL,CAAa,4BAAb,EAA2CD,GAA3C,EAAgD,MAAhD,EAAwD,KAAxD,EAA+D,KAA/D,EAAsE,cAAtE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACqL,8BAAP,GAAwC,UAAUnL,GAAV,EAAe;EACrD,SAAO,KAAKC,OAAL,CAAa,qCAAb,EAAoDD,GAApD,EAAyD,MAAzD,EAAiE,KAAjE,EAAwE,KAAxE,EAA+E,cAA/E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACsL,gBAAP,GAA0B,UAAUpL,GAAV,EAAe;EACvC,SAAO,KAAKC,OAAL,CAAa,oBAAb,EAAmCD,GAAnC,EAAwC,MAAxC,EAAgD,KAAhD,EAAuD,KAAvD,EAA8D,cAA9D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACuL,0BAAP,GAAoC,UAAUrL,GAAV,EAAe;EACjD,SAAO,KAAKC,OAAL,CAAa,gCAAb,EAA+CD,GAA/C,EAAoD,MAApD,EAA4D,KAA5D,EAAmE,KAAnE,EAA0E,4BAA1E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACwL,uBAAP,GAAiC,UAAUtL,GAAV,EAAe;EAC9C,SAAO,KAAKC,OAAL,CAAa,6BAAb,EAA4CD,GAA5C,EAAiD,MAAjD,EAAyD,KAAzD,EAAgE,KAAhE,EAAuE,4BAAvE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACyL,+BAAP,GAAyC,UAAUvL,GAAV,EAAe;EACtD,SAAO,KAAKC,OAAL,CAAa,sCAAb,EAAqDD,GAArD,EAA0D,MAA1D,EAAkE,KAAlE,EAAyE,KAAzE,EAAgF,4BAAhF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC0L,uCAAP,GAAiD,UAAUxL,GAAV,EAAe;EAC9D,SAAO,KAAKC,OAAL,CAAa,+CAAb,EAA8DD,GAA9D,EAAmE,MAAnE,EAA2E,KAA3E,EAAkF,KAAlF,EAAyF,4BAAzF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC2L,0BAAP,GAAoC,UAAUzL,GAAV,EAAe;EACjD,SAAO,KAAKC,OAAL,CAAa,gCAAb,EAA+CD,GAA/C,EAAoD,MAApD,EAA4D,KAA5D,EAAmE,KAAnE,EAA0E,4BAA1E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC4L,2BAAP,GAAqC,UAAU1L,GAAV,EAAe;EAClD,SAAO,KAAKC,OAAL,CAAa,iCAAb,EAAgDD,GAAhD,EAAqD,MAArD,EAA6D,KAA7D,EAAoE,KAApE,EAA2E,4BAA3E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC6L,0BAAP,GAAoC,UAAU3L,GAAV,EAAe;EACjD,SAAO,KAAKC,OAAL,CAAa,gCAAb,EAA+CD,GAA/C,EAAoD,MAApD,EAA4D,KAA5D,EAAmE,KAAnE,EAA0E,4BAA1E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC8L,kCAAP,GAA4C,UAAU5L,GAAV,EAAe;EACzD,SAAO,KAAKC,OAAL,CAAa,0CAAb,EAAyDD,GAAzD,EAA8D,MAA9D,EAAsE,KAAtE,EAA6E,KAA7E,EAAoF,eAApF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC+L,mCAAP,GAA6C,UAAU7L,GAAV,EAAe;EAC1D,SAAO,KAAKC,OAAL,CAAa,2CAAb,EAA0DD,GAA1D,EAA+D,MAA/D,EAAuE,KAAvE,EAA8E,KAA9E,EAAqF,eAArF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACgM,gCAAP,GAA0C,UAAU9L,GAAV,EAAe;EACvD,SAAO,KAAKC,OAAL,CAAa,wCAAb,EAAuDD,GAAvD,EAA4D,MAA5D,EAAoE,KAApE,EAA2E,KAA3E,EAAkF,eAAlF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACiM,6BAAP,GAAuC,UAAU/L,GAAV,EAAe;EACpD,SAAO,KAAKC,OAAL,CAAa,oCAAb,EAAmDD,GAAnD,EAAwD,MAAxD,EAAgE,KAAhE,EAAuE,KAAvE,EAA8E,iBAA9E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACkM,kCAAP,GAA4C,UAAUhM,GAAV,EAAe;EACzD,SAAO,KAAKC,OAAL,CAAa,0CAAb,EAAyDD,GAAzD,EAA8D,MAA9D,EAAsE,KAAtE,EAA6E,KAA7E,EAAoF,iBAApF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACmM,qCAAP,GAA+C,UAAUjM,GAAV,EAAe;EAC5D,SAAO,KAAKC,OAAL,CAAa,6CAAb,EAA4DD,GAA5D,EAAiE,MAAjE,EAAyE,KAAzE,EAAgF,KAAhF,EAAuF,eAAvF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACoM,sCAAP,GAAgD,UAAUlM,GAAV,EAAe;EAC7D,SAAO,KAAKC,OAAL,CAAa,8CAAb,EAA6DD,GAA7D,EAAkE,MAAlE,EAA0E,KAA1E,EAAiF,KAAjF,EAAwF,cAAxF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACqM,8CAAP,GAAwD,UAAUnM,GAAV,EAAe;EACrE,SAAO,KAAKC,OAAL,CAAa,uDAAb,EAAsED,GAAtE,EAA2E,MAA3E,EAAmF,KAAnF,EAA0F,KAA1F,EAAiG,cAAjG,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACsM,wCAAP,GAAkD,UAAUpM,GAAV,EAAe;EAC/D,SAAO,KAAKC,OAAL,CAAa,gDAAb,EAA+DD,GAA/D,EAAoE,MAApE,EAA4E,KAA5E,EAAmF,KAAnF,EAA0F,eAA1F,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACuM,mCAAP,GAA6C,UAAUrM,GAAV,EAAe;EAC1D,SAAO,KAAKC,OAAL,CAAa,2CAAb,EAA0DD,GAA1D,EAA+D,MAA/D,EAAuE,KAAvE,EAA8E,KAA9E,EAAqF,cAArF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACwM,sCAAP,GAAgD,UAAUtM,GAAV,EAAe;EAC7D,SAAO,KAAKC,OAAL,CAAa,8CAAb,EAA6DD,GAA7D,EAAkE,MAAlE,EAA0E,KAA1E,EAAiF,KAAjF,EAAwF,eAAxF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACyM,mCAAP,GAA6C,UAAUvM,GAAV,EAAe;EAC1D,SAAO,KAAKC,OAAL,CAAa,2CAAb,EAA0DD,GAA1D,EAA+D,MAA/D,EAAuE,KAAvE,EAA8E,KAA9E,EAAqF,cAArF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC0M,gBAAP,GAA0B,UAAUxM,GAAV,EAAe;EACvC,SAAO,KAAKC,OAAL,CAAa,qBAAb,EAAoCD,GAApC,EAAyC,MAAzC,EAAiD,KAAjD,EAAwD,KAAxD,EAA+D,eAA/D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC2M,cAAP,GAAwB,UAAUzM,GAAV,EAAe;EACrC,SAAO,KAAKC,OAAL,CAAa,kBAAb,EAAiCD,GAAjC,EAAsC,MAAtC,EAA8C,KAA9C,EAAqD,KAArD,EAA4D,eAA5D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC4M,4BAAP,GAAsC,UAAU1M,GAAV,EAAe;EACnD,SAAO,KAAKC,OAAL,CAAa,oCAAb,EAAmDD,GAAnD,EAAwD,MAAxD,EAAgE,KAAhE,EAAuE,KAAvE,EAA8E,eAA9E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC6M,0BAAP,GAAoC,UAAU3M,GAAV,EAAe;EACjD,SAAO,KAAKC,OAAL,CAAa,iCAAb,EAAgDD,GAAhD,EAAqD,MAArD,EAA6D,KAA7D,EAAoE,KAApE,EAA2E,eAA3E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC8M,+BAAP,GAAyC,UAAU5M,GAAV,EAAe;EACtD,SAAO,KAAKC,OAAL,CAAa,sCAAb,EAAqDD,GAArD,EAA0D,MAA1D,EAAkE,KAAlE,EAAyE,KAAzE,EAAgF,eAAhF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC+M,6BAAP,GAAuC,UAAU7M,GAAV,EAAe;EACpD,SAAO,KAAKC,OAAL,CAAa,mCAAb,EAAkDD,GAAlD,EAAuD,MAAvD,EAA+D,KAA/D,EAAsE,KAAtE,EAA6E,eAA7E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACgN,sCAAP,GAAgD,YAAY;EAC1D,SAAO,KAAK7M,OAAL,CAAa,8CAAb,EAA6D,IAA7D,EAAmE,MAAnE,EAA2E,KAA3E,EAAkF,KAAlF,EAAyF,cAAzF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAH,MAAM,CAACiN,oBAAP,GAA8B,UAAU/M,GAAV,EAAe;EAC3C,SAAO,KAAKC,OAAL,CAAa,0BAAb,EAAyCD,GAAzC,EAA8C,MAA9C,EAAsD,KAAtD,EAA6D,KAA7D,EAAoE,cAApE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACkN,kBAAP,GAA4B,UAAUhN,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,uBAAb,EAAsCD,GAAtC,EAA2C,MAA3C,EAAmD,KAAnD,EAA0D,KAA1D,EAAiE,cAAjE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACmN,iBAAP,GAA2B,UAAUjN,GAAV,EAAe;EACxC,SAAO,KAAKC,OAAL,CAAa,sBAAb,EAAqCD,GAArC,EAA0C,MAA1C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,cAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACoN,eAAP,GAAyB,UAAUlN,GAAV,EAAe;EACtC,SAAO,KAAKC,OAAL,CAAa,mBAAb,EAAkCD,GAAlC,EAAuC,MAAvC,EAA+C,KAA/C,EAAsD,KAAtD,EAA6D,cAA7D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACqN,yBAAP,GAAmC,UAAUnN,GAAV,EAAe;EAChD,SAAO,KAAKC,OAAL,CAAa,+BAAb,EAA8CD,GAA9C,EAAmD,MAAnD,EAA2D,KAA3D,EAAkE,KAAlE,EAAyE,cAAzE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACsN,uBAAP,GAAiC,UAAUpN,GAAV,EAAe;EAC9C,SAAO,KAAKC,OAAL,CAAa,4BAAb,EAA2CD,GAA3C,EAAgD,MAAhD,EAAwD,KAAxD,EAA+D,KAA/D,EAAsE,cAAtE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACuN,gCAAP,GAA0C,UAAUrN,GAAV,EAAe;EACvD,SAAO,KAAKC,OAAL,CAAa,uCAAb,EAAsDD,GAAtD,EAA2D,MAA3D,EAAmE,KAAnE,EAA0E,KAA1E,EAAiF,eAAjF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACwN,8CAAP,GAAwD,UAAUtN,GAAV,EAAe;EACrE,SAAO,KAAKC,OAAL,CAAa,wDAAb,EAAuED,GAAvE,EAA4E,MAA5E,EAAoF,KAApF,EAA2F,KAA3F,EAAkG,eAAlG,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACyN,kBAAP,GAA4B,UAAUvN,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,sBAAb,EAAqCD,GAArC,EAA0C,MAA1C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,gBAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC0N,iBAAP,GAA2B,UAAUxN,GAAV,EAAe;EACxC,SAAO,KAAKC,OAAL,CAAa,qBAAb,EAAoCD,GAApC,EAAyC,MAAzC,EAAiD,KAAjD,EAAwD,KAAxD,EAA+D,gBAA/D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC2N,6BAAP,GAAuC,UAAUzN,GAAV,EAAe;EACpD,SAAO,KAAKC,OAAL,CAAa,oCAAb,EAAmDD,GAAnD,EAAwD,MAAxD,EAAgE,KAAhE,EAAuE,KAAvE,EAA8E,gBAA9E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC4N,6BAAP,GAAuC,UAAU1N,GAAV,EAAe;EACpD,SAAO,KAAKC,OAAL,CAAa,mCAAb,EAAkDD,GAAlD,EAAuD,MAAvD,EAA+D,KAA/D,EAAsE,KAAtE,EAA6E,eAA7E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC6N,gCAAP,GAA0C,UAAU3N,GAAV,EAAe;EACvD,SAAO,KAAKC,OAAL,CAAa,sCAAb,EAAqDD,GAArD,EAA0D,MAA1D,EAAkE,KAAlE,EAAyE,KAAzE,EAAgF,eAAhF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC8N,kDAAP,GAA4D,UAAU5N,GAAV,EAAe;EACzE,SAAO,KAAKC,OAAL,CAAa,0DAAb,EAAyED,GAAzE,EAA8E,MAA9E,EAAsF,KAAtF,EAA6F,KAA7F,EAAoG,eAApG,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC+N,2BAAP,GAAqC,UAAU7N,GAAV,EAAe;EAClD,SAAO,KAAKC,OAAL,CAAa,iCAAb,EAAgDD,GAAhD,EAAqD,MAArD,EAA6D,KAA7D,EAAoE,KAApE,EAA2E,eAA3E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACgO,gCAAP,GAA0C,UAAU9N,GAAV,EAAe;EACvD,SAAO,KAAKC,OAAL,CAAa,uCAAb,EAAsDD,GAAtD,EAA2D,MAA3D,EAAmE,KAAnE,EAA0E,KAA1E,EAAiF,eAAjF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACiO,8BAAP,GAAwC,UAAU/N,GAAV,EAAe;EACrD,SAAO,KAAKC,OAAL,CAAa,oCAAb,EAAmDD,GAAnD,EAAwD,MAAxD,EAAgE,KAAhE,EAAuE,KAAvE,EAA8E,eAA9E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACkO,uBAAP,GAAiC,UAAUhO,GAAV,EAAe;EAC9C,SAAO,KAAKC,OAAL,CAAa,6BAAb,EAA4CD,GAA5C,EAAiD,MAAjD,EAAyD,KAAzD,EAAgE,KAAhE,EAAuE,eAAvE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACmO,qBAAP,GAA+B,UAAUjO,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,0BAAb,EAAyCD,GAAzC,EAA8C,MAA9C,EAAsD,KAAtD,EAA6D,KAA7D,EAAoE,eAApE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACoO,4BAAP,GAAsC,UAAUlO,GAAV,EAAe;EACnD,SAAO,KAAKC,OAAL,CAAa,mCAAb,EAAkDD,GAAlD,EAAuD,MAAvD,EAA+D,KAA/D,EAAsE,KAAtE,EAA6E,eAA7E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACqO,0BAAP,GAAoC,UAAUnO,GAAV,EAAe;EACjD,SAAO,KAAKC,OAAL,CAAa,gCAAb,EAA+CD,GAA/C,EAAoD,MAApD,EAA4D,KAA5D,EAAmE,KAAnE,EAA0E,eAA1E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACsO,kBAAP,GAA4B,UAAUpO,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,sBAAb,EAAqCD,GAArC,EAA0C,MAA1C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,eAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACuO,oBAAP,GAA8B,UAAUrO,GAAV,EAAe;EAC3C,SAAO,KAAKC,OAAL,CAAa,wBAAb,EAAuCD,GAAvC,EAA4C,MAA5C,EAAoD,KAApD,EAA2D,KAA3D,EAAkE,eAAlE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACwO,kBAAP,GAA4B,UAAUtO,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,sBAAb,EAAqCD,GAArC,EAA0C,MAA1C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,kBAAhE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACyO,0BAAP,GAAoC,UAAUvO,GAAV,EAAe;EACjD,SAAO,KAAKC,OAAL,CAAa,+BAAb,EAA8CD,GAA9C,EAAmD,MAAnD,EAA2D,KAA3D,EAAkE,KAAlE,EAAyE,kBAAzE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC0O,yBAAP,GAAmC,UAAUxO,GAAV,EAAe;EAChD,SAAO,KAAKC,OAAL,CAAa,8BAAb,EAA6CD,GAA7C,EAAkD,MAAlD,EAA0D,KAA1D,EAAiE,KAAjE,EAAwE,2BAAxE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC2O,yBAAP,GAAmC,UAAUzO,GAAV,EAAe;EAChD,SAAO,KAAKC,OAAL,CAAa,8BAAb,EAA6CD,GAA7C,EAAkD,MAAlD,EAA0D,KAA1D,EAAiE,KAAjE,EAAwE,2BAAxE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC4O,0BAAP,GAAoC,YAAY;EAC9C,SAAO,KAAKzO,OAAL,CAAa,+BAAb,EAA8C,IAA9C,EAAoD,MAApD,EAA4D,KAA5D,EAAmE,KAAnE,EAA0E,2BAA1E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAH,MAAM,CAAC6O,4BAAP,GAAsC,UAAU3O,GAAV,EAAe;EACnD,SAAO,KAAKC,OAAL,CAAa,iCAAb,EAAgDD,GAAhD,EAAqD,MAArD,EAA6D,KAA7D,EAAoE,KAApE,EAA2E,2BAA3E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC8O,sBAAP,GAAgC,UAAU5O,GAAV,EAAe;EAC7C,SAAO,KAAKC,OAAL,CAAa,2BAAb,EAA0CD,GAA1C,EAA+C,MAA/C,EAAuD,KAAvD,EAA8D,KAA9D,EAAqE,gBAArE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC+O,qBAAP,GAA+B,UAAU7O,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,0BAAb,EAAyCD,GAAzC,EAA8C,MAA9C,EAAsD,KAAtD,EAA6D,KAA7D,EAAoE,gBAApE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACgP,wBAAP,GAAkC,UAAU9O,GAAV,EAAe;EAC/C,SAAO,KAAKC,OAAL,CAAa,6BAAb,EAA4CD,GAA5C,EAAiD,MAAjD,EAAyD,KAAzD,EAAgE,KAAhE,EAAuE,gBAAvE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACiP,qBAAP,GAA+B,UAAU/O,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,0BAAb,EAAyCD,GAAzC,EAA8C,MAA9C,EAAsD,KAAtD,EAA6D,KAA7D,EAAoE,gBAApE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACkP,uBAAP,GAAiC,UAAUhP,GAAV,EAAe;EAC9C,SAAO,KAAKC,OAAL,CAAa,4BAAb,EAA2CD,GAA3C,EAAgD,MAAhD,EAAwD,KAAxD,EAA+D,KAA/D,EAAsE,iBAAtE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACmP,wBAAP,GAAkC,UAAUjP,GAAV,EAAe;EAC/C,SAAO,KAAKC,OAAL,CAAa,6BAAb,EAA4CD,GAA5C,EAAiD,MAAjD,EAAyD,KAAzD,EAAgE,KAAhE,EAAuE,gBAAvE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACoP,gCAAP,GAA0C,UAAUlP,GAAV,EAAe;EACvD,SAAO,KAAKC,OAAL,CAAa,sCAAb,EAAqDD,GAArD,EAA0D,MAA1D,EAAkE,KAAlE,EAAyE,KAAzE,EAAgF,gBAAhF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACqP,0BAAP,GAAoC,UAAUnP,GAAV,EAAe;EACjD,SAAO,KAAKC,OAAL,CAAa,+BAAb,EAA8CD,GAA9C,EAAmD,MAAnD,EAA2D,KAA3D,EAAkE,KAAlE,EAAyE,iBAAzE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACsP,sBAAP,GAAgC,UAAUpP,GAAV,EAAe;EAC7C,SAAO,KAAKC,OAAL,CAAa,2BAAb,EAA0CD,GAA1C,EAA+C,MAA/C,EAAuD,KAAvD,EAA8D,KAA9D,EAAqE,yBAArE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACuP,qBAAP,GAA+B,UAAUrP,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,0BAAb,EAAyCD,GAAzC,EAA8C,MAA9C,EAAsD,KAAtD,EAA6D,KAA7D,EAAoE,yBAApE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACwP,0BAAP,GAAoC,UAAUtP,GAAV,EAAe;EACjD,SAAO,KAAKC,OAAL,CAAa,gCAAb,EAA+CD,GAA/C,EAAoD,MAApD,EAA4D,KAA5D,EAAmE,KAAnE,EAA0E,yBAA1E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACyP,oBAAP,GAA8B,UAAUvP,GAAV,EAAe;EAC3C,SAAO,KAAKC,OAAL,CAAa,yBAAb,EAAwCD,GAAxC,EAA6C,MAA7C,EAAqD,KAArD,EAA4D,KAA5D,EAAmE,yBAAnE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC0P,qBAAP,GAA+B,UAAUxP,GAAV,EAAe;EAC5C,SAAO,KAAKC,OAAL,CAAa,2BAAb,EAA0CD,GAA1C,EAA+C,MAA/C,EAAuD,KAAvD,EAA8D,KAA9D,EAAqE,wBAArE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC2P,kBAAP,GAA4B,UAAUzP,GAAV,EAAe;EACzC,SAAO,KAAKC,OAAL,CAAa,uBAAb,EAAsCD,GAAtC,EAA2C,MAA3C,EAAmD,KAAnD,EAA0D,KAA1D,EAAiE,wBAAjE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC4P,0BAAP,GAAoC,UAAU1P,GAAV,EAAe;EACjD,SAAO,KAAKC,OAAL,CAAa,gCAAb,EAA+CD,GAA/C,EAAoD,MAApD,EAA4D,KAA5D,EAAmE,KAAnE,EAA0E,wBAA1E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC6P,+BAAP,GAAyC,UAAU3P,GAAV,EAAe;EACtD,SAAO,KAAKC,OAAL,CAAa,qCAAb,EAAoDD,GAApD,EAAyD,MAAzD,EAAiE,KAAjE,EAAwE,KAAxE,EAA+E,yBAA/E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC8P,oBAAP,GAA8B,UAAU5P,GAAV,EAAe;EAC3C,SAAO,KAAKC,OAAL,CAAa,yBAAb,EAAwCD,GAAxC,EAA6C,MAA7C,EAAqD,KAArD,EAA4D,KAA5D,EAAmE,yBAAnE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAAC+P,gCAAP,GAA0C,UAAU7P,GAAV,EAAe;EACvD,SAAO,KAAKC,OAAL,CAAa,uCAAb,EAAsDD,GAAtD,EAA2D,MAA3D,EAAmE,KAAnE,EAA0E,KAA1E,EAAiF,yBAAjF,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACgQ,8BAAP,GAAwC,YAAY;EAClD,SAAO,KAAK7P,OAAL,CAAa,oCAAb,EAAmD,IAAnD,EAAyD,MAAzD,EAAiE,KAAjE,EAAwE,KAAxE,EAA+E,gBAA/E,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAH,MAAM,CAACiQ,gBAAP,GAA0B,UAAU/P,GAAV,EAAe;EACvC,SAAO,KAAKC,OAAL,CAAa,qBAAb,EAAoCD,GAApC,EAAyC,MAAzC,EAAiD,KAAjD,EAAwD,KAAxD,EAA+D,aAA/D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACkQ,wBAAP,GAAkC,UAAUhQ,GAAV,EAAe;EAC/C,SAAO,KAAKC,OAAL,CAAa,8BAAb,EAA6CD,GAA7C,EAAkD,MAAlD,EAA0D,KAA1D,EAAiE,KAAjE,EAAwE,aAAxE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACmQ,sBAAP,GAAgC,UAAUjQ,GAAV,EAAe;EAC7C,SAAO,KAAKC,OAAL,CAAa,2BAAb,EAA0CD,GAA1C,EAA+C,MAA/C,EAAuD,KAAvD,EAA8D,KAA9D,EAAqE,mBAArE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACoQ,eAAP,GAAyB,UAAUlQ,GAAV,EAAe;EACtC,SAAO,KAAKC,OAAL,CAAa,mBAAb,EAAkCD,GAAlC,EAAuC,MAAvC,EAA+C,KAA/C,EAAsD,KAAtD,EAA6D,cAA7D,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACqQ,oBAAP,GAA8B,UAAUnQ,GAAV,EAAe;EAC3C,SAAO,KAAKC,OAAL,CAAa,yBAAb,EAAwCD,GAAxC,EAA6C,MAA7C,EAAqD,KAArD,EAA4D,KAA5D,EAAmE,cAAnE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAF,MAAM,CAACsQ,sBAAP,GAAgC,YAAY;EAC1C,SAAO,KAAKnQ,OAAL,CAAa,2BAAb,EAA0C,IAA1C,EAAgD,MAAhD,EAAwD,KAAxD,EAA+D,KAA/D,EAAsE,mBAAtE,CAAP;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAH,MAAM,CAACuQ,kBAAP,GAA4B,YAAY;EACtC,SAAO,KAAKpQ,OAAL,CAAa,uBAAb,EAAsC,IAAtC,EAA4C,MAA5C,EAAoD,KAApD,EAA2D,KAA3D,EAAkE,mBAAlE,CAAP;EACD,CAFD;;EChsHA,SAASqQ,cAAT,CAAwBC,CAAxB,EAA2B;EACzB,MAAMC,OAAO,GAAG,aAAMD,CAAC,CAACE,UAAF,CAAa,CAAb,EAAgBC,QAAhB,CAAyB,EAAzB,CAAN,EAAqCC,KAArC,CAA2C,CAAC,CAA5C,CAAhB;EACA,sBAAaH,OAAb;EACD;;EAEM,IAAMI,UAAU,GAAG,SAAbA,UAAa,CAACC,SAAD,EAAmE;EAAA,MAAvDC,MAAuD,uEAA9CtR,kBAA8C;EAAA,MAA1BuR,eAA0B,uEAAR,GAAQ;;EAC3F,MAAI,CAACA,eAAL,EAAsB;EACpB,6BAAkBD,MAAlB;EACD;;EACD,MAAIA,MAAM,KAAKtR,kBAAX,IAAiCE,oBAAoB,CAACmR,SAAD,CAApB,KAAoCG,SAAzE,EAAoF;EAClFH,IAAAA,SAAS,GAAGnR,oBAAoB,CAACmR,SAAD,CAAhC;EACAE,IAAAA,eAAe,GAAG,GAAlB;EACD;;EACD,2BAAkBF,SAAlB,SAA8BE,eAA9B,SAAgDD,MAAhD;EACD,CATM;EAUA,IAAMG,sBAAsB,GAAG,SAAzBA,sBAAyB,GAA6B;EAAA,MAA5BH,MAA4B,uEAAnBrR,cAAmB;;EACjE,MAAIqR,MAAM,KAAKrR,cAAf,EAA+B;EAC7BqR,IAAAA,MAAM,kBAAWA,MAAX,CAAN;EACD;;EACD,2BAAkBA,MAAlB;EACD,CALM;EAMA,IAAMI,cAAc,GAAG,SAAjBA,cAAiB,GAAwD;EAAA,MAAvDJ,MAAuD,uEAA9CtR,kBAA8C;EAAA,MAA1BuR,eAA0B,uEAAR,GAAQ;EACpF,MAAIF,SAAS,GAAG,KAAhB;;EACA,MAAIC,MAAM,KAAKtR,kBAAf,EAAmC;EACjCqR,IAAAA,SAAS,GAAGnR,oBAAoB,CAACmR,SAAD,CAAhC;EACAE,IAAAA,eAAe,GAAG,GAAlB;EACD;;EACD,2BAAkBF,SAAlB,SAA8BE,eAA9B,SAAgDD,MAAhD;EACD,CAPM;;EAUA,SAASK,kBAAT,CAA4BC,IAA5B,EAAkC;EACvC,SAAOC,IAAI,CAACC,SAAL,CAAeF,IAAf,EAAqBG,OAArB,CAA6B,kBAA7B,EAAiDjB,cAAjD,CAAP;EACD;EAEM,SAASkB,qBAAT,CAA+BC,SAA/B,EAA0C;EAC/C,SAAO,IAAIC,IAAJ,CAASA,IAAI,CAACC,GAAL,KAAcF,SAAS,GAAG,IAAnC,CAAP;EACD;EAED;;EACO,SAASG,gBAAT,GAA4B;EACjC,SAEI,OAAOC,iBAAP,KAA6B,WAA7B,IACSC,IAAI,YAAYD,iBAF3B;EAAA,KAKM,OAAOE,MAAP,KAAkB,WAAlB,IACK,OAAOC,MAAP,KAAkB,WAP/B;EAUD;EAEM,SAASC,YAAT,GAAwB;EAC7B,SAAO,OAAOD,MAAP,KAAkB,WAAzB;EACD;EAEM,SAASE,WAAT,GAAuB;EAC5B,SAAO,OAAOL,iBAAP,KAA6B,WAA7B,IAA4CC,IAAI,YAAYD,iBAAnE,CAD4B;EAE7B;EAEM,SAASM,uBAAT,CAAiCC,aAAjC,EAAgD;EACrD,MAAMC,eAAe,GAAGD,aAAa,CAAC1B,QAAd,CAAuB,QAAvB,EACrBa,OADqB,CACb,KADa,EACN,GADM,EAErBA,OAFqB,CAEb,KAFa,EAEN,GAFM,EAGrBA,OAHqB,CAGb,IAHa,EAGP,EAHO,CAAxB;EAIA,SAAOc,eAAP;EACD;;ECrED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;MACaC,oBAAb;EAAA;;EAAA;;EACE,gCAAYC,MAAZ,EAAoBC,OAApB,EAA6BC,KAA7B,EAAoC;EAAA;;EAAA;;EAClC,+DAAgCF,MAAhC;EACA,UAAKG,IAAL,GAAY,sBAAZ;EACA,UAAKH,MAAL,GAAcA,MAAd;EACA,UAAKC,OAAL,GAAeA,OAAf;EACA,UAAKC,KAAL,GAAaA,KAAb;EALkC;EAMnC;;EAPH;EAAA,iCAA0CE,KAA1C;;MCLaC,eAAb,GACE,yBAAYL,MAAZ,EAAoBC,OAApB,EAA6BK,MAA7B,EAAqC;EAAA;;EACnC,OAAKN,MAAL,GAAcA,MAAd;EACA,OAAKC,OAAL,GAAeA,OAAf;EACA,OAAKK,MAAL,GAAcA,MAAd;EACD;;EAGH,SAASC,YAAT,CAAsBC,GAAtB,EAA2B;EACzB,SAAOA,GAAG,CAACC,IAAJ,GACJC,IADI,CACC,UAACC,IAAD,EAAU;EACd,QAAIC,WAAJ;;EACA,QAAI;EACFA,MAAAA,WAAW,GAAG9B,IAAI,CAAC+B,KAAL,CAAWF,IAAX,CAAd;EACD,KAFD,CAEE,OAAOT,KAAP,EAAc;EACdU,MAAAA,WAAW,GAAGD,IAAd;EACD;;EAED,UAAM,IAAIZ,oBAAJ,CAAyBS,GAAG,CAACR,MAA7B,EAAqCQ,GAAG,CAACP,OAAzC,EAAkDW,WAAlD,CAAN;EACD,GAVI,CAAP;EAWD;;EAEM,SAASE,aAAT,CAAuBN,GAAvB,EAA4B;EACjC,MAAI,CAACA,GAAG,CAACO,EAAT,EAAa;EACX,WAAOR,YAAY,CAACC,GAAD,CAAnB;EACD;;EACD,SAAOA,GAAG,CAACC,IAAJ,GACJC,IADI,CACC,UAACC,IAAD,EAAU;EACd,QAAIK,cAAJ;;EACA,QAAI;EACFA,MAAAA,cAAc,GAAGlC,IAAI,CAAC+B,KAAL,CAAWF,IAAX,CAAjB;EACD,KAFD,CAEE,OAAOT,KAAP,EAAc;EACdc,MAAAA,cAAc,GAAGL,IAAjB;EACD;;EAED,WAAO,IAAIN,eAAJ,CAAoBG,GAAG,CAACR,MAAxB,EAAgCQ,GAAG,CAACP,OAApC,EAA6Ce,cAA7C,CAAP;EACD,GAVI,CAAP;EAWD;EAEM,SAASC,qBAAT,CAA+BT,GAA/B,EAAoC;EACzC,MAAI,CAACA,GAAG,CAACO,EAAT,EAAa;EACX,WAAOR,YAAY,CAACC,GAAD,CAAnB;EACD;;EACD,SAAO,IAAIU,OAAJ,CAAY,UAACC,OAAD,EAAa;EAC9B,QAAI9B,gBAAgB,EAApB,EAAwB;EACtBmB,MAAAA,GAAG,CAACY,IAAJ,GAAWV,IAAX,CAAgB,UAACC,IAAD;EAAA,eAAUQ,OAAO,CAACR,IAAD,CAAjB;EAAA,OAAhB;EACD,KAFD,MAEO;EACLH,MAAAA,GAAG,CAACa,MAAJ,GAAaX,IAAb,CAAkB,UAACC,IAAD;EAAA,eAAUQ,OAAO,CAACR,IAAD,CAAjB;EAAA,OAAlB;EACD;EACF,GANM,EAMJD,IANI,CAMC,UAACC,IAAD,EAAU;EAChB,QAAML,MAAM,GAAGxB,IAAI,CAAC+B,KAAL,CAAWL,GAAG,CAACP,OAAJ,CAAYqB,GAAZ,CAAgB,oBAAhB,CAAX,CAAf;;EAEA,QAAIjC,gBAAgB,EAApB,EAAwB;EACtBiB,MAAAA,MAAM,CAACiB,QAAP,GAAkBZ,IAAlB;EACD,KAFD,MAEO;EACLL,MAAAA,MAAM,CAACkB,UAAP,GAAoBb,IAApB;EACD;;EAED,WAAO,IAAIN,eAAJ,CAAoBG,GAAG,CAACR,MAAxB,EAAgCQ,GAAG,CAACP,OAApC,EAA6CK,MAA7C,CAAP;EACD,GAhBM,CAAP;EAiBD;;ECrDD,IAAImB,KAAJ;EACA,IAAIC,MAAJ;EACA,IAAIC,OAAJ;;EAGA,IAAMC,qBAAqB,GAAG,MAAM,IAApC;EACA,IAAMC,UAAU,GAAG,GAAnB;EACA,IAAMC,gBAAgB,GAAG,CAAC,QAAD,EAAW,SAAX,EAAsB,QAAtB,CAAzB;EACA,IAAMC,UAAU,GAAG,CAAC,MAAD,EAAS,OAAT,CAAnB;EACA,IAAMC,oBAAoB,GAAG,CAAC,MAAD,EAAS,MAAT,EAAiB,MAAjB,CAA7B;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;MACqBC;EACnB,uBAAYC,OAAZ,EAAqB;EAAA;;EACnBA,IAAAA,OAAO,GAAGA,OAAO,IAAI,EAArB;;EAEA,QAAIxC,YAAY,EAAhB,EAAoB;EAClB+B,MAAAA,KAAK,GAAGhC,MAAM,CAACgC,KAAP,CAAaU,IAAb,CAAkB1C,MAAlB,CAAR;EACAiC,MAAAA,MAAM,GAAGjC,MAAM,CAACiC,MAAP,IAAiBjC,MAAM,CAAC2C,QAAjC,CAFkB;EAGnB,KAHD,MAGO,IAAIzC,WAAW,EAAf,EAAmB;EACxB;EACA8B,MAAAA,KAAK,GAAGlC,IAAI,CAACkC,KAAL,CAAWU,IAAX,CAAgB5C,IAAhB,CAAR;EACAmC,MAAAA,MAAM,GAAGnC,IAAI,CAACmC,MAAd;EACA;EACD,KALM,MAKA;EACLD,MAAAA,KAAK,GAAGY,OAAO,CAAC,YAAD,CAAf,CADK;;EAELX,MAAAA,MAAM,GAAGW,OAAO,CAAC,QAAD,CAAhB,CAFK;EAGN;;EAED,QAAI,OAAOC,WAAP,KAAuB,WAA3B,EAAwC;EACtCX,MAAAA,OAAO,GAAGU,OAAO,CAAC,MAAD,CAAP,CAAgBC,WAA1B,CADsC;EAEvC,KAFD,MAEO;EACLX,MAAAA,OAAO,GAAGW,WAAV;EACD;;EAED,SAAKb,KAAL,GAAaS,OAAO,CAACT,KAAR,IAAiBA,KAA9B;EACA,SAAKc,WAAL,GAAmBL,OAAO,CAACK,WAA3B;EACA,SAAKC,oBAAL,GAA4BN,OAAO,CAACM,oBAApC;EACA,SAAKC,YAAL,GAAoBP,OAAO,CAACO,YAA5B;EACA,SAAKC,QAAL,GAAgBR,OAAO,CAACQ,QAAxB;EACA,SAAKC,YAAL,GAAoBT,OAAO,CAACS,YAA5B;EAEA,SAAKpE,MAAL,GAAc2D,OAAO,CAAC3D,MAAtB;EACA,SAAKC,eAAL,GAAuB0D,OAAO,CAAC1D,eAA/B;EACA,SAAKoE,aAAL,GAAqBV,OAAO,CAACU,aAA7B;EACA,SAAKC,UAAL,GAAkBX,OAAO,CAACW,UAA1B;EACD;EAED;EACF;EACA;EACA;EACA;;;;;aACE,wBAAeN,WAAf,EAA4B;EAC1B,WAAKA,WAAL,GAAmBA,WAAnB;EACD;EAED;EACF;EACA;EACA;;;;aACE,0BAAiB;EACf,aAAO,KAAKA,WAAZ;EACD;EAED;EACF;EACA;EACA;EACA;;;;aACE,qBAAYG,QAAZ,EAAsB;EACpB,WAAKA,QAAL,GAAgBA,QAAhB;EACD;EAED;EACF;EACA;EACA;;;;aACE,uBAAc;EACZ,aAAO,KAAKA,QAAZ;EACD;EAED;EACF;EACA;EACA;EACA;;;;aACE,yBAAgBC,YAAhB,EAA8B;EAC5B,WAAKA,YAAL,GAAoBA,YAApB;EACD;EAED;EACF;EACA;EACA;;;;aACE,2BAAkB;EAChB,aAAO,KAAKA,YAAZ;EACD;EAED;EACF;EACA;EACA;;;;aACE,2BAAkB;EAChB,aAAO,KAAKF,YAAZ;EACD;EAED;EACF;EACA;EACA;;;;aACE,yBAAgBA,YAAhB,EAA8B;EAC5B,WAAKA,YAAL,GAAoBA,YAApB;EACD;EAED;EACF;EACA;EACA;;;;aACE,mCAA0B;EACxB,aAAO,KAAKD,oBAAZ;EACD;EAED;EACF;EACA;EACA;;;;aACE,iCAAwBA,oBAAxB,EAA8C;EAC5C,WAAKA,oBAAL,GAA4BA,oBAA5B;EACD;EAED;EACF;EACA;EACA;;;;aACE,yBAAgBM,YAAhB,EAA8B;EAC5B,WAAKA,YAAL,GAAoBA,YAApB;EACD;EAED;EACF;EACA;EACA;;;;aACE,2BAAkB;EAChB,aAAO,KAAKA,YAAZ;EACD;;;aAED,iCAAwB;EAAA;;EACtB,UAAMC,OAAO,GAAG,IAAIpB,OAAJ,EAAhB;EACA,UAAMqB,QAAQ,GAAGD,OAAO,CAACE,MAAR,CAAe,KAAKH,YAApB,CAAjB;EACA,UAAII,aAAJ;;EACA,UAAIxD,YAAY,MAAMC,WAAW,EAAjC,EAAqC;EACnC,eAAO+B,MAAM,CAACyB,MAAP,CAAcC,MAAd,CAAqB,SAArB,EAAgCJ,QAAhC,EACJtC,IADI,CACC,UAAC2C,YAAD,EAAkB;EACtB,cAAMC,YAAY,GAAGC,IAAI,CAACC,MAAM,CAACC,YAAP,CAAoBC,KAApB,CAA0B,IAA1B,EAAgC,IAAIC,UAAJ,CAAeN,YAAf,CAAhC,CAAD,CAAzB;EACAH,UAAAA,aAAa,GAAGtD,uBAAuB,CAAC0D,YAAD,CAAvB,CAAsCM,MAAtC,CAA6C,CAA7C,EAAgD,GAAhD,CAAhB;EACA,UAAA,KAAI,CAACV,aAAL,GAAqBA,aAArB;EACD,SALI,CAAP;EAMD;;EACD,UAAMG,YAAY,GAAG3B,MAAM,CAACmC,UAAP,CAAkB,QAAlB,EAA4BC,MAA5B,CAAmCd,QAAnC,EAA6CI,MAA7C,EAArB;EACAF,MAAAA,aAAa,GAAGtD,uBAAuB,CAACyD,YAAD,CAAvC;EACA,WAAKH,aAAL,GAAqBA,aAArB;EACA,aAAOhC,OAAO,CAACC,OAAR,EAAP;EACD;;;aAED,6BAAoB;EAClB,UAAI2B,YAAJ;;EACA,UAAIpD,YAAY,MAAMC,WAAW,EAAjC,EAAqC;EACnC,YAAMoE,KAAK,GAAG,IAAIJ,UAAJ,CAAe9B,UAAf,CAAd;EACA,YAAMmC,gBAAgB,GAAGtC,MAAM,CAACuC,eAAP,CAAuBF,KAAvB,CAAzB;EACA,YAAMT,YAAY,GAAGC,IAAI,CAACS,gBAAD,CAAzB;EACAlB,QAAAA,YAAY,GAAGlD,uBAAuB,CAAC0D,YAAD,CAAvB,CAAsCM,MAAtC,CAA6C,CAA7C,EAAgD,GAAhD,CAAf;EACD,OALD,MAKO;EACL,YAAMM,WAAW,GAAGxC,MAAM,CAACwC,WAAP,CAAmBrC,UAAnB,CAApB;EACAiB,QAAAA,YAAY,GAAGlD,uBAAuB,CAACsE,WAAD,CAAvB,CAAqCN,MAArC,CAA4C,CAA5C,EAA+C,GAA/C,CAAf;EACD;;EACD,WAAKd,YAAL,GAAoBA,YAApB;EAEA,aAAO,KAAKqB,qBAAL,EAAP;EACD;EAED;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;;aACE,8BAAqBC,WAArB,EAAkCC,KAAlC,EAAmJ;EAAA;;EAAA,UAA1GC,QAA0G,uEAA/F,OAA+F;EAAA,UAAtFC,eAAsF,uEAApE,IAAoE;EAAA,UAA9DC,KAA8D,uEAAtD,IAAsD;EAAA,UAAhDC,oBAAgD,uEAAzB,MAAyB;EAAA,UAAjBC,OAAiB,uEAAP,KAAO;EACjJ,UAAMhC,QAAQ,GAAG,KAAKiC,WAAL,EAAjB;EACA,UAAMC,OAAO,GAAGlG,sBAAsB,CAAC,KAAKH,MAAN,CAAtC;;EAEA,UAAI,CAACmE,QAAL,EAAe;EACb,cAAM,IAAItC,KAAJ,CAAU,0EAAV,CAAN;EACD;;EACD,UAAIkE,QAAQ,KAAK,MAAb,IAAuB,CAACF,WAA5B,EAAyC;EACvC,cAAM,IAAIhE,KAAJ,CAAU,6BAAV,CAAN;EACD;;EACD,UAAI,CAAC2B,UAAU,CAAC8C,QAAX,CAAoBP,QAApB,CAAL,EAAoC;EAClC,cAAM,IAAIlE,KAAJ,CAAU,0CAAV,CAAN;EACD;;EACD,UAAImE,eAAe,IAAI,CAACzC,gBAAgB,CAAC+C,QAAjB,CAA0BN,eAA1B,CAAxB,EAAoE;EAClE,cAAM,IAAInE,KAAJ,CAAU,sDAAV,CAAN;EACD;;EACD,UAAIoE,KAAK,IAAI,EAAEA,KAAK,YAAYM,KAAnB,CAAb,EAAwC;EACtC,cAAM,IAAI1E,KAAJ,CAAU,mCAAV,CAAN;EACD;;EACD,UAAI,CAAC4B,oBAAoB,CAAC6C,QAArB,CAA8BJ,oBAA9B,CAAL,EAA0D;EACxD,cAAM,IAAIrE,KAAJ,CAAU,kDAAV,CAAN;EACD;;EAED,UAAI2E,OAAJ;;EACA,UAAIT,QAAQ,KAAK,MAAjB,EAAyB;EACvBS,QAAAA,OAAO,aAAMH,OAAN,2CAA8ClC,QAA9C,CAAP;EACD,OAFD,MAEO;EACLqC,QAAAA,OAAO,aAAMH,OAAN,4CAA+ClC,QAA/C,CAAP;EACD;;EAED,UAAI0B,WAAJ,EAAiB;EACfW,QAAAA,OAAO,4BAAqBX,WAArB,CAAP;EACD;;EACD,UAAIC,KAAJ,EAAW;EACTU,QAAAA,OAAO,qBAAcV,KAAd,CAAP;EACD;;EACD,UAAIE,eAAJ,EAAqB;EACnBQ,QAAAA,OAAO,iCAA0BR,eAA1B,CAAP;EACD;;EACD,UAAIC,KAAJ,EAAW;EACTO,QAAAA,OAAO,qBAAcP,KAAK,CAACQ,IAAN,CAAW,GAAX,CAAd,CAAP;EACD;;EACD,UAAIP,oBAAoB,KAAK,MAA7B,EAAqC;EACnCM,QAAAA,OAAO,sCAA+BN,oBAA/B,CAAP;EACD;;EACD,UAAIC,OAAJ,EAAa;EACX,eAAO,KAAKO,iBAAL,GACJvE,IADI,CACC,YAAM;EACVqE,UAAAA,OAAO,IAAI,6BAAX;EACAA,UAAAA,OAAO,8BAAuB,MAAI,CAAC7B,aAA5B,CAAP;EACA,iBAAO6B,OAAP;EACD,SALI,CAAP;EAMD;;EACD,aAAO7D,OAAO,CAACC,OAAR,CAAgB4D,OAAhB,CAAP;EACD;EAED;EACF;EACA;EACA;EACA;EACA;EACA;;;;aACE,gCAAuBX,WAAvB,EAAoCc,IAApC,EAA0C;EACxC,UAAMxC,QAAQ,GAAG,KAAKiC,WAAL,EAAjB;EACA,UAAMhC,YAAY,GAAG,KAAKwC,eAAL,EAArB;;EAEA,UAAI,CAACzC,QAAL,EAAe;EACb,cAAM,IAAItC,KAAJ,CAAU,0EAAV,CAAN;EACD;;EACD,UAAIgF,IAAI,GAAGzG,cAAc,CAAC,KAAKJ,MAAN,EAAc,KAAKC,eAAnB,CAAzB;EACA4G,MAAAA,IAAI,IAAI,gCAAR;EACAA,MAAAA,IAAI,oBAAaF,IAAb,CAAJ;EACAE,MAAAA,IAAI,yBAAkB1C,QAAlB,CAAJ;;EAEA,UAAIC,YAAJ,EAAkB;EAChByC,QAAAA,IAAI,6BAAsBzC,YAAtB,CAAJ;EACD,OAFD,MAEO;EACL,YAAI,CAAC,KAAKG,YAAV,EAAwB;EACtB,gBAAM,IAAI1C,KAAJ,CAAU,wFAAV,CAAN;EACD;;EACDgF,QAAAA,IAAI,6BAAsB,KAAKtC,YAA3B,CAAJ;EACD;;EACD,UAAIsB,WAAJ,EAAiB;EACfgB,QAAAA,IAAI,4BAAqBhB,WAArB,CAAJ;EACD;;EAED,UAAMiB,YAAY,GAAG;EACnBC,QAAAA,MAAM,EAAE,MADW;EAEnBrF,QAAAA,OAAO,EAAE;EACP,0BAAgB;EADT;EAFU,OAArB;EAMA,aAAO,KAAKwB,KAAL,CAAW2D,IAAX,EAAiBC,YAAjB,EACJ3E,IADI,CACC,UAACF,GAAD;EAAA,eAASM,aAAa,CAACN,GAAD,CAAtB;EAAA,OADD,CAAP;EAED;EAED;EACF;EACA;EACA;EACA;;;;aACE,sCAA6B;EAC3B,UAAM+E,UAAU,GAAG,KAAKC,eAAL,MAA0B,KAAKb,WAAL,EAA7C;EACA,UAAMc,YAAY,GAAG,CAAC,KAAKC,uBAAL,EAAD,IACT,IAAIvG,IAAJ,CAASA,IAAI,CAACC,GAAL,KAAawC,qBAAtB,CAAD,IAAkD,KAAK8D,uBAAL,EAD7D;EAEA,UAAMC,UAAU,GAAG,CAAC,KAAKC,cAAL,EAApB;;EACA,UAAI,CAACH,YAAY,IAAIE,UAAjB,KAAgCJ,UAApC,EAAgD;EAC9C,eAAO,KAAKM,kBAAL,EAAP;EACD;;EACD,aAAO3E,OAAO,CAACC,OAAR,EAAP;EACD;EAED;EACF;EACA;EACA;EACA;EACA;;;;aACE,8BAAiC;EAAA;;EAAA,UAAdqD,KAAc,uEAAN,IAAM;EAC/B,UAAM9B,QAAQ,GAAG,KAAKiC,WAAL,EAAjB;EACA,UAAMhC,YAAY,GAAG,KAAKwC,eAAL,EAArB;;EAEA,UAAI,CAACzC,QAAL,EAAe;EACb,cAAM,IAAItC,KAAJ,CAAU,0EAAV,CAAN;EACD;;EACD,UAAIoE,KAAK,IAAI,EAAEA,KAAK,YAAYM,KAAnB,CAAb,EAAwC;EACtC,cAAM,IAAI1E,KAAJ,CAAU,mCAAV,CAAN;EACD;;EAED,UAAI0F,UAAU,GAAGnH,cAAc,CAAC,KAAKJ,MAAN,EAAc,KAAKC,eAAnB,CAA/B;EACA,UAAM6G,YAAY,GAAG;EACnBpF,QAAAA,OAAO,EAAE;EAAE,0BAAgB;EAAlB,SADU;EAEnBqF,QAAAA,MAAM,EAAE;EAFW,OAArB;;EAKA,UAAI,KAAKzC,UAAT,EAAqB;EACnB,YAAMkD,IAAI,GAAG;EAAEC,UAAAA,UAAU,EAAE,eAAd;EAA+BC,UAAAA,SAAS,EAAEvD,QAA1C;EAAoDwD,UAAAA,aAAa,EAAE,KAAKV,eAAL;EAAnE,SAAb;;EAEA,YAAI7C,YAAJ,EAAkB;EAChBoD,UAAAA,IAAI,CAACI,aAAL,GAAqBxD,YAArB;EACD;;EACD,YAAI6B,KAAJ,EAAW;EACTuB,UAAAA,IAAI,CAACvB,KAAL,GAAaA,KAAK,CAACQ,IAAN,CAAW,GAAX,CAAb;EACD;;EAEDK,QAAAA,YAAY,CAACU,IAAb,GAAoBA,IAApB;EACD,OAXD,MAWO;EACLD,QAAAA,UAAU,sDAA+C,KAAKN,eAAL,EAA/C,CAAV;EACAM,QAAAA,UAAU,yBAAkBpD,QAAlB,CAAV;;EACA,YAAIC,YAAJ,EAAkB;EAChBmD,UAAAA,UAAU,6BAAsBnD,YAAtB,CAAV;EACD;;EACD,YAAI6B,KAAJ,EAAW;EACTsB,UAAAA,UAAU,qBAActB,KAAK,CAACQ,IAAN,CAAW,GAAX,CAAd,CAAV;EACD;EACF;;EAED,aAAO,KAAKvD,KAAL,CAAWqE,UAAX,EAAuBT,YAAvB,EACJ3E,IADI,CACC,UAACF,GAAD;EAAA,eAASM,aAAa,CAACN,GAAD,CAAtB;EAAA,OADD,EAEJE,IAFI,CAEC,UAACF,GAAD,EAAS;EACb,QAAA,MAAI,CAAC4F,cAAL,CAAoB5F,GAAG,CAACF,MAAJ,CAAW+F,YAA/B;;EACA,QAAA,MAAI,CAACC,uBAAL,CAA6BrH,qBAAqB,CAACuB,GAAG,CAACF,MAAJ,CAAWiG,UAAZ,CAAlD;EACD,OALI,CAAP;EAMD;;;;;;EClYH,IAAMC,GAAG,GAAG,OAAOjD,IAAP,KAAgB,WAAhB,GACR,UAACkD,GAAD;EAAA,SAASC,MAAM,CAACC,IAAP,CAAYF,GAAZ,EAAiBtI,QAAjB,CAA0B,QAA1B,CAAT;EAAA,CADQ,GAERoF,IAFJ;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;MACqBqD;EACnB,mBAAY1E,OAAZ,EAAqB;EAAA;;EACnBA,IAAAA,OAAO,GAAGA,OAAO,IAAI,EAArB;;EAEA,QAAIA,OAAO,CAAC2E,IAAZ,EAAkB;EAChB,WAAKA,IAAL,GAAY3E,OAAO,CAAC2E,IAApB;EACD,KAFD,MAEO;EACL,WAAKA,IAAL,GAAY,IAAI5E,WAAJ,CAAgBC,OAAhB,CAAZ;EACD;;EAED,SAAKT,KAAL,GAAaS,OAAO,CAACT,KAAR,IAAiB,KAAKoF,IAAL,CAAUpF,KAAxC;EACA,SAAKqF,UAAL,GAAkB5E,OAAO,CAAC4E,UAA1B;EACA,SAAKC,WAAL,GAAmB7E,OAAO,CAAC6E,WAA3B;EACA,SAAKC,QAAL,GAAgB9E,OAAO,CAAC8E,QAAxB;EAEA,SAAKzI,MAAL,GAAc2D,OAAO,CAAC3D,MAAR,IAAkB,KAAKsI,IAAL,CAAUtI,MAA1C;EACA,SAAKC,eAAL,GAAuB0D,OAAO,CAAC1D,eAAR,IAA2B,KAAKqI,IAAL,CAAUrI,eAA5D;EACA,SAAKoE,aAAL,GAAqBV,OAAO,CAACU,aAAR,IAAyB,KAAKiE,IAAL,CAAUjE,aAAxD;EAEAqE,IAAAA,MAAM,CAACC,MAAP,CAAc,IAAd,EAAoB3Z,MAApB;EACD;;;;aAED,iBAAQ6X,IAAR,EAAcvG,IAAd,EAAoBgI,IAApB,EAA0BM,IAA1B,EAAgCC,KAAhC,EAAuC;EACrC;EACA,cAAQA,KAAR;EACE,aAAK3a,GAAL;EACE,iBAAO,KAAK4a,UAAL,CAAgBjC,IAAhB,EAAsBvG,IAAtB,EAA4BgI,IAA5B,EAAkCM,IAAlC,CAAP;;EACF,aAAKxa,QAAL;EACE,iBAAO,KAAK2a,eAAL,CAAqBlC,IAArB,EAA2BvG,IAA3B,EAAiCgI,IAAjC,EAAuCM,IAAvC,CAAP;;EACF,aAAKza,MAAL;EACE,iBAAO,KAAK6a,aAAL,CAAmBnC,IAAnB,EAAyBvG,IAAzB,EAA+BgI,IAA/B,EAAqCM,IAArC,CAAP;;EACF;EACE,gBAAM,IAAI/G,KAAJ,kCAAoCgH,KAApC,EAAN;EARJ;EAUD;;;aAED,oBAAWhC,IAAX,EAAiBW,IAAjB,EAAuBc,IAAvB,EAA6BM,IAA7B,EAAmC;EAAA;;EACjC,aAAO,KAAKN,IAAL,CAAUW,0BAAV,GACJ9G,IADI,CACC,YAAM;EACV,YAAM2E,YAAY,GAAG;EACnBC,UAAAA,MAAM,EAAE,MADW;EAEnBS,UAAAA,IAAI,EAAGA,IAAD,GAASjH,IAAI,CAACC,SAAL,CAAegH,IAAf,CAAT,GAAgC,IAFnB;EAGnB9F,UAAAA,OAAO,EAAE;EAHU,SAArB;;EAMA,YAAI8F,IAAJ,EAAU;EACRV,UAAAA,YAAY,CAACpF,OAAb,CAAqB,cAArB,IAAuC,kBAAvC;EACD;;EAED,QAAA,KAAI,CAACwH,cAAL,CAAoBZ,IAApB,EAA0BxB,YAA1B;;EACA,QAAA,KAAI,CAACqC,gBAAL,CAAsBrC,YAAtB;;EAEA,eAAOA,YAAP;EACD,OAhBI,EAiBJ3E,IAjBI,CAiBC,UAAC2E,YAAD;EAAA,eAAkB,KAAI,CAAC5D,KAAL,CACtBpD,UAAU,CAAC8I,IAAD,EAAO,KAAI,CAAC5I,MAAZ,EAAoB,KAAI,CAACC,eAAzB,CAAV,GAAsD4G,IADhC,EAEtBC,YAFsB,CAAlB;EAAA,OAjBD,EAqBJ3E,IArBI,CAqBC,UAACF,GAAD;EAAA,eAASM,aAAa,CAACN,GAAD,CAAtB;EAAA,OArBD,CAAP;EAsBD;;;aAED,yBAAgB4E,IAAhB,EAAsBvG,IAAtB,EAA4BgI,IAA5B,EAAkCM,IAAlC,EAAwC;EAAA;;EACtC,aAAO,KAAKN,IAAL,CAAUW,0BAAV,GACJ9G,IADI,CACC,YAAM;EACV,YAAM2E,YAAY,GAAG;EACnBC,UAAAA,MAAM,EAAE,MADW;EAEnBrF,UAAAA,OAAO,EAAE;EACP,+BAAmBrB,kBAAkB,CAACC,IAAD;EAD9B;EAFU,SAArB;;EAOA,QAAA,MAAI,CAAC4I,cAAL,CAAoBZ,IAApB,EAA0BxB,YAA1B;;EACA,QAAA,MAAI,CAACqC,gBAAL,CAAsBrC,YAAtB;;EAEA,eAAOA,YAAP;EACD,OAbI,EAcJ3E,IAdI,CAcC,UAAC2E,YAAD;EAAA,eAAkB,MAAI,CAAC5D,KAAL,CACtBpD,UAAU,CAAC8I,IAAD,EAAO,MAAI,CAAC5I,MAAZ,EAAoB,MAAI,CAACC,eAAzB,CAAV,GAAsD4G,IADhC,EAEtBC,YAFsB,CAAlB;EAAA,OAdD,EAkBJ3E,IAlBI,CAkBC,UAACF,GAAD;EAAA,eAASS,qBAAqB,CAACT,GAAD,CAA9B;EAAA,OAlBD,CAAP;EAmBD;;;aAED,uBAAc4E,IAAd,EAAoBvG,IAApB,EAA0BgI,IAA1B,EAAgCM,IAAhC,EAAsC;EAAA;;EACpC,aAAO,KAAKN,IAAL,CAAUW,0BAAV,GACJ9G,IADI,CACC,YAAM;EAAA,YACFiH,QADE,GACW9I,IADX,CACF8I,QADE;EAEV,eAAO9I,IAAI,CAAC8I,QAAZ;EAEA,YAAMtC,YAAY,GAAG;EACnBU,UAAAA,IAAI,EAAE4B,QADa;EAEnBrC,UAAAA,MAAM,EAAE,MAFW;EAGnBrF,UAAAA,OAAO,EAAE;EACP,4BAAgB,0BADT;EAEP,+BAAmBrB,kBAAkB,CAACC,IAAD;EAF9B;EAHU,SAArB;;EASA,QAAA,MAAI,CAAC4I,cAAL,CAAoBZ,IAApB,EAA0BxB,YAA1B;;EACA,QAAA,MAAI,CAACqC,gBAAL,CAAsBrC,YAAtB;;EAEA,eAAOA,YAAP;EACD,OAlBI,EAmBJ3E,IAnBI,CAmBC,UAAC2E,YAAD;EAAA,eAAkB,MAAI,CAAC5D,KAAL,CACtBpD,UAAU,CAAC8I,IAAD,EAAO,MAAI,CAAC5I,MAAZ,EAAoB,MAAI,CAACC,eAAzB,CAAV,GAAsD4G,IADhC,EAEtBC,YAFsB,CAAlB;EAAA,OAnBD,EAuBJ3E,IAvBI,CAuBC,UAACF,GAAD;EAAA,eAASM,aAAa,CAACN,GAAD,CAAtB;EAAA,OAvBD,CAAP;EAwBD;;;aAED,wBAAeqG,IAAf,EAAqBxB,YAArB,EAAmC;EACjC;EACA,UAAIwB,IAAI,CAACe,KAAL,CAAW,GAAX,EAAgBC,MAAhB,GAAyB,CAA7B,EAAgC;EAC9B,YAAMC,SAAS,GAAGjB,IAAI,CAAC7H,OAAL,CAAa,GAAb,EAAkB,EAAlB,EAAsB4I,KAAtB,CAA4B,GAA5B,CAAlB;;EACA,YAAIE,SAAS,CAACjD,QAAV,CAAmBhY,SAAnB,KAAiC,KAAKga,IAAL,CAAUjB,cAAV,EAArC,EAAiE;EAC/DiB,UAAAA,IAAI,GAAGha,SAAP;EACD,SAFD,MAEO,IAAIib,SAAS,CAACjD,QAAV,CAAmB/X,SAAnB,KAAiC,KAAK+Z,IAAL,CAAUjB,cAAV,EAArC,EAAiE;EACtEiB,UAAAA,IAAI,GAAG/Z,SAAP;EACD,SAFM,MAEA,IAAIgb,SAAS,CAACjD,QAAV,CAAmBjY,QAAnB,CAAJ,EAAkC;EACvCia,UAAAA,IAAI,GAAGja,QAAP;EACD;EACF;;EAED,cAAQia,IAAR;EACE,aAAKja,QAAL;EACE,cAAI,KAAKia,IAAL,CAAUnE,QAAV,IAAsB,KAAKmE,IAAL,CAAUlE,YAApC,EAAkD;EAChD,gBAAMoF,UAAU,GAAGvB,GAAG,WAAI,KAAKK,IAAL,CAAUnE,QAAd,cAA0B,KAAKmE,IAAL,CAAUlE,YAApC,EAAtB;EACA0C,YAAAA,YAAY,CAACpF,OAAb,CAAqB+H,aAArB,mBAA8CD,UAA9C;EACD;;EACD;;EACF,aAAKjb,SAAL;EACA,aAAKD,SAAL;EACE,cAAI,KAAKga,IAAL,CAAUjB,cAAV,EAAJ,EAAgC;EAC9BP,YAAAA,YAAY,CAACpF,OAAb,CAAqB+H,aAArB,oBAA+C,KAAKnB,IAAL,CAAUjB,cAAV,EAA/C;EACD;;EACD;;EACF,aAAK7Y,OAAL;EACA,aAAKC,MAAL;EACE;;EACF;EACE,gBAAM,IAAIoT,KAAJ,gCAAkCyG,IAAlC,EAAN;EAjBJ;EAmBD;;;aAED,0BAAiB3E,OAAjB,EAA0B;EAAA;;EACxB,UAAI,KAAK4E,UAAT,EAAqB;EACnB5E,QAAAA,OAAO,CAACjC,OAAR,CAAgB,yBAAhB,IAA6C,KAAK6G,UAAlD;EACD;;EACD,UAAI,KAAKC,WAAT,EAAsB;EACpB7E,QAAAA,OAAO,CAACjC,OAAR,CAAgB,0BAAhB,IAA8C,KAAK8G,WAAnD;EACD;;EACD,UAAI,KAAKC,QAAT,EAAmB;EACjB9E,QAAAA,OAAO,CAACjC,OAAR,CAAgB,uBAAhB,IAA2C,KAAK+G,QAAhD;EACD;;EACD,UAAI,KAAKpE,aAAT,EAAwB;EACtB,YAAMqF,UAAU,GAAGhB,MAAM,CAACiB,IAAP,CAAY,KAAKtF,aAAjB,CAAnB;EACAqF,QAAAA,UAAU,CAACE,OAAX,CAAmB,UAACC,MAAD,EAAY;EAC7BlG,UAAAA,OAAO,CAACjC,OAAR,CAAgBmI,MAAhB,IAA0B,MAAI,CAACxF,aAAL,CAAmBwF,MAAnB,CAA1B;EACD,SAFD;EAGD;EACF;;;;;;;;;;;;;;;;;"}