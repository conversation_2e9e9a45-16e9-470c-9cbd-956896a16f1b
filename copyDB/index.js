const mdb = require("../mdb.js");

mdb.connect().then(async ()=>{ 

   // COPY FROM PROD TO STAGING ---------------------------------------------------------

   let collections=["accounts"]; //"orgs", "items", "orders" //"formflows","formflow_submissions","rebate_submissions","rebates"

   for(let collectionName of collections){
      let records = await mdb.client().prod.collection(collectionName).find().toArray(); 
      
      console.log("Copying "+collectionName);

      //Clear the collection in STAGING
      await mdb.client().staging.collection(collectionName).deleteMany(); 

      //Loop through the PROD records and insert them into STAGING
      for(let r of records){ 
         await mdb.client().staging.collection(collectionName).insertOne(r); 
      }
      
      console.log("Copied "+collectionName);
   }

   console.log("Complete")
});