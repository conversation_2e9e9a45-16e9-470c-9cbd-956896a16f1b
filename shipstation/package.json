{"name": "bmghub-shipstation-library", "version": "1.0.0", "description": "A comprehensive JavaScript library for interacting with the ShipStation API", "main": "index.js", "scripts": {"test": "node test.js", "examples": "node examples.js"}, "keywords": ["shipstation", "shipping", "api", "ecommerce", "logistics", "labels", "tracking"], "author": "BMG Hub", "license": "MIT", "dependencies": {"shipstation-node": "^1.5.6"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "local"}, "bugs": {"url": "local"}, "homepage": "local"}