const https = require('https');
const querystring = require('querystring');

/**
 * Vanilla JavaScript ShipStation API Library
 * Supports both v1 and v2 APIs without external dependencies
 */
class ShipStationLibrary {
  constructor(config = {}) {
    // Get credentials from environment variables or config
    this.apiKey = config.apiKey || process.env.SHIPSTATION_API_KEY;
    this.apiSecret = config.apiSecret || process.env.SHIPSTATION_API_SECRET;
    this.v2ApiKey = config.v2ApiKey || process.env.SHIPSTATION_V2_API_KEY || this.apiKey;

    if (!this.apiKey || !this.apiSecret) {
      throw new Error('ShipStation API credentials are required. Set SHIPSTATION_API_KEY and SHIPSTATION_API_SECRET environment variables or pass them in config.');
    }

    // API configuration
    this.timeout = config.timeout || 30000;
    this.retries = config.retries || 3;
    this.retryDelay = config.retryDelay || 1000;
    this.mock = config.mock || false;

    // V1 API configuration
    this.v1BaseUrl = 'ssapi.shipstation.com';
    this.v1AuthHeader = 'Basic ' + Buffer.from(`${this.apiKey}:${this.apiSecret}`).toString('base64');
    this.v1RateLimitMax = 40; // 40 requests per minute
    this.v1RateLimitInterval = 60000; // 1 minute

    // V2 API configuration
    this.v2BaseUrl = this.mock ? 'docs.shipstation.com' : 'api.shipstation.com';
    this.v2AuthHeader = `Bearer ${this.v2ApiKey}`;
    this.v2RateLimitMax = 300; // 300 requests per minute (estimated)
    this.v2RateLimitInterval = 60000; // 1 minute

    // Rate limiting tracking for both APIs
    this.v1RateLimit = {
      current: 0,
      reset: Date.now() + this.v1RateLimitInterval
    };
    this.v2RateLimit = {
      current: 0,
      reset: Date.now() + this.v2RateLimitInterval
    };
  }

  /**
   * Make HTTP request to ShipStation API
   * @param {string} method - HTTP method
   * @param {string} path - API endpoint path
   * @param {Object} data - Request data
   * @param {Object} params - Query parameters
   * @param {string} version - API version ('v1' or 'v2')
   * @returns {Promise<Object>} API response
   */
  async makeRequest(method, path, data = null, params = null, version = 'v1') {
    // Handle rate limiting for the specific API version
    await this._handleRateLimit(version);

    // Configure request based on API version
    const isV2 = version === 'v2';
    const baseUrl = isV2 ? this.v2BaseUrl : this.v1BaseUrl;
    const authHeader = isV2 ? this.v2AuthHeader : this.v1AuthHeader;

    // Build path with version prefix for v2
    const fullPath = isV2
      ? (this.mock ? '/_mock/openapi/v2' + path : '/v2' + path)
      : path;

    const options = {
      hostname: baseUrl,
      path: fullPath + (params ? '?' + querystring.stringify(params) : ''),
      method: method.toUpperCase(),
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
        'User-Agent': 'BMGHub-ShipStation-Library/1.0.0'
      },
      timeout: this.timeout
    };

    // V2 API uses different header name for API key
    if (isV2 && !authHeader.startsWith('Bearer')) {
      options.headers['API-Key'] = this.v2ApiKey;
      delete options.headers['Authorization'];
    }

    // Add content length for POST/PUT requests
    if (data && (method === 'POST' || method === 'PUT')) {
      const jsonData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(jsonData);
    }

    return new Promise((resolve, reject) => {
      const req = https.request(options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          try {
            // Update rate limit tracking
            this._updateRateLimit(res.headers, version);

            // Parse response
            const parsedData = responseData ? JSON.parse(responseData) : {};

            if (res.statusCode >= 200 && res.statusCode < 300) {
              resolve(parsedData);
            } else {
              const error = new Error(`ShipStation API ${version.toUpperCase()} Error: ${res.statusCode} ${res.statusMessage}`);
              error.status = res.statusCode;
              error.statusText = res.statusMessage;
              error.responseData = parsedData;
              error.version = version;
              reject(error);
            }
          } catch (parseError) {
            reject(new Error(`Failed to parse response: ${parseError.message}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(new Error(`Request failed: ${error.message}`));
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      // Write data for POST/PUT requests
      if (data && (method === 'POST' || method === 'PUT')) {
        req.write(JSON.stringify(data));
      }

      req.end();
    });
  }

  /**
   * Handle rate limiting for specific API version
   * @param {string} version - API version ('v1' or 'v2')
   */
  async _handleRateLimit(version = 'v1') {
    const now = Date.now();
    const isV2 = version === 'v2';
    const rateLimit = isV2 ? this.v2RateLimit : this.v1RateLimit;
    const rateLimitMax = isV2 ? this.v2RateLimitMax : this.v1RateLimitMax;
    const rateLimitInterval = isV2 ? this.v2RateLimitInterval : this.v1RateLimitInterval;

    // Reset rate limit counter if interval has passed
    if (now >= rateLimit.reset) {
      rateLimit.current = 0;
      rateLimit.reset = now + rateLimitInterval;
    }

    // If we've hit the rate limit, wait
    if (rateLimit.current >= rateLimitMax) {
      const waitTime = rateLimit.reset - now;
      await this._sleep(waitTime);
      rateLimit.current = 0;
      rateLimit.reset = Date.now() + rateLimitInterval;
    }

    rateLimit.current++;
  }

  /**
   * Update rate limit tracking from response headers
   * @param {Object} headers - Response headers
   * @param {string} version - API version ('v1' or 'v2')
   */
  _updateRateLimit(headers, version = 'v1') {
    const isV2 = version === 'v2';
    const rateLimit = isV2 ? this.v2RateLimit : this.v1RateLimit;
    const rateLimitMax = isV2 ? this.v2RateLimitMax : this.v1RateLimitMax;

    if (headers['x-rate-limit-remaining']) {
      rateLimit.current = rateLimitMax - parseInt(headers['x-rate-limit-remaining']);
    }
    if (headers['x-rate-limit-reset']) {
      rateLimit.reset = parseInt(headers['x-rate-limit-reset']) * 1000;
    }
  }

  /**
   * Sleep utility function
   */
  _sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Retry wrapper for API calls
   */
  async _withRetry(fn, retries = this.retries) {
    try {
      return await fn();
    } catch (error) {
      if (retries > 0 && this._isRetryableError(error)) {
        await this._sleep(this.retryDelay);
        return this._withRetry(fn, retries - 1);
      }
      throw error;
    }
  }

  /**
   * Check if error is retryable
   */
  _isRetryableError(error) {
    return error.status >= 500 || error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT';
  }

  /**
   * Orders API Methods
   */

  /**
   * Get orders with filtering options (V1 API)
   * @param {Object} options - Filter options
   * @returns {Promise<Object>} Orders response
   */
  async getOrders(options = {}) {
    const params = {
      pageSize: 100,
      page: 1,
      sortBy: 'ModifyDate',
      sortDir: 'DESC',
      ...options
    };

    return this._withRetry(() => this.makeRequest('GET', '/orders', null, params, 'v1'));
  }

  /**
   * Get orders by status
   * @param {string} status - Order status
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Orders response
   */
  async getOrdersByStatus(status, options = {}) {
    return this.getOrders({ ...options, orderStatus: status });
  }

  /**
   * Get orders awaiting shipment
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Orders response
   */
  async getOrdersAwaitingShipment(options = {}) {
    return this.getOrdersByStatus('awaiting_shipment', options);
  }

  /**
   * Get order by ID (V1 API)
   * @param {number} orderId - Order ID
   * @returns {Promise<Object>} Order details
   */
  async getOrder(orderId) {
    return this._withRetry(() => this.makeRequest('GET', `/orders/${orderId}`, null, null, 'v1'));
  }

  /**
   * Create a new order (V1 API)
   * @param {Object} orderData - Order data
   * @returns {Promise<Object>} Created order
   */
  async createOrder(orderData) {
    return this._withRetry(() => this.makeRequest('POST', '/orders/createorder', orderData, null, 'v1'));
  }

  /**
   * Update an existing order (V1 API)
   * @param {number} orderId - Order ID
   * @param {Object} orderData - Updated order data
   * @returns {Promise<Object>} Updated order
   */
  async updateOrder(orderId, orderData) {
    return this._withRetry(() => this.makeRequest('POST', `/orders/createorder`, { ...orderData, orderId }, null, 'v1'));
  }

  /**
   * Delete an order (V1 API)
   * @param {number} orderId - Order ID
   * @returns {Promise<Object>} Deletion result
   */
  async deleteOrder(orderId) {
    return this._withRetry(() => this.makeRequest('DELETE', `/orders/${orderId}`, null, null, 'v1'));
  }

  /**
   * Search orders by customer email
   * @param {string} email - Customer email
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Orders response
   */
  async searchOrdersByEmail(email, options = {}) {
    return this.getOrders({ ...options, customerEmail: email });
  }

  /**
   * Search orders by order number
   * @param {string} orderNumber - Order number
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Orders response
   */
  async searchOrdersByNumber(orderNumber, options = {}) {
    return this.getOrders({ ...options, orderNumber: orderNumber });
  }

  /**
   * Get orders by date range
   * @param {string} startDate - Start date (YYYY-MM-DD)
   * @param {string} endDate - End date (YYYY-MM-DD)
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Orders response
   */
  async getOrdersByDateRange(startDate, endDate, options = {}) {
    return this.getOrders({
      ...options,
      createDateStart: startDate,
      createDateEnd: endDate
    });
  }

  /**
   * Shipments API Methods
   */

  /**
   * Get shipments with filtering options
   * @param {Object} options - Filter options
   * @returns {Promise<Object>} Shipments response
   */
  async getShipments(options = {}) {
    const params = {
      pageSize: 100,
      page: 1,
      sortBy: 'ShipDate',
      sortDir: 'DESC',
      ...options
    };

    return this._withRetry(() => this.makeRequest('GET', '/shipments', null, params));
  }

  /**
   * Get shipment by ID
   * @param {number} shipmentId - Shipment ID
   * @returns {Promise<Object>} Shipment details
   */
  async getShipment(shipmentId) {
    return this._withRetry(() => this.makeRequest('GET', `/shipments/${shipmentId}`));
  }

  /**
   * Create a shipment/label
   * @param {Object} shipmentData - Shipment data
   * @returns {Promise<Object>} Created shipment with label
   */
  async createShipment(shipmentData) {
    return this._withRetry(() => this.makeRequest('POST', '/shipments/createlabel', shipmentData));
  }

  /**
   * Void a shipment label
   * @param {number} shipmentId - Shipment ID
   * @returns {Promise<Object>} Void result
   */
  async voidShipment(shipmentId) {
    return this._withRetry(() => this.makeRequest('POST', '/shipments/voidlabel', { shipmentId }));
  }

  /**
   * Get shipments by date range
   * @param {string} startDate - Start date (YYYY-MM-DD)
   * @param {string} endDate - End date (YYYY-MM-DD)
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Shipments response
   */
  async getShipmentsByDateRange(startDate, endDate, options = {}) {
    return this.getShipments({
      ...options,
      shipDateStart: startDate,
      shipDateEnd: endDate
    });
  }

  /**
   * Get shipping rates
   * @param {Object} rateData - Rate request data
   * @returns {Promise<Array>} Array of rates
   */
  async getRates(rateData) {
    return this._withRetry(() => this.makeRequest('POST', '/shipments/getrates', rateData));
  }

  /**
   * Carriers API Methods
   */

  /**
   * Get all carriers
   * @returns {Promise<Array>} Array of carriers
   */
  async getCarriers() {
    return this._withRetry(() => this.makeRequest('GET', '/carriers'));
  }

  /**
   * Get carrier by code
   * @param {string} carrierCode - Carrier code
   * @returns {Promise<Object>} Carrier details
   */
  async getCarrier(carrierCode) {
    return this._withRetry(() => this.makeRequest('GET', `/carriers/getcarrier?carrierCode=${carrierCode}`));
  }

  /**
   * Add funds to carrier account
   * @param {string} carrierCode - Carrier code
   * @param {number} amount - Amount to add
   * @returns {Promise<Object>} Result
   */
  async addFundsToCarrier(carrierCode, amount) {
    return this._withRetry(() => this.makeRequest('POST', '/carriers/addfunds', { carrierCode, amount }));
  }

  /**
   * Get carrier packages
   * @param {string} carrierCode - Carrier code
   * @returns {Promise<Array>} Array of packages
   */
  async getCarrierPackages(carrierCode) {
    return this._withRetry(() => this.makeRequest('GET', `/carriers/listpackages?carrierCode=${carrierCode}`));
  }

  /**
   * Get carrier services
   * @param {string} carrierCode - Carrier code
   * @returns {Promise<Array>} Array of services
   */
  async getCarrierServices(carrierCode) {
    return this._withRetry(() => this.makeRequest('GET', `/carriers/listservices?carrierCode=${carrierCode}`));
  }

  /**
   * Stores API Methods
   */

  /**
   * Get all stores
   * @returns {Promise<Array>} Array of stores
   */
  async getStores() {
    return this._withRetry(() => this.makeRequest('GET', '/stores'));
  }

  /**
   * Get store by ID
   * @param {number} storeId - Store ID
   * @returns {Promise<Object>} Store details
   */
  async getStore(storeId) {
    return this._withRetry(() => this.makeRequest('GET', `/stores/${storeId}`));
  }

  /**
   * Update store
   * @param {number} storeId - Store ID
   * @param {Object} storeData - Updated store data
   * @returns {Promise<Object>} Updated store
   */
  async updateStore(storeId, storeData) {
    return this._withRetry(() => this.makeRequest('POST', `/stores/${storeId}`, storeData));
  }

  /**
   * Deactivate store
   * @param {number} storeId - Store ID
   * @returns {Promise<Object>} Result
   */
  async deactivateStore(storeId) {
    return this._withRetry(() => this.makeRequest('POST', `/stores/deactivate`, { storeId }));
  }

  /**
   * Reactivate store
   * @param {number} storeId - Store ID
   * @returns {Promise<Object>} Result
   */
  async reactivateStore(storeId) {
    return this._withRetry(() => this.makeRequest('POST', `/stores/reactivate`, { storeId }));
  }

  /**
   * Get store refresh status
   * @param {number} storeId - Store ID
   * @returns {Promise<Object>} Refresh status
   */
  async getStoreRefreshStatus(storeId) {
    return this._withRetry(() => this.makeRequest('GET', `/stores/getrefreshstatus?storeId=${storeId}`));
  }

  /**
   * Refresh store
   * @param {number} storeId - Store ID
   * @returns {Promise<Object>} Result
   */
  async refreshStore(storeId) {
    return this._withRetry(() => this.makeRequest('POST', `/stores/refreshstore`, { storeId }));
  }

  /**
   * Products API Methods
   */

  /**
   * Get products with filtering options
   * @param {Object} options - Filter options
   * @returns {Promise<Object>} Products response
   */
  async getProducts(options = {}) {
    const params = {
      pageSize: 100,
      page: 1,
      sortBy: 'ModifyDate',
      sortDir: 'DESC',
      ...options
    };

    return this._withRetry(() => this.makeRequest('GET', '/products', null, params));
  }

  /**
   * Get product by ID
   * @param {number} productId - Product ID
   * @returns {Promise<Object>} Product details
   */
  async getProduct(productId) {
    return this._withRetry(() => this.makeRequest('GET', `/products/${productId}`));
  }

  /**
   * Create product
   * @param {Object} productData - Product data
   * @returns {Promise<Object>} Created product
   */
  async createProduct(productData) {
    return this._withRetry(() => this.makeRequest('POST', '/products', productData));
  }

  /**
   * Update product
   * @param {number} productId - Product ID
   * @param {Object} productData - Updated product data
   * @returns {Promise<Object>} Updated product
   */
  async updateProduct(productId, productData) {
    return this._withRetry(() => this.makeRequest('PUT', `/products/${productId}`, productData));
  }

  /**
   * Warehouses API Methods
   */

  /**
   * Get all warehouses
   * @returns {Promise<Array>} Array of warehouses
   */
  async getWarehouses() {
    return this._withRetry(() => this.makeRequest('GET', '/warehouses'));
  }

  /**
   * Get warehouse by ID
   * @param {number} warehouseId - Warehouse ID
   * @returns {Promise<Object>} Warehouse details
   */
  async getWarehouse(warehouseId) {
    return this._withRetry(() => this.makeRequest('GET', `/warehouses/${warehouseId}`));
  }

  /**
   * Create warehouse
   * @param {Object} warehouseData - Warehouse data
   * @returns {Promise<Object>} Created warehouse
   */
  async createWarehouse(warehouseData) {
    return this._withRetry(() => this.makeRequest('POST', '/warehouses/createwarehouse', warehouseData));
  }

  /**
   * Update warehouse
   * @param {number} warehouseId - Warehouse ID
   * @param {Object} warehouseData - Updated warehouse data
   * @returns {Promise<Object>} Updated warehouse
   */
  async updateWarehouse(warehouseId, warehouseData) {
    return this._withRetry(() => this.makeRequest('PUT', `/warehouses/${warehouseId}`, warehouseData));
  }

  /**
   * Users API Methods
   */

  /**
   * Get all users
   * @returns {Promise<Array>} Array of users
   */
  async getUsers() {
    return this._withRetry(() => this.makeRequest('GET', '/users'));
  }

  /**
   * Accounts API Methods
   */

  /**
   * Get account tags
   * @returns {Promise<Array>} Array of tags
   */
  async getTags() {
    return this._withRetry(() => this.makeRequest('GET', '/accounts/listtags'));
  }

  /**
   * Register account (Partner API only)
   * @param {Object} accountData - Account data
   * @returns {Promise<Object>} Registration result
   */
  async registerAccount(accountData) {
    return this._withRetry(() => this.makeRequest('POST', '/accounts/registeraccount', accountData));
  }

  /**
   * Webhooks API Methods
   */

  /**
   * Get all webhooks
   * @returns {Promise<Array>} Array of webhooks
   */
  async getWebhooks() {
    return this._withRetry(() => this.makeRequest('GET', '/webhooks'));
  }

  /**
   * Subscribe to webhook
   * @param {Object} webhookData - Webhook subscription data
   * @returns {Promise<Object>} Webhook subscription result
   */
  async createWebhook(webhookData) {
    return this._withRetry(() => this.makeRequest('POST', '/webhooks/subscribe', webhookData));
  }

  /**
   * Unsubscribe from webhook
   * @param {number} webhookId - Webhook ID
   * @returns {Promise<Object>} Unsubscribe result
   */
  async deleteWebhook(webhookId) {
    return this._withRetry(() => this.makeRequest('DELETE', `/webhooks/${webhookId}`));
  }

  /**
   * Fulfillments API Methods
   */

  /**
   * Get fulfillments
   * @param {Object} options - Filter options
   * @returns {Promise<Object>} Fulfillments response
   */
  async getFulfillments(options = {}) {
    const params = {
      pageSize: 100,
      page: 1,
      sortBy: 'CreateDate',
      sortDir: 'DESC',
      ...options
    };

    return this._withRetry(() => this.makeRequest('GET', '/fulfillments', null, params));
  }

  /**
   * Get fulfillment by ID
   * @param {number} fulfillmentId - Fulfillment ID
   * @returns {Promise<Object>} Fulfillment details
   */
  async getFulfillment(fulfillmentId) {
    return this._withRetry(() => this.makeRequest('GET', `/fulfillments/${fulfillmentId}`));
  }

  /**
   * Utility Methods
   */

  /**
   * Get order status counts
   * @returns {Promise<Object>} Object with status counts
   */
  async getOrderStatusCounts() {
    const statuses = [
      'awaiting_payment',
      'awaiting_shipment',
      'shipped',
      'on_hold',
      'cancelled'
    ];

    const counts = {};
    for (const status of statuses) {
      try {
        const result = await this.getOrdersByStatus(status, { pageSize: 1 });
        counts[status] = result.total || 0;
      } catch (error) {
        counts[status] = 0;
      }
    }

    return counts;
  }

  /**
   * Bulk create orders
   * @param {Array} ordersData - Array of order data objects
   * @returns {Promise<Array>} Array of results
   */
  async bulkCreateOrders(ordersData) {
    const results = [];
    for (const orderData of ordersData) {
      try {
        const result = await this.createOrder(orderData);
        results.push({ success: true, data: result });
      } catch (error) {
        results.push({
          success: false,
          error: error.message,
          data: orderData,
          status: error.status
        });
      }
    }
    return results;
  }

  /**
   * Test API connection
   * @returns {Promise<Object>} Connection test result
   */
  async testConnection() {
    try {
      const stores = await this.getStores();
      return {
        success: true,
        message: 'Connection successful',
        storeCount: stores.length
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        status: error.status
      };
    }
  }

  /**
   * Get API rate limit status for both versions
   * @param {string} version - API version ('v1', 'v2', or 'both')
   * @returns {Object} Rate limit status
   */
  getRateLimitStatus(version = 'both') {
    if (version === 'v1') {
      return {
        version: 'v1',
        current: this.v1RateLimit.current,
        max: this.v1RateLimitMax,
        resetTime: new Date(this.v1RateLimit.reset),
        remaining: this.v1RateLimitMax - this.v1RateLimit.current
      };
    }

    if (version === 'v2') {
      return {
        version: 'v2',
        current: this.v2RateLimit.current,
        max: this.v2RateLimitMax,
        resetTime: new Date(this.v2RateLimit.reset),
        remaining: this.v2RateLimitMax - this.v2RateLimit.current
      };
    }

    // Return both by default
    return {
      v1: {
        current: this.v1RateLimit.current,
        max: this.v1RateLimitMax,
        resetTime: new Date(this.v1RateLimit.reset),
        remaining: this.v1RateLimitMax - this.v1RateLimit.current
      },
      v2: {
        current: this.v2RateLimit.current,
        max: this.v2RateLimitMax,
        resetTime: new Date(this.v2RateLimit.reset),
        remaining: this.v2RateLimitMax - this.v2RateLimit.current
      }
    };
  }

  /**
   * V2 API Methods
   */

  /**
   * Get shipments using V2 API
   * @param {Object} options - Filter options
   * @returns {Promise<Object>} Shipments response
   */
  async getShipmentsV2(options = {}) {
    const params = {
      page: 1,
      pageSize: 100,
      ...options
    };

    return this._withRetry(() => this.makeRequest('GET', '/shipments', null, params, 'v2'));
  }

  /**
   * Create shipment using V2 API
   * @param {Object} shipmentData - Shipment data
   * @returns {Promise<Object>} Created shipment
   */
  async createShipmentV2(shipmentData) {
    return this._withRetry(() => this.makeRequest('POST', '/shipments', shipmentData, null, 'v2'));
  }

  /**
   * Get rates using V2 API
   * @param {Object} rateData - Rate request data
   * @returns {Promise<Object>} Rates response
   */
  async getRatesV2(rateData) {
    return this._withRetry(() => this.makeRequest('POST', '/rates', rateData, null, 'v2'));
  }

  /**
   * Get carriers using V2 API
   * @returns {Promise<Object>} Carriers response
   */
  async getCarriersV2() {
    return this._withRetry(() => this.makeRequest('GET', '/carriers', null, null, 'v2'));
  }

  /**
   * Get tags using V2 API
   * @returns {Promise<Object>} Tags response
   */
  async getTagsV2() {
    return this._withRetry(() => this.makeRequest('GET', '/tags', null, null, 'v2'));
  }

  /**
   * Get warehouses using V2 API
   * @returns {Promise<Object>} Warehouses response
   */
  async getWarehousesV2() {
    return this._withRetry(() => this.makeRequest('GET', '/warehouses', null, null, 'v2'));
  }

  /**
   * Create label using V2 API
   * @param {Object} labelData - Label data
   * @returns {Promise<Object>} Created label
   */
  async createLabelV2(labelData) {
    return this._withRetry(() => this.makeRequest('POST', '/labels', labelData, null, 'v2'));
  }

  /**
   * Get package types using V2 API
   * @returns {Promise<Object>} Package types response
   */
  async getPackageTypesV2() {
    return this._withRetry(() => this.makeRequest('GET', '/package-types', null, null, 'v2'));
  }

  /**
   * Track shipment using V2 API
   * @param {string} carrierCode - Carrier code
   * @param {string} trackingNumber - Tracking number
   * @returns {Promise<Object>} Tracking information
   */
  async trackShipmentV2(carrierCode, trackingNumber) {
    const params = {
      carrierCode,
      trackingNumber
    };
    return this._withRetry(() => this.makeRequest('GET', '/tracking', null, params, 'v2'));
  }

  /**
   * Get manifests using V2 API
   * @param {Object} options - Filter options
   * @returns {Promise<Object>} Manifests response
   */
  async getManifestsV2(options = {}) {
    const params = {
      page: 1,
      pageSize: 100,
      ...options
    };

    return this._withRetry(() => this.makeRequest('GET', '/manifests', null, params, 'v2'));
  }

  /**
   * Create manifest using V2 API
   * @param {Object} manifestData - Manifest data
   * @returns {Promise<Object>} Created manifest
   */
  async createManifestV2(manifestData) {
    return this._withRetry(() => this.makeRequest('POST', '/manifests', manifestData, null, 'v2'));
  }

  /**
   * Unified API Methods (automatically choose best API version)
   */

  /**
   * Get shipments using the best available API
   * @param {Object} options - Filter options
   * @param {string} preferredVersion - Preferred API version ('v1' or 'v2')
   * @returns {Promise<Object>} Shipments response
   */
  async getShipmentsUnified(options = {}, preferredVersion = 'v2') {
    if (preferredVersion === 'v2' && this.v2ApiKey) {
      return this.getShipmentsV2(options);
    }
    return this.getShipments(options);
  }

  /**
   * Get rates using the best available API
   * @param {Object} rateData - Rate request data
   * @param {string} preferredVersion - Preferred API version ('v1' or 'v2')
   * @returns {Promise<Object>} Rates response
   */
  async getRatesUnified(rateData, preferredVersion = 'v2') {
    if (preferredVersion === 'v2' && this.v2ApiKey) {
      return this.getRatesV2(rateData);
    }
    return this.getRates(rateData);
  }

  /**
   * Get carriers using the best available API
   * @param {string} preferredVersion - Preferred API version ('v1' or 'v2')
   * @returns {Promise<Object>} Carriers response
   */
  async getCarriersUnified(preferredVersion = 'v2') {
    if (preferredVersion === 'v2' && this.v2ApiKey) {
      return this.getCarriersV2();
    }
    return this.getCarriers();
  }

  /**
   * Test connection to both APIs
   * @returns {Promise<Object>} Connection test results
   */
  async testConnectionBoth() {
    const results = {
      v1: { success: false, message: '', error: null },
      v2: { success: false, message: '', error: null }
    };

    // Test V1 API
    try {
      const stores = await this.getStores();
      results.v1 = {
        success: true,
        message: 'V1 API connection successful',
        storeCount: stores.length
      };
    } catch (error) {
      results.v1 = {
        success: false,
        message: `V1 API connection failed: ${error.message}`,
        error: error.status
      };
    }

    // Test V2 API (if we have v2 credentials)
    if (this.v2ApiKey) {
      try {
        const carriers = await this.getCarriersV2();
        results.v2 = {
          success: true,
          message: 'V2 API connection successful',
          carrierCount: carriers.carriers?.length || 0
        };
      } catch (error) {
        results.v2 = {
          success: false,
          message: `V2 API connection failed: ${error.message}`,
          error: error.status
        };
      }
    } else {
      results.v2 = {
        success: false,
        message: 'V2 API key not provided',
        error: null
      };
    }

    return results;
  }

  /**
   * Get API version capabilities
   * @returns {Object} Available API versions and their capabilities
   */
  getApiCapabilities() {
    return {
      v1: {
        available: !!(this.apiKey && this.apiSecret),
        baseUrl: this.v1BaseUrl,
        rateLimit: `${this.v1RateLimitMax} requests per minute`,
        features: [
          'Orders management',
          'Shipments and labels',
          'Carriers and rates',
          'Stores management',
          'Products management',
          'Warehouses management',
          'Users management',
          'Webhooks',
          'Fulfillments'
        ]
      },
      v2: {
        available: !!this.v2ApiKey,
        baseUrl: this.v2BaseUrl,
        rateLimit: `${this.v2RateLimitMax} requests per minute`,
        mock: this.mock,
        features: [
          'Enhanced shipments',
          'Advanced rates',
          'Improved carriers',
          'Tags management',
          'Package types',
          'Tracking',
          'Manifests',
          'Better error handling'
        ]
      }
    };
  }

  /**
   * Switch between mock and live V2 API
   * @param {boolean} useMock - Whether to use mock API
   */
  setMockMode(useMock) {
    this.mock = useMock;
    this.v2BaseUrl = useMock ? 'docs.shipstation.com' : 'api.shipstation.com';
  }
}

// Export the class
module.exports = ShipStationLibrary;

// Export factory function
module.exports.create = (config) => new ShipStationLibrary(config);

// Export the class itself for destructuring
module.exports.ShipStationLibrary = ShipStationLibrary;