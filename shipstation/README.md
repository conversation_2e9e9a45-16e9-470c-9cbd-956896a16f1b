# ShipStation API Library

A comprehensive JavaScript library for interacting with the ShipStation API. This library wraps the `shipstation-node` package with additional convenience methods, better error handling, and a more intuitive interface.

## Features

- ✅ Full support for ShipStation v1 and v2 APIs
- ✅ Comprehensive error handling with detailed error messages
- ✅ Automatic retry logic with configurable options
- ✅ Rate limiting built-in
- ✅ TypeScript support (via underlying shipstation-node package)
- ✅ Convenience methods for common operations
- ✅ Bulk operations support
- ✅ Environment variable configuration
- ✅ Mock API support for testing

## Installation

This library uses the existing `shipstation-node` package that's already installed in your project.

## Configuration

### Environment Variables

Set these environment variables for automatic configuration:

```bash
SHIPSTATION_API_KEY=your_api_key
SHIPSTATION_API_SECRET=your_api_secret
SHIPSTATION_V2_API_KEY=your_v2_api_key  # Optional, defaults to API_KEY
SHIPSTATION_PARTNER_KEY=your_partner_key  # Optional
```

### Manual Configuration

```javascript
const ShipStationLibrary = require('./shipstation');

const shipstation = new ShipStationLibrary({
  apiKey: 'your-api-key',
  apiSecret: 'your-api-secret',
  v2ApiKey: 'your-v2-api-key', // Optional
  partnerKey: 'your-partner-key', // Optional
  mock: false, // Set to true for testing
  requestConfig: {
    timeout: 30000
  },
  retryConfig: {
    retries: 3,
    retryDelay: (retryCount) => retryCount * 1000
  }
});
```

## Usage Examples

### Basic Usage

```javascript
const ShipStationLibrary = require('./shipstation');

// Using environment variables
const shipstation = new ShipStationLibrary();

// Using factory function
const shipstation = ShipStationLibrary.create({
  apiKey: 'your-api-key',
  apiSecret: 'your-api-secret'
});
```

### Orders Management

```javascript
// Get all orders
const orders = await shipstation.getOrders({
  pageSize: 100,
  page: 1,
  sortBy: 'ModifyDate',
  sortDir: 'DESC'
});

// Get orders awaiting shipment
const awaitingShipment = await shipstation.getOrdersAwaitingShipment();

// Get orders by status
const shippedOrders = await shipstation.getOrdersByStatus('shipped');

// Get orders by date range
const recentOrders = await shipstation.getOrdersByDateRange('2024-01-01', '2024-01-31');

// Search orders by customer email
const customerOrders = await shipstation.searchOrdersByEmail('<EMAIL>');

// Create a new order
const newOrder = await shipstation.createOrder({
  orderNumber: 'ORDER-123',
  orderDate: new Date().toISOString(),
  orderStatus: 'awaiting_shipment',
  customerEmail: '<EMAIL>',
  billTo: {
    name: 'John Doe',
    street1: '123 Main St',
    city: 'Anytown',
    state: 'CA',
    postalCode: '12345',
    country: 'US'
  },
  shipTo: {
    name: 'John Doe',
    street1: '123 Main St',
    city: 'Anytown',
    state: 'CA',
    postalCode: '12345',
    country: 'US'
  },
  items: [{
    sku: 'PRODUCT-SKU',
    name: 'Product Name',
    quantity: 1,
    unitPrice: 29.99
  }]
});

// Update an order
const updatedOrder = await shipstation.updateOrder(orderId, {
  orderStatus: 'on_hold',
  internalNotes: 'Order on hold for review'
});
```

### Shipments Management

```javascript
// Get all shipments
const shipments = await shipstation.getShipments({
  pageSize: 50,
  page: 1
});

// Create a shipment/label
const shipment = await shipstation.createShipment({
  orderId: 12345,
  carrierCode: 'fedex',
  serviceCode: 'fedex_ground',
  packageCode: 'package',
  weight: {
    value: 1.5,
    units: 'pounds'
  }
});

// Void a shipment
const voidResult = await shipstation.voidShipment(shipmentId);
```

### Rates and Carriers

```javascript
// Get all carriers
const carriers = await shipstation.getCarriers();

// Get shipping rates
const rates = await shipstation.getRates({
  carrierCode: 'fedex',
  fromPostalCode: '12345',
  toPostalCode: '90210',
  toCountry: 'US',
  weight: {
    value: 1.5,
    units: 'pounds'
  }
});
```

### Bulk Operations

```javascript
// Bulk create orders
const ordersData = [
  { orderNumber: 'ORDER-1', /* ... */ },
  { orderNumber: 'ORDER-2', /* ... */ }
];

const results = await shipstation.bulkCreateOrders(ordersData);
// Returns array with success/failure status for each order
```

### Utility Methods

```javascript
// Get order status counts
const statusCounts = await shipstation.getOrderStatusCounts();
// Returns: { awaiting_payment: 5, awaiting_shipment: 10, shipped: 100, ... }

// Get orders by date range
const orders = await shipstation.getOrdersByDateRange('2024-01-01', '2024-01-31');

// Get shipments by date range
const shipments = await shipstation.getShipmentsByDateRange('2024-01-01', '2024-01-31');
```

## API Methods

### Orders
- `getOrders(options)` - Get orders with filtering
- `getOrdersByStatus(status, options)` - Get orders by status
- `getOrdersAwaitingShipment(options)` - Get orders awaiting shipment
- `getOrder(orderId)` - Get specific order
- `createOrder(orderData)` - Create new order
- `updateOrder(orderId, orderData)` - Update order
- `deleteOrder(orderId)` - Delete order
- `getOrdersByDateRange(startDate, endDate, options)` - Get orders by date range
- `searchOrdersByEmail(email, options)` - Search orders by customer email
- `searchOrdersByNumber(orderNumber, options)` - Search orders by order number
- `getOrderStatusCounts()` - Get count of orders by status
- `bulkCreateOrders(ordersData)` - Bulk create orders

### Shipments
- `getShipments(options)` - Get shipments with filtering
- `getShipment(shipmentId)` - Get specific shipment
- `createShipment(shipmentData)` - Create shipment/label
- `voidShipment(shipmentId)` - Void shipment
- `getShipmentsByDateRange(startDate, endDate, options)` - Get shipments by date range

### Carriers & Rates
- `getCarriers()` - Get all carriers
- `getRates(rateData)` - Get shipping rates
- `createLabel(labelData)` - Create shipping label

### Stores
- `getStores()` - Get all stores
- `getStore(storeId)` - Get specific store
- `updateStore(storeId, storeData)` - Update store

### Warehouses
- `getWarehouses()` - Get all warehouses
- `getWarehouse(warehouseId)` - Get specific warehouse
- `createWarehouse(warehouseData)` - Create warehouse

### Products
- `getProducts(options)` - Get products with filtering
- `getProduct(productId)` - Get specific product
- `createProduct(productData)` - Create product
- `updateProduct(productId, productData)` - Update product

### Tags
- `getTags()` - Get all tags

### Webhooks
- `getWebhooks()` - Get all webhooks
- `createWebhook(webhookData)` - Create webhook
- `deleteWebhook(webhookId)` - Delete webhook

### Utility
- `getClient()` - Get direct access to underlying ShipStation client

## Error Handling

The library provides enhanced error handling with detailed error messages:

```javascript
try {
  const orders = await shipstation.getOrders();
} catch (error) {
  console.error('Method:', error.method);
  console.error('Message:', error.message);
  console.error('Status:', error.status);
  console.error('Response:', error.responseData);
  console.error('Original Error:', error.originalError);
}
```

## Testing

Use the mock API for testing:

```javascript
const shipstation = new ShipStationLibrary({
  apiKey: 'test-key',
  apiSecret: 'test-secret',
  mock: true // Enables mock API
});
```

## Examples

See `examples.js` for comprehensive usage examples covering all functionality.

## License

This library is built on top of the `shipstation-node` package and follows the same licensing terms.
