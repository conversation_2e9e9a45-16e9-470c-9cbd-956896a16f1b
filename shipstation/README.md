# Vanilla JavaScript ShipStation API Library

A comprehensive vanilla JavaScript library for interacting with the ShipStation API. This library provides direct API access without external dependencies, using only Node.js built-in modules.

## Features

- ✅ **Zero Dependencies** - Uses only Node.js built-in modules (https, querystring)
- ✅ **Full ShipStation v1 API Support** - Complete coverage of all endpoints
- ✅ **Comprehensive Error Handling** - Detailed error messages with context
- ✅ **Automatic Retry Logic** - Configurable retry mechanism for failed requests
- ✅ **Built-in Rate Limiting** - Respects ShipStation's 40 requests/minute limit
- ✅ **Convenience Methods** - High-level methods for common operations
- ✅ **Bulk Operations** - Support for batch processing
- ✅ **Environment Variable Configuration** - Easy setup with env vars
- ✅ **Connection Testing** - Built-in API connection validation
- ✅ **Lightweight** - Minimal footprint, fast startup

## Installation

No installation required! This library uses only Node.js built-in modules.

## Configuration

### Environment Variables

Set these environment variables for automatic configuration:

```bash
SHIPSTATION_API_KEY=your_api_key
SHIPSTATION_API_SECRET=your_api_secret
```

### Manual Configuration

```javascript
const ShipStationLibrary = require('./shipstation');

const shipstation = new ShipStationLibrary({
  apiKey: 'your-api-key',
  apiSecret: 'your-api-secret',
  timeout: 30000,        // Request timeout in milliseconds
  retries: 3,            // Number of retry attempts
  retryDelay: 1000       // Delay between retries in milliseconds
});
```

## Usage Examples

### Basic Usage

```javascript
const ShipStationLibrary = require('./shipstation');

// Using environment variables
const shipstation = new ShipStationLibrary();

// Using factory function
const shipstation = ShipStationLibrary.create({
  apiKey: 'your-api-key',
  apiSecret: 'your-api-secret'
});
```

### Orders Management

```javascript
// Get all orders
const orders = await shipstation.getOrders({
  pageSize: 100,
  page: 1,
  sortBy: 'ModifyDate',
  sortDir: 'DESC'
});

// Get orders awaiting shipment
const awaitingShipment = await shipstation.getOrdersAwaitingShipment();

// Get orders by status
const shippedOrders = await shipstation.getOrdersByStatus('shipped');

// Get orders by date range
const recentOrders = await shipstation.getOrdersByDateRange('2024-01-01', '2024-01-31');

// Search orders by customer email
const customerOrders = await shipstation.searchOrdersByEmail('<EMAIL>');

// Create a new order
const newOrder = await shipstation.createOrder({
  orderNumber: 'ORDER-123',
  orderDate: new Date().toISOString(),
  orderStatus: 'awaiting_shipment',
  customerEmail: '<EMAIL>',
  billTo: {
    name: 'John Doe',
    street1: '123 Main St',
    city: 'Anytown',
    state: 'CA',
    postalCode: '12345',
    country: 'US'
  },
  shipTo: {
    name: 'John Doe',
    street1: '123 Main St',
    city: 'Anytown',
    state: 'CA',
    postalCode: '12345',
    country: 'US'
  },
  items: [{
    sku: 'PRODUCT-SKU',
    name: 'Product Name',
    quantity: 1,
    unitPrice: 29.99
  }]
});

// Update an order
const updatedOrder = await shipstation.updateOrder(orderId, {
  orderStatus: 'on_hold',
  internalNotes: 'Order on hold for review'
});
```

### Shipments Management

```javascript
// Get all shipments
const shipments = await shipstation.getShipments({
  pageSize: 50,
  page: 1
});

// Create a shipment/label
const shipment = await shipstation.createShipment({
  orderId: 12345,
  carrierCode: 'fedex',
  serviceCode: 'fedex_ground',
  packageCode: 'package',
  weight: {
    value: 1.5,
    units: 'pounds'
  }
});

// Void a shipment
const voidResult = await shipstation.voidShipment(shipmentId);
```

### Rates and Carriers

```javascript
// Get all carriers
const carriers = await shipstation.getCarriers();

// Get shipping rates
const rates = await shipstation.getRates({
  carrierCode: 'fedex',
  fromPostalCode: '12345',
  toPostalCode: '90210',
  toCountry: 'US',
  weight: {
    value: 1.5,
    units: 'pounds'
  }
});
```

### Bulk Operations

```javascript
// Bulk create orders
const ordersData = [
  { orderNumber: 'ORDER-1', /* ... */ },
  { orderNumber: 'ORDER-2', /* ... */ }
];

const results = await shipstation.bulkCreateOrders(ordersData);
// Returns array with success/failure status for each order
```

### Utility Methods

```javascript
// Get order status counts
const statusCounts = await shipstation.getOrderStatusCounts();
// Returns: { awaiting_payment: 5, awaiting_shipment: 10, shipped: 100, ... }

// Get orders by date range
const orders = await shipstation.getOrdersByDateRange('2024-01-01', '2024-01-31');

// Get shipments by date range
const shipments = await shipstation.getShipmentsByDateRange('2024-01-01', '2024-01-31');
```

## API Methods

### Orders
- `getOrders(options)` - Get orders with filtering
- `getOrdersByStatus(status, options)` - Get orders by status
- `getOrdersAwaitingShipment(options)` - Get orders awaiting shipment
- `getOrder(orderId)` - Get specific order
- `createOrder(orderData)` - Create new order
- `updateOrder(orderId, orderData)` - Update order
- `deleteOrder(orderId)` - Delete order
- `getOrdersByDateRange(startDate, endDate, options)` - Get orders by date range
- `searchOrdersByEmail(email, options)` - Search orders by customer email
- `searchOrdersByNumber(orderNumber, options)` - Search orders by order number
- `getOrderStatusCounts()` - Get count of orders by status
- `bulkCreateOrders(ordersData)` - Bulk create orders

### Shipments
- `getShipments(options)` - Get shipments with filtering
- `getShipment(shipmentId)` - Get specific shipment
- `createShipment(shipmentData)` - Create shipment/label
- `voidShipment(shipmentId)` - Void shipment
- `getShipmentsByDateRange(startDate, endDate, options)` - Get shipments by date range

### Carriers & Rates
- `getCarriers()` - Get all carriers
- `getRates(rateData)` - Get shipping rates
- `createLabel(labelData)` - Create shipping label

### Stores
- `getStores()` - Get all stores
- `getStore(storeId)` - Get specific store
- `updateStore(storeId, storeData)` - Update store

### Warehouses
- `getWarehouses()` - Get all warehouses
- `getWarehouse(warehouseId)` - Get specific warehouse
- `createWarehouse(warehouseData)` - Create warehouse

### Products
- `getProducts(options)` - Get products with filtering
- `getProduct(productId)` - Get specific product
- `createProduct(productData)` - Create product
- `updateProduct(productId, productData)` - Update product

### Tags
- `getTags()` - Get all tags

### Webhooks
- `getWebhooks()` - Get all webhooks
- `createWebhook(webhookData)` - Create webhook
- `deleteWebhook(webhookId)` - Delete webhook

### Utility
- `getClient()` - Get direct access to underlying ShipStation client

## Error Handling

The library provides enhanced error handling with detailed error messages:

```javascript
try {
  const orders = await shipstation.getOrders();
} catch (error) {
  console.error('Method:', error.method);
  console.error('Message:', error.message);
  console.error('Status:', error.status);
  console.error('Response:', error.responseData);
  console.error('Original Error:', error.originalError);
}
```

## Testing

Use the mock API for testing:

```javascript
const shipstation = new ShipStationLibrary({
  apiKey: 'test-key',
  apiSecret: 'test-secret',
  mock: true // Enables mock API
});
```

## Examples

See `examples.js` for comprehensive usage examples covering all functionality.

## License

This library is built on top of the `shipstation-node` package and follows the same licensing terms.
