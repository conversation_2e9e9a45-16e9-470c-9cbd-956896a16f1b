/**
 * ShipStation Library Usage Examples
 * Demonstrates both CommonJS (require) and ES6 (import) usage
 */

// ============================================================================
// COMMONJS (require) USAGE - Current Node.js default
// ============================================================================

// Method 1: Basic require
const ShipStationLibrary = require('./index');

// Method 2: Destructured require
const { ShipStationLibrary: ShipStation } = require('./index');

// Method 3: Factory function require
const { create } = require('./index');

// Initialize with environment variables
const shipstation1 = new ShipStationLibrary();

// Initialize with configuration
const shipstation2 = new ShipStationLibrary({
  apiKey: 'your-api-key',
  apiSecret: 'your-api-secret',
  timeout: 30000,
  retries: 3
});

// Initialize with factory function
const shipstation3 = create({
  apiKey: 'your-api-key',
  apiSecret: 'your-api-secret'
});

// ============================================================================
// ES6 MODULES (import) USAGE - Requires "type": "module" in package.json
// ============================================================================

/*
// To use import syntax, add this to your package.json:
{
  "type": "module"
}

// Then you can use:

// Method 1: Default import
import ShipStationLibrary from './index.js';

// Method 2: Named import
import { ShipStationLibrary } from './index.js';

// Method 3: Factory function import
import { create } from './index.js';

// Initialize the same way as CommonJS
const shipstation = new ShipStationLibrary({
  apiKey: 'your-api-key',
  apiSecret: 'your-api-secret'
});
*/

// ============================================================================
// BASIC USAGE EXAMPLES
// ============================================================================

async function basicUsageExamples() {
  try {
    // Test connection first
    console.log('Testing connection...');
    const connectionTest = await shipstation1.testConnection();
    console.log('Connection result:', connectionTest);

    if (!connectionTest.success) {
      console.log('Connection failed. Please check your API credentials.');
      return;
    }

    // Get orders
    console.log('\nGetting orders...');
    const orders = await shipstation1.getOrders({ pageSize: 5 });
    console.log(`Found ${orders.total} total orders, showing ${orders.orders?.length || 0}`);

    // Get orders awaiting shipment
    console.log('\nGetting orders awaiting shipment...');
    const awaitingShipment = await shipstation1.getOrdersAwaitingShipment({ pageSize: 5 });
    console.log(`Found ${awaitingShipment.total} orders awaiting shipment`);

    // Get carriers
    console.log('\nGetting carriers...');
    const carriers = await shipstation1.getCarriers();
    console.log(`Found ${carriers.length} carriers:`, carriers.map(c => c.name || c.carrierCode));

    // Get stores
    console.log('\nGetting stores...');
    const stores = await shipstation1.getStores();
    console.log(`Found ${stores.length} stores:`, stores.map(s => s.storeName));

    // Get rate limit status
    console.log('\nRate limit status:');
    const rateLimitStatus = shipstation1.getRateLimitStatus();
    console.log(rateLimitStatus);

  } catch (error) {
    console.error('Error in basic usage examples:', error.message);
  }
}

// ============================================================================
// ADVANCED USAGE EXAMPLES
// ============================================================================

async function advancedUsageExamples() {
  try {
    // Search orders by email
    const customerOrders = await shipstation1.searchOrdersByEmail('<EMAIL>');
    console.log('Customer orders:', customerOrders.total);

    // Get orders by date range
    const recentOrders = await shipstation1.getOrdersByDateRange('2024-01-01', '2024-01-31');
    console.log('Recent orders:', recentOrders.total);

    // Get order status counts
    const statusCounts = await shipstation1.getOrderStatusCounts();
    console.log('Order status counts:', statusCounts);

    // Create a test order (commented out to avoid creating real orders)
    /*
    const newOrder = await shipstation1.createOrder({
      orderNumber: 'TEST-' + Date.now(),
      orderDate: new Date().toISOString(),
      orderStatus: 'awaiting_shipment',
      customerEmail: '<EMAIL>',
      billTo: {
        name: 'Test Customer',
        street1: '123 Test St',
        city: 'Test City',
        state: 'CA',
        postalCode: '12345',
        country: 'US'
      },
      shipTo: {
        name: 'Test Customer',
        street1: '123 Test St',
        city: 'Test City',
        state: 'CA',
        postalCode: '12345',
        country: 'US'
      },
      items: [{
        sku: 'TEST-SKU',
        name: 'Test Product',
        quantity: 1,
        unitPrice: 10.00
      }]
    });
    console.log('Created order:', newOrder.orderId);
    */

  } catch (error) {
    console.error('Error in advanced usage examples:', error.message);
  }
}

// ============================================================================
// ERROR HANDLING EXAMPLES
// ============================================================================

async function errorHandlingExamples() {
  try {
    // Example of handling API errors
    const invalidShipStation = new ShipStationLibrary({
      apiKey: 'invalid-key',
      apiSecret: 'invalid-secret'
    });

    await invalidShipStation.getOrders();
  } catch (error) {
    console.log('Caught expected error:');
    console.log('- Message:', error.message);
    console.log('- Status:', error.status);
    console.log('- Response Data:', error.responseData);
  }
}

// ============================================================================
// BULK OPERATIONS EXAMPLES
// ============================================================================

async function bulkOperationsExamples() {
  try {
    // Example bulk order creation (commented out to avoid creating real orders)
    /*
    const ordersToCreate = [
      {
        orderNumber: 'BULK-1-' + Date.now(),
        orderDate: new Date().toISOString(),
        orderStatus: 'awaiting_shipment',
        customerEmail: '<EMAIL>',
        // ... other order data
      },
      {
        orderNumber: 'BULK-2-' + Date.now(),
        orderDate: new Date().toISOString(),
        orderStatus: 'awaiting_shipment',
        customerEmail: '<EMAIL>',
        // ... other order data
      }
    ];

    const bulkResults = await shipstation1.bulkCreateOrders(ordersToCreate);
    console.log('Bulk creation results:');
    bulkResults.forEach((result, index) => {
      if (result.success) {
        console.log(`Order ${index + 1}: Created successfully`);
      } else {
        console.log(`Order ${index + 1}: Failed - ${result.error}`);
      }
    });
    */

    console.log('Bulk operations example completed (commented out to avoid creating real orders)');
  } catch (error) {
    console.error('Error in bulk operations examples:', error.message);
  }
}

// ============================================================================
// RATE LIMITING EXAMPLES
// ============================================================================

async function rateLimitingExamples() {
  console.log('Rate limiting example:');
  
  // Make multiple requests to demonstrate rate limiting
  for (let i = 0; i < 5; i++) {
    console.log(`Request ${i + 1}:`);
    const start = Date.now();
    
    try {
      await shipstation1.getStores();
      const end = Date.now();
      const rateLimitStatus = shipstation1.getRateLimitStatus();
      
      console.log(`  - Completed in ${end - start}ms`);
      console.log(`  - Rate limit: ${rateLimitStatus.current}/${rateLimitStatus.max}`);
      console.log(`  - Remaining: ${rateLimitStatus.remaining}`);
    } catch (error) {
      console.log(`  - Error: ${error.message}`);
    }
  }
}

// ============================================================================
// RUN EXAMPLES
// ============================================================================

async function runAllExamples() {
  console.log('🚀 ShipStation Library Usage Examples\n');
  console.log('=' .repeat(60));

  // Only run if we have valid credentials
  if (!process.env.SHIPSTATION_API_KEY || !process.env.SHIPSTATION_API_SECRET) {
    console.log('⚠️  Please set SHIPSTATION_API_KEY and SHIPSTATION_API_SECRET environment variables to run examples');
    return;
  }

  console.log('\n📋 Basic Usage Examples:');
  await basicUsageExamples();

  console.log('\n🔧 Advanced Usage Examples:');
  await advancedUsageExamples();

  console.log('\n❌ Error Handling Examples:');
  await errorHandlingExamples();

  console.log('\n📦 Bulk Operations Examples:');
  await bulkOperationsExamples();

  console.log('\n⏱️  Rate Limiting Examples:');
  await rateLimitingExamples();

  console.log('\n' + '='.repeat(60));
  console.log('✅ All examples completed!');
}

// Export functions for use in other files
module.exports = {
  basicUsageExamples,
  advancedUsageExamples,
  errorHandlingExamples,
  bulkOperationsExamples,
  rateLimitingExamples,
  runAllExamples
};

// Run examples if this file is executed directly
if (require.main === module) {
  runAllExamples().catch(console.error);
}
