const ShipStation = require('shipstation-node');

/**
 * ShipStation API Library
 * A comprehensive wrapper for the ShipStation API with convenience methods
 */
class ShipStationLibrary {
  constructor(config = {}) {
    // Get credentials from environment variables or config
    const apiKey = config.apiKey || process.env.SHIPSTATION_API_KEY;
    const apiSecret = config.apiSecret || process.env.SHIPSTATION_API_SECRET;
    const v2ApiKey = config.v2ApiKey || process.env.SHIPSTATION_V2_API_KEY || apiKey;

    if (!apiKey || !apiSecret) {
      throw new Error('ShipStation API credentials are required. Set SHIPSTATION_API_KEY and SHIPSTATION_API_SECRET environment variables or pass them in config.');
    }

    // Initialize ShipStation client
    this.client = new ShipStation({
      credentials: {
        v1: {
          apiKey,
          apiSecret,
          partnerKey: config.partnerKey || process.env.SHIPSTATION_PARTNER_KEY
        },
        v2: {
          apiKey: v2Api<PERSON><PERSON>,
          mock: config.mock || false
        }
      },
      requestConfig: config.requestConfig || {
        timeout: 30000
      },
      retryConfig: config.retryConfig || {
        retries: 3,
        retryDelay: (retryCount) => retryCount * 1000
      }
    });

    // Bind methods to preserve context
    this.getOrders = this.getOrders.bind(this);
    this.createOrder = this.createOrder.bind(this);
    this.updateOrder = this.updateOrder.bind(this);
    this.createShipment = this.createShipment.bind(this);
    this.getShipments = this.getShipments.bind(this);
    this.getCarriers = this.getCarriers.bind(this);
    this.getRates = this.getRates.bind(this);
    this.createLabel = this.createLabel.bind(this);
    this.getStores = this.getStores.bind(this);
    this.getWarehouses = this.getWarehouses.bind(this);
    this.getTags = this.getTags.bind(this);
    this.getProducts = this.getProducts.bind(this);
  }

  /**
   * Orders Management
   */

  /**
   * Get orders with filtering options
   * @param {Object} options - Filter options
   * @returns {Promise<Array>} Array of orders
   */
  async getOrders(options = {}) {
    try {
      const defaultOptions = {
        pageSize: 100,
        page: 1,
        sortBy: 'ModifyDate',
        sortDir: 'DESC'
      };

      const params = { ...defaultOptions, ...options };
      const response = await this.client.v1.orders.list(params);

      return {
        orders: response.orders || [],
        total: response.total || 0,
        page: response.page || 1,
        pages: response.pages || 1
      };
    } catch (error) {
      throw this._handleError('getOrders', error);
    }
  }

  /**
   * Get orders by status
   * @param {string} status - Order status (awaiting_payment, awaiting_shipment, shipped, etc.)
   * @param {Object} options - Additional filter options
   * @returns {Promise<Array>} Array of orders
   */
  async getOrdersByStatus(status, options = {}) {
    return this.getOrders({ ...options, orderStatus: status });
  }

  /**
   * Get orders awaiting shipment
   * @param {Object} options - Additional filter options
   * @returns {Promise<Array>} Array of orders
   */
  async getOrdersAwaitingShipment(options = {}) {
    return this.getOrdersByStatus('awaiting_shipment', options);
  }

  /**
   * Create a new order
   * @param {Object} orderData - Order data
   * @returns {Promise<Object>} Created order
   */
  async createOrder(orderData) {
    try {
      const response = await this.client.v1.orders.create(orderData);
      return response;
    } catch (error) {
      throw this._handleError('createOrder', error);
    }
  }

  /**
   * Update an existing order
   * @param {number} orderId - Order ID
   * @param {Object} orderData - Updated order data
   * @returns {Promise<Object>} Updated order
   */
  async updateOrder(orderId, orderData) {
    try {
      const response = await this.client.v1.orders.update(orderId, orderData);
      return response;
    } catch (error) {
      throw this._handleError('updateOrder', error);
    }
  }

  /**
   * Get order by ID
   * @param {number} orderId - Order ID
   * @returns {Promise<Object>} Order details
   */
  async getOrder(orderId) {
    try {
      const response = await this.client.v1.orders.get(orderId);
      return response;
    } catch (error) {
      throw this._handleError('getOrder', error);
    }
  }

  /**
   * Delete an order
   * @param {number} orderId - Order ID
   * @returns {Promise<Object>} Deletion result
   */
  async deleteOrder(orderId) {
    try {
      const response = await this.client.v1.orders.delete(orderId);
      return response;
    } catch (error) {
      throw this._handleError('deleteOrder', error);
    }
  }

  /**
   * Shipments Management
   */

  /**
   * Create a shipment/label
   * @param {Object} shipmentData - Shipment data
   * @returns {Promise<Object>} Created shipment with label
   */
  async createShipment(shipmentData) {
    try {
      const response = await this.client.v1.shipments.create(shipmentData);
      return response;
    } catch (error) {
      throw this._handleError('createShipment', error);
    }
  }

  /**
   * Get shipments with filtering options
   * @param {Object} options - Filter options
   * @returns {Promise<Object>} Shipments data
   */
  async getShipments(options = {}) {
    try {
      const defaultOptions = {
        pageSize: 100,
        page: 1,
        sortBy: 'ShipDate',
        sortDir: 'DESC'
      };

      const params = { ...defaultOptions, ...options };
      const response = await this.client.v1.shipments.list(params);

      return {
        shipments: response.shipments || [],
        total: response.total || 0,
        page: response.page || 1,
        pages: response.pages || 1
      };
    } catch (error) {
      throw this._handleError('getShipments', error);
    }
  }

  /**
   * Get shipment by ID
   * @param {number} shipmentId - Shipment ID
   * @returns {Promise<Object>} Shipment details
   */
  async getShipment(shipmentId) {
    try {
      const response = await this.client.v1.shipments.get(shipmentId);
      return response;
    } catch (error) {
      throw this._handleError('getShipment', error);
    }
  }

  /**
   * Void a shipment label
   * @param {number} shipmentId - Shipment ID
   * @returns {Promise<Object>} Void result
   */
  async voidShipment(shipmentId) {
    try {
      const response = await this.client.v1.shipments.void(shipmentId);
      return response;
    } catch (error) {
      throw this._handleError('voidShipment', error);
    }
  }

  /**
   * Carriers and Rates
   */

  /**
   * Get all carriers
   * @returns {Promise<Array>} Array of carriers
   */
  async getCarriers() {
    try {
      const response = await this.client.v1.carriers.list();
      return response;
    } catch (error) {
      throw this._handleError('getCarriers', error);
    }
  }

  /**
   * Get shipping rates
   * @param {Object} rateData - Rate request data
   * @returns {Promise<Array>} Array of rates
   */
  async getRates(rateData) {
    try {
      const response = await this.client.v1.shipments.getRates(rateData);
      return response;
    } catch (error) {
      throw this._handleError('getRates', error);
    }
  }

  /**
   * Labels
   */

  /**
   * Create a shipping label
   * @param {Object} labelData - Label data
   * @returns {Promise<Object>} Created label
   */
  async createLabel(labelData) {
    try {
      const response = await this.client.v1.shipments.createLabel(labelData);
      return response;
    } catch (error) {
      throw this._handleError('createLabel', error);
    }
  }

  /**
   * Stores Management
   */

  /**
   * Get all stores
   * @returns {Promise<Array>} Array of stores
   */
  async getStores() {
    try {
      const response = await this.client.v1.stores.list();
      return response;
    } catch (error) {
      throw this._handleError('getStores', error);
    }
  }

  /**
   * Get store by ID
   * @param {number} storeId - Store ID
   * @returns {Promise<Object>} Store details
   */
  async getStore(storeId) {
    try {
      const response = await this.client.v1.stores.get(storeId);
      return response;
    } catch (error) {
      throw this._handleError('getStore', error);
    }
  }

  /**
   * Update store
   * @param {number} storeId - Store ID
   * @param {Object} storeData - Updated store data
   * @returns {Promise<Object>} Updated store
   */
  async updateStore(storeId, storeData) {
    try {
      const response = await this.client.v1.stores.update(storeId, storeData);
      return response;
    } catch (error) {
      throw this._handleError('updateStore', error);
    }
  }

  /**
   * Warehouses Management
   */

  /**
   * Get all warehouses
   * @returns {Promise<Array>} Array of warehouses
   */
  async getWarehouses() {
    try {
      const response = await this.client.v1.warehouses.list();
      return response;
    } catch (error) {
      throw this._handleError('getWarehouses', error);
    }
  }

  /**
   * Get warehouse by ID
   * @param {number} warehouseId - Warehouse ID
   * @returns {Promise<Object>} Warehouse details
   */
  async getWarehouse(warehouseId) {
    try {
      const response = await this.client.v1.warehouses.get(warehouseId);
      return response;
    } catch (error) {
      throw this._handleError('getWarehouse', error);
    }
  }

  /**
   * Create warehouse
   * @param {Object} warehouseData - Warehouse data
   * @returns {Promise<Object>} Created warehouse
   */
  async createWarehouse(warehouseData) {
    try {
      const response = await this.client.v1.warehouses.create(warehouseData);
      return response;
    } catch (error) {
      throw this._handleError('createWarehouse', error);
    }
  }

  /**
   * Tags Management
   */

  /**
   * Get all tags
   * @returns {Promise<Array>} Array of tags
   */
  async getTags() {
    try {
      const response = await this.client.v1.accounts.listTags();
      return response;
    } catch (error) {
      throw this._handleError('getTags', error);
    }
  }

  /**
   * Products Management
   */

  /**
   * Get products with filtering options
   * @param {Object} options - Filter options
   * @returns {Promise<Object>} Products data
   */
  async getProducts(options = {}) {
    try {
      const defaultOptions = {
        pageSize: 100,
        page: 1,
        sortBy: 'ModifyDate',
        sortDir: 'DESC'
      };

      const params = { ...defaultOptions, ...options };
      const response = await this.client.v1.products.list(params);

      return {
        products: response.products || [],
        total: response.total || 0,
        page: response.page || 1,
        pages: response.pages || 1
      };
    } catch (error) {
      throw this._handleError('getProducts', error);
    }
  }

  /**
   * Get product by ID
   * @param {number} productId - Product ID
   * @returns {Promise<Object>} Product details
   */
  async getProduct(productId) {
    try {
      const response = await this.client.v1.products.get(productId);
      return response;
    } catch (error) {
      throw this._handleError('getProduct', error);
    }
  }

  /**
   * Create product
   * @param {Object} productData - Product data
   * @returns {Promise<Object>} Created product
   */
  async createProduct(productData) {
    try {
      const response = await this.client.v1.products.create(productData);
      return response;
    } catch (error) {
      throw this._handleError('createProduct', error);
    }
  }

  /**
   * Update product
   * @param {number} productId - Product ID
   * @param {Object} productData - Updated product data
   * @returns {Promise<Object>} Updated product
   */
  async updateProduct(productId, productData) {
    try {
      const response = await this.client.v1.products.update(productId, productData);
      return response;
    } catch (error) {
      throw this._handleError('updateProduct', error);
    }
  }

  /**
   * Webhooks Management
   */

  /**
   * Get all webhooks
   * @returns {Promise<Array>} Array of webhooks
   */
  async getWebhooks() {
    try {
      const response = await this.client.v1.webhooks.list();
      return response;
    } catch (error) {
      throw this._handleError('getWebhooks', error);
    }
  }

  /**
   * Create webhook
   * @param {Object} webhookData - Webhook data
   * @returns {Promise<Object>} Created webhook
   */
  async createWebhook(webhookData) {
    try {
      const response = await this.client.v1.webhooks.create(webhookData);
      return response;
    } catch (error) {
      throw this._handleError('createWebhook', error);
    }
  }

  /**
   * Delete webhook
   * @param {number} webhookId - Webhook ID
   * @returns {Promise<Object>} Deletion result
   */
  async deleteWebhook(webhookId) {
    try {
      const response = await this.client.v1.webhooks.delete(webhookId);
      return response;
    } catch (error) {
      throw this._handleError('deleteWebhook', error);
    }
  }

  /**
   * Utility Methods
   */

  /**
   * Get all orders for a specific date range
   * @param {string} startDate - Start date (YYYY-MM-DD)
   * @param {string} endDate - End date (YYYY-MM-DD)
   * @param {Object} options - Additional options
   * @returns {Promise<Array>} Array of orders
   */
  async getOrdersByDateRange(startDate, endDate, options = {}) {
    return this.getOrders({
      ...options,
      createDateStart: startDate,
      createDateEnd: endDate
    });
  }

  /**
   * Get all shipments for a specific date range
   * @param {string} startDate - Start date (YYYY-MM-DD)
   * @param {string} endDate - End date (YYYY-MM-DD)
   * @param {Object} options - Additional options
   * @returns {Promise<Array>} Array of shipments
   */
  async getShipmentsByDateRange(startDate, endDate, options = {}) {
    return this.getShipments({
      ...options,
      shipDateStart: startDate,
      shipDateEnd: endDate
    });
  }

  /**
   * Bulk create orders
   * @param {Array} ordersData - Array of order data objects
   * @returns {Promise<Array>} Array of created orders
   */
  async bulkCreateOrders(ordersData) {
    const results = [];
    for (const orderData of ordersData) {
      try {
        const result = await this.createOrder(orderData);
        results.push({ success: true, data: result });
      } catch (error) {
        results.push({ success: false, error: error.message, data: orderData });
      }
    }
    return results;
  }

  /**
   * Get order status counts
   * @returns {Promise<Object>} Object with status counts
   */
  async getOrderStatusCounts() {
    try {
      const statuses = [
        'awaiting_payment',
        'awaiting_shipment',
        'shipped',
        'on_hold',
        'cancelled'
      ];

      const counts = {};
      for (const status of statuses) {
        const result = await this.getOrdersByStatus(status, { pageSize: 1 });
        counts[status] = result.total;
      }

      return counts;
    } catch (error) {
      throw this._handleError('getOrderStatusCounts', error);
    }
  }

  /**
   * Search orders by customer email
   * @param {string} email - Customer email
   * @param {Object} options - Additional options
   * @returns {Promise<Array>} Array of orders
   */
  async searchOrdersByEmail(email, options = {}) {
    return this.getOrders({
      ...options,
      customerEmail: email
    });
  }

  /**
   * Search orders by order number
   * @param {string} orderNumber - Order number
   * @param {Object} options - Additional options
   * @returns {Promise<Array>} Array of orders
   */
  async searchOrdersByNumber(orderNumber, options = {}) {
    return this.getOrders({
      ...options,
      orderNumber: orderNumber
    });
  }

  /**
   * Private error handling method
   * @param {string} method - Method name where error occurred
   * @param {Error} error - Original error
   * @returns {Error} Enhanced error with context
   */
  _handleError(method, error) {
    const enhancedError = new Error(`ShipStation API Error in ${method}: ${error.message}`);
    enhancedError.originalError = error;
    enhancedError.method = method;

    // Add response data if available
    if (error.response) {
      enhancedError.status = error.response.status;
      enhancedError.statusText = error.response.statusText;
      enhancedError.responseData = error.response.data;
    }

    return enhancedError;
  }

  /**
   * Get direct access to the underlying ShipStation client
   * @returns {Object} ShipStation client instance
   */
  getClient() {
    return this.client;
  }
}

// Export both the class and a factory function
module.exports = ShipStationLibrary;

// Factory function for easier instantiation
module.exports.create = (config) => new ShipStationLibrary(config);

// Export individual methods for require destructuring
module.exports.ShipStationLibrary = ShipStationLibrary;