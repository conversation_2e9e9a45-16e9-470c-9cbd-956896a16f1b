const ShipStationLibrary = require('./index');

/**
 * Simple test file to verify the vanilla JavaScript ShipStation library works
 *
 * To run this test:
 * 1. Set your environment variables:
 *    export SHIPSTATION_API_KEY=your_api_key
 *    export SHIPSTATION_API_SECRET=your_api_secret
 *
 * 2. Run: node shipstation/test.js
 */

async function testLibrary() {
  console.log('🧪 Testing Vanilla JavaScript ShipStation Library...\n');

  try {
    // Test 1: Initialize library
    console.log('1️⃣ Testing library initialization...');

    // Test basic initialization
    const testShipStation = new ShipStationLibrary({
      apiKey: 'test-key',
      apiSecret: 'test-secret'
    });
    console.log('✅ Library initialized successfully');

    // Test 2: Test with environment variables (if available)
    console.log('\n2️⃣ Testing with environment variables...');
    try {
      const shipstation = new ShipStationLibrary();
      console.log('✅ Library initialized with environment variables');
      
      // Test 3: Test connection
      console.log('\n3️⃣ Testing API connection...');
      const connectionTest = await shipstation.testConnection();
      if (connectionTest.success) {
        console.log('✅ API connection successful:', connectionTest.message);
        console.log('   Store count:', connectionTest.storeCount);
      } else {
        console.log('❌ API connection failed:', connectionTest.message);
        return;
      }

      // Test 4: Test rate limit status
      console.log('\n4️⃣ Testing rate limit status...');
      const rateLimitStatus = shipstation.getRateLimitStatus();
      console.log('✅ Rate limit status:', rateLimitStatus);

      // Test 5: Test basic API call (get carriers)
      console.log('\n5️⃣ Testing carriers retrieval...');
      const carriers = await shipstation.getCarriers();
      console.log('✅ Successfully retrieved carriers:', carriers.length, 'carriers found');

      // Test 6: Test stores
      console.log('\n6️⃣ Testing stores retrieval...');
      const stores = await shipstation.getStores();
      console.log('✅ Successfully retrieved stores:', stores.length, 'stores found');

      // Test 7: Test warehouses
      console.log('\n7️⃣ Testing warehouses retrieval...');
      const warehouses = await shipstation.getWarehouses();
      console.log('✅ Successfully retrieved warehouses:', warehouses.length, 'warehouses found');

      // Test 8: Test tags
      console.log('\n8️⃣ Testing tags retrieval...');
      const tags = await shipstation.getTags();
      console.log('✅ Successfully retrieved tags:', tags.length, 'tags found');

      // Test 9: Test orders (with pagination)
      console.log('\n9️⃣ Testing orders retrieval...');
      const orders = await shipstation.getOrders({ pageSize: 5, page: 1 });
      console.log('✅ Successfully retrieved orders:', orders.total, 'total orders,', orders.orders?.length || 0, 'on this page');

      // Test 10: Test order status counts
      console.log('\n🔟 Testing order status counts...');
      const statusCounts = await shipstation.getOrderStatusCounts();
      console.log('✅ Successfully retrieved order status counts:', statusCounts);

      // Test 11: Test products
      console.log('\n1️⃣1️⃣ Testing products retrieval...');
      const products = await shipstation.getProducts({ pageSize: 5, page: 1 });
      console.log('✅ Successfully retrieved products:', products.total, 'total products,', products.products?.length || 0, 'on this page');

      // Test 12: Test shipments
      console.log('\n1️⃣2️⃣ Testing shipments retrieval...');
      const shipments = await shipstation.getShipments({ pageSize: 5, page: 1 });
      console.log('✅ Successfully retrieved shipments:', shipments.total, 'total shipments,', shipments.shipments?.length || 0, 'on this page');
      
    } catch (envError) {
      console.log('⚠️  Environment variables not set or invalid credentials');
      console.log('   Set SHIPSTATION_API_KEY and SHIPSTATION_API_SECRET to test with real API');
      console.log('   Error:', envError.message);
    }

    // Test 11: Test factory function
    console.log('\n1️⃣1️⃣ Testing factory function...');
    const factoryShipStation = ShipStationLibrary.create({
      apiKey: 'test-key',
      apiSecret: 'test-secret',
      mock: true
    });
    console.log('✅ Factory function works correctly');

    // Test 12: Test error handling
    console.log('\n1️⃣2️⃣ Testing error handling...');
    try {
      const invalidShipStation = new ShipStationLibrary({
        apiKey: 'invalid-key',
        apiSecret: 'invalid-secret'
      });
      await invalidShipStation.getCarriers();
    } catch (error) {
      console.log('✅ Error handling works correctly');
      console.log('   Error method:', error.method);
      console.log('   Error message:', error.message);
      console.log('   Has original error:', !!error.originalError);
    }

    // Test 13: Test direct client access
    console.log('\n1️⃣3️⃣ Testing direct client access...');
    const client = mockShipStation.getClient();
    console.log('✅ Direct client access works:', !!client.v1 && !!client.v2);

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Test Summary:');
    console.log('   ✅ Library initialization');
    console.log('   ✅ Environment variable configuration');
    console.log('   ✅ Basic API calls (if credentials provided)');
    console.log('   ✅ Factory function');
    console.log('   ✅ Error handling');
    console.log('   ✅ Direct client access');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

// Test configuration validation
function testConfiguration() {
  console.log('\n🔧 Testing Configuration Options...\n');

  // Test 1: Missing credentials
  console.log('1️⃣ Testing missing credentials...');
  try {
    new ShipStationLibrary({});
    console.log('❌ Should have thrown error for missing credentials');
  } catch (error) {
    console.log('✅ Correctly throws error for missing credentials');
  }

  // Test 2: Partial credentials
  console.log('\n2️⃣ Testing partial credentials...');
  try {
    new ShipStationLibrary({ apiKey: 'test' });
    console.log('❌ Should have thrown error for missing apiSecret');
  } catch (error) {
    console.log('✅ Correctly throws error for missing apiSecret');
  }

  // Test 3: Valid configuration
  console.log('\n3️⃣ Testing valid configuration...');
  try {
    const shipstation = new ShipStationLibrary({
      apiKey: 'test-key',
      apiSecret: 'test-secret'
    });
    console.log('✅ Valid configuration accepted');
  } catch (error) {
    console.log('❌ Valid configuration rejected:', error.message);
  }

  // Test 4: Mock configuration
  console.log('\n4️⃣ Testing mock configuration...');
  try {
    const shipstation = new ShipStationLibrary({
      apiKey: 'test-key',
      apiSecret: 'test-secret',
      mock: true
    });
    console.log('✅ Mock configuration works');
  } catch (error) {
    console.log('❌ Mock configuration failed:', error.message);
  }

  console.log('\n✅ Configuration tests completed!');
}

// Test method binding
function testMethodBinding() {
  console.log('\n🔗 Testing Method Binding...\n');

  try {
    const shipstation = new ShipStationLibrary({
      apiKey: 'test-key',
      apiSecret: 'test-secret',
      mock: true
    });

    // Extract methods and test they're bound correctly
    const { getOrders, getCarriers, getStores } = shipstation;
    
    console.log('✅ Methods can be destructured');
    console.log('✅ getOrders is bound:', typeof getOrders === 'function');
    console.log('✅ getCarriers is bound:', typeof getCarriers === 'function');
    console.log('✅ getStores is bound:', typeof getStores === 'function');

  } catch (error) {
    console.log('❌ Method binding test failed:', error.message);
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting ShipStation Library Tests\n');
  console.log('=' .repeat(50));
  
  testConfiguration();
  testMethodBinding();
  await testLibrary();
  
  console.log('\n' + '='.repeat(50));
  console.log('🏁 All tests completed!');
}

// Export for use in other files
module.exports = {
  testLibrary,
  testConfiguration,
  testMethodBinding,
  runAllTests
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}
