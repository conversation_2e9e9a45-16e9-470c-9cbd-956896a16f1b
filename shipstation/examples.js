const ShipStationLibrary = require('./shipstation');

// Example usage of the Vanilla JavaScript ShipStation Library

/**
 * Initialize the library
 */

// Method 1: Using environment variables
// Set SHIPSTATION_API_KEY and SHIPSTATION_API_SECRET in your environment
const shipstation = new ShipStationLibrary();

// Method 2: Using configuration object
const shipstationWithConfig = new ShipStationLibrary({
  apiKey: 'your-api-key',
  apiSecret: 'your-api-secret',
  timeout: 30000,
  retries: 3,
  retryDelay: 1000
});

// Method 3: Using factory function
const shipstationFactory = ShipStationLibrary.create({
  apiKey: 'your-api-key',
  apiSecret: 'your-api-secret'
});

/**
 * Orders Examples
 */

async function ordersExamples() {
  try {
    // Get all orders (paginated)
    const orders = await shipstation.getOrders({
      pageSize: 50,
      page: 1,
      sortBy: 'ModifyDate',
      sortDir: 'DESC'
    });
    console.log('Orders:', orders);

    // Get orders awaiting shipment
    const awaitingShipment = await shipstation.getOrdersAwaitingShipment();
    console.log('Orders awaiting shipment:', awaitingShipment);

    // Get orders by status
    const shippedOrders = await shipstation.getOrdersByStatus('shipped');
    console.log('Shipped orders:', shippedOrders);

    // Get orders by date range
    const recentOrders = await shipstation.getOrdersByDateRange('2024-01-01', '2024-01-31');
    console.log('Recent orders:', recentOrders);

    // Search orders by customer email
    const customerOrders = await shipstation.searchOrdersByEmail('<EMAIL>');
    console.log('Customer orders:', customerOrders);

    // Search orders by order number
    const specificOrder = await shipstation.searchOrdersByNumber('ORDER-123');
    console.log('Specific order:', specificOrder);

    // Get order status counts
    const statusCounts = await shipstation.getOrderStatusCounts();
    console.log('Order status counts:', statusCounts);

    // Create a new order
    const newOrder = await shipstation.createOrder({
      orderNumber: 'ORDER-' + Date.now(),
      orderDate: new Date().toISOString(),
      orderStatus: 'awaiting_shipment',
      customerUsername: '<EMAIL>',
      customerEmail: '<EMAIL>',
      billTo: {
        name: 'John Doe',
        company: 'Example Company',
        street1: '123 Main St',
        city: 'Anytown',
        state: 'CA',
        postalCode: '12345',
        country: 'US',
        phone: '************'
      },
      shipTo: {
        name: 'John Doe',
        company: 'Example Company',
        street1: '123 Main St',
        city: 'Anytown',
        state: 'CA',
        postalCode: '12345',
        country: 'US',
        phone: '************'
      },
      items: [
        {
          lineItemKey: 'item1',
          sku: 'PRODUCT-SKU',
          name: 'Product Name',
          quantity: 1,
          unitPrice: 29.99,
          weight: {
            value: 1.5,
            units: 'pounds'
          }
        }
      ]
    });
    console.log('Created order:', newOrder);

    // Update an order
    const updatedOrder = await shipstation.updateOrder(newOrder.orderId, {
      orderStatus: 'on_hold',
      internalNotes: 'Order placed on hold for review'
    });
    console.log('Updated order:', updatedOrder);

    // Get specific order
    const orderDetails = await shipstation.getOrder(newOrder.orderId);
    console.log('Order details:', orderDetails);

  } catch (error) {
    console.error('Orders example error:', error.message);
  }
}

/**
 * Shipments Examples
 */

async function shipmentsExamples() {
  try {
    // Get all shipments
    const shipments = await shipstation.getShipments({
      pageSize: 50,
      page: 1
    });
    console.log('Shipments:', shipments);

    // Get shipments by date range
    const recentShipments = await shipstation.getShipmentsByDateRange('2024-01-01', '2024-01-31');
    console.log('Recent shipments:', recentShipments);

    // Create a shipment/label
    const shipment = await shipstation.createShipment({
      orderId: 12345, // Replace with actual order ID
      carrierCode: 'fedex',
      serviceCode: 'fedex_ground',
      packageCode: 'package',
      confirmation: 'delivery',
      shipDate: new Date().toISOString().split('T')[0],
      weight: {
        value: 1.5,
        units: 'pounds'
      },
      dimensions: {
        units: 'inches',
        length: 10,
        width: 8,
        height: 6
      }
    });
    console.log('Created shipment:', shipment);

    // Get specific shipment
    const shipmentDetails = await shipstation.getShipment(shipment.shipmentId);
    console.log('Shipment details:', shipmentDetails);

    // Void a shipment (if needed)
    // const voidResult = await shipstation.voidShipment(shipment.shipmentId);
    // console.log('Void result:', voidResult);

  } catch (error) {
    console.error('Shipments example error:', error.message);
  }
}

/**
 * Rates Examples
 */

async function ratesExamples() {
  try {
    // Get shipping rates
    const rates = await shipstation.getRates({
      carrierCode: 'fedex',
      fromPostalCode: '12345',
      toState: 'CA',
      toPostalCode: '90210',
      toCountry: 'US',
      weight: {
        value: 1.5,
        units: 'pounds'
      },
      dimensions: {
        units: 'inches',
        length: 10,
        width: 8,
        height: 6
      }
    });
    console.log('Shipping rates:', rates);

  } catch (error) {
    console.error('Rates example error:', error.message);
  }
}

/**
 * Carriers Examples
 */

async function carriersExamples() {
  try {
    // Get all carriers
    const carriers = await shipstation.getCarriers();
    console.log('Carriers:', carriers);

  } catch (error) {
    console.error('Carriers example error:', error.message);
  }
}

/**
 * Stores Examples
 */

async function storesExamples() {
  try {
    // Get all stores
    const stores = await shipstation.getStores();
    console.log('Stores:', stores);

    if (stores.length > 0) {
      // Get specific store
      const store = await shipstation.getStore(stores[0].storeId);
      console.log('Store details:', store);
    }

  } catch (error) {
    console.error('Stores example error:', error.message);
  }
}

/**
 * Warehouses Examples
 */

async function warehousesExamples() {
  try {
    // Get all warehouses
    const warehouses = await shipstation.getWarehouses();
    console.log('Warehouses:', warehouses);

    if (warehouses.length > 0) {
      // Get specific warehouse
      const warehouse = await shipstation.getWarehouse(warehouses[0].warehouseId);
      console.log('Warehouse details:', warehouse);
    }

  } catch (error) {
    console.error('Warehouses example error:', error.message);
  }
}

/**
 * Products Examples
 */

async function productsExamples() {
  try {
    // Get all products
    const products = await shipstation.getProducts({
      pageSize: 50,
      page: 1
    });
    console.log('Products:', products);

    // Create a new product
    const newProduct = await shipstation.createProduct({
      sku: 'NEW-PRODUCT-' + Date.now(),
      name: 'New Product',
      price: 29.99,
      weightOz: 24,
      internalNotes: 'Created via API',
      fulfillmentSku: 'FULFILLMENT-SKU',
      createDate: new Date().toISOString()
    });
    console.log('Created product:', newProduct);

    // Get specific product
    const productDetails = await shipstation.getProduct(newProduct.productId);
    console.log('Product details:', productDetails);

    // Update product
    const updatedProduct = await shipstation.updateProduct(newProduct.productId, {
      price: 34.99,
      internalNotes: 'Updated price via API'
    });
    console.log('Updated product:', updatedProduct);

  } catch (error) {
    console.error('Products example error:', error.message);
  }
}

/**
 * Bulk Operations Examples
 */

async function bulkExamples() {
  try {
    // Bulk create orders
    const ordersData = [
      {
        orderNumber: 'BULK-ORDER-1-' + Date.now(),
        orderDate: new Date().toISOString(),
        orderStatus: 'awaiting_shipment',
        customerEmail: '<EMAIL>',
        // ... other order data
      },
      {
        orderNumber: 'BULK-ORDER-2-' + Date.now(),
        orderDate: new Date().toISOString(),
        orderStatus: 'awaiting_shipment',
        customerEmail: '<EMAIL>',
        // ... other order data
      }
    ];

    const bulkResults = await shipstation.bulkCreateOrders(ordersData);
    console.log('Bulk create results:', bulkResults);

  } catch (error) {
    console.error('Bulk examples error:', error.message);
  }
}

/**
 * Run all examples
 */

async function runExamples() {
  console.log('Running ShipStation Library Examples...\n');

  // Uncomment the examples you want to run
  // await ordersExamples();
  // await shipmentsExamples();
  // await ratesExamples();
  // await carriersExamples();
  // await storesExamples();
  // await warehousesExamples();
  // await productsExamples();
  // await bulkExamples();

  console.log('\nExamples completed!');
}

// Export for use in other files
module.exports = {
  ordersExamples,
  shipmentsExamples,
  ratesExamples,
  carriersExamples,
  storesExamples,
  warehousesExamples,
  productsExamples,
  bulkExamples,
  runExamples
};

// Run examples if this file is executed directly
if (require.main === module) {
  runExamples().catch(console.error);
}
