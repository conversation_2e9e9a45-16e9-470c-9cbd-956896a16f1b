/**
 * ShipStation Dual API Demo
 * Demonstrates the differences between v1 and v2 APIs
 */

const ShipStationLibrary = require('./index');

async function demoApiVersions() {
  console.log('🚀 ShipStation Dual API Demo\n');
  console.log('=' .repeat(60));

  // Initialize library
  const shipstation = new ShipStationLibrary({
    apiKey: process.env.SHIPSTATION_API_KEY || 'demo-key',
    apiSecret: process.env.SHIPSTATION_API_SECRET || 'demo-secret',
    v2ApiKey: process.env.SHIPSTATION_V2_API_KEY || 'demo-v2-key',
    mock: !process.env.SHIPSTATION_API_KEY // Use mock if no real credentials
  });

  // Show API capabilities
  console.log('\n📋 API Capabilities:');
  const capabilities = shipstation.getApiCapabilities();
  
  console.log('\n🔹 V1 API:');
  console.log(`   Available: ${capabilities.v1.available ? '✅' : '❌'}`);
  console.log(`   Base URL: ${capabilities.v1.baseUrl}`);
  console.log(`   Rate Limit: ${capabilities.v1.rateLimit}`);
  console.log(`   Features: ${capabilities.v1.features.length} available`);
  
  console.log('\n🔹 V2 API:');
  console.log(`   Available: ${capabilities.v2.available ? '✅' : '❌'}`);
  console.log(`   Base URL: ${capabilities.v2.baseUrl}`);
  console.log(`   Rate Limit: ${capabilities.v2.rateLimit}`);
  console.log(`   Mock Mode: ${capabilities.v2.mock ? '✅' : '❌'}`);
  console.log(`   Features: ${capabilities.v2.features.length} available`);

  // Test connections
  console.log('\n🔗 Testing API Connections:');
  const connectionTests = await shipstation.testConnectionBoth();
  
  console.log(`\nV1 API: ${connectionTests.v1.success ? '✅' : '❌'} ${connectionTests.v1.message}`);
  if (connectionTests.v1.storeCount !== undefined) {
    console.log(`   Stores found: ${connectionTests.v1.storeCount}`);
  }
  
  console.log(`V2 API: ${connectionTests.v2.success ? '✅' : '❌'} ${connectionTests.v2.message}`);
  if (connectionTests.v2.carrierCount !== undefined) {
    console.log(`   Carriers found: ${connectionTests.v2.carrierCount}`);
  }

  // Show rate limit status
  console.log('\n⏱️  Rate Limit Status:');
  const rateLimits = shipstation.getRateLimitStatus();
  console.log('V1 API:', rateLimits.v1);
  console.log('V2 API:', rateLimits.v2);

  // Demo API differences
  console.log('\n🔄 API Method Comparisons:');
  
  // Carriers comparison
  console.log('\n📦 Carriers:');
  if (connectionTests.v1.success) {
    try {
      console.log('   V1 API: Getting carriers...');
      const v1Carriers = await shipstation.getCarriers();
      console.log(`   ✅ V1 returned ${v1Carriers.length} carriers`);
      if (v1Carriers.length > 0) {
        console.log(`   Sample V1 carrier: ${v1Carriers[0].name || v1Carriers[0].carrierCode}`);
      }
    } catch (error) {
      console.log(`   ❌ V1 failed: ${error.message}`);
    }
  }

  if (connectionTests.v2.success) {
    try {
      console.log('   V2 API: Getting carriers...');
      const v2Carriers = await shipstation.getCarriersV2();
      console.log(`   ✅ V2 returned ${v2Carriers.carriers?.length || 0} carriers`);
      if (v2Carriers.carriers?.length > 0) {
        console.log(`   Sample V2 carrier: ${v2Carriers.carriers[0].name || v2Carriers.carriers[0].carrierCode}`);
      }
    } catch (error) {
      console.log(`   ❌ V2 failed: ${error.message}`);
    }
  }

  // Shipments comparison
  console.log('\n🚢 Shipments:');
  if (connectionTests.v1.success) {
    try {
      console.log('   V1 API: Getting shipments...');
      const v1Shipments = await shipstation.getShipments({ pageSize: 3 });
      console.log(`   ✅ V1 returned ${v1Shipments.total} total shipments`);
    } catch (error) {
      console.log(`   ❌ V1 failed: ${error.message}`);
    }
  }

  if (connectionTests.v2.success) {
    try {
      console.log('   V2 API: Getting shipments...');
      const v2Shipments = await shipstation.getShipmentsV2({ pageSize: 3 });
      console.log(`   ✅ V2 returned ${v2Shipments.shipments?.length || 0} shipments on page`);
    } catch (error) {
      console.log(`   ❌ V2 failed: ${error.message}`);
    }
  }

  // V2-only features
  console.log('\n🆕 V2-Only Features:');
  if (connectionTests.v2.success) {
    // Package Types
    try {
      console.log('   📦 Package Types...');
      const packageTypes = await shipstation.getPackageTypesV2();
      console.log(`   ✅ Found ${packageTypes.packageTypes?.length || 0} package types`);
    } catch (error) {
      console.log(`   ❌ Package types failed: ${error.message}`);
    }

    // Tags
    try {
      console.log('   🏷️  Tags...');
      const tags = await shipstation.getTagsV2();
      console.log(`   ✅ Found ${tags.tags?.length || 0} tags`);
    } catch (error) {
      console.log(`   ❌ Tags failed: ${error.message}`);
    }

    // Warehouses
    try {
      console.log('   🏭 Warehouses...');
      const warehouses = await shipstation.getWarehousesV2();
      console.log(`   ✅ Found ${warehouses.warehouses?.length || 0} warehouses`);
    } catch (error) {
      console.log(`   ❌ Warehouses failed: ${error.message}`);
    }
  } else {
    console.log('   ⚠️  V2 API not available - cannot test V2-only features');
  }

  // Unified API demo
  console.log('\n🔄 Unified API Methods:');
  try {
    console.log('   🔄 Unified carriers (prefers V2)...');
    const unifiedCarriers = await shipstation.getCarriersUnified('v2');
    console.log('   ✅ Unified carriers method successful');
  } catch (error) {
    console.log(`   ❌ Unified carriers failed: ${error.message}`);
  }

  // Mock mode demo (V2 only)
  if (capabilities.v2.mock) {
    console.log('\n🎭 Mock API Demo:');
    console.log('   Currently using V2 mock API for testing');
    console.log('   Mock API provides sample data without real API calls');
    console.log('   Perfect for development and testing!');
  }

  // Performance comparison
  console.log('\n⚡ Performance Notes:');
  console.log('   V1 API: 40 requests/minute limit');
  console.log('   V2 API: 300 requests/minute limit (estimated)');
  console.log('   V2 API: Generally faster response times');
  console.log('   V2 API: More detailed error messages');

  // Best practices
  console.log('\n💡 Best Practices:');
  console.log('   • Use V1 API for orders, stores, and products');
  console.log('   • Use V2 API for shipments, rates, and tracking');
  console.log('   • Use unified methods for automatic version selection');
  console.log('   • Monitor rate limits with getRateLimitStatus()');
  console.log('   • Use mock mode for development and testing');

  console.log('\n' + '='.repeat(60));
  console.log('✅ Demo completed!');
}

// Mock mode demo
async function demoMockMode() {
  console.log('\n🎭 Mock Mode Demo:');
  
  const mockShipStation = new ShipStationLibrary({
    apiKey: 'mock-key',
    apiSecret: 'mock-secret',
    v2ApiKey: 'mock-v2-key',
    mock: true
  });

  console.log('   Mock mode enabled for V2 API');
  console.log('   Base URL:', mockShipStation.v2BaseUrl);
  
  try {
    const mockCarriers = await mockShipStation.getCarriersV2();
    console.log('   ✅ Mock carriers retrieved successfully');
    console.log('   Mock data available for testing without real API calls');
  } catch (error) {
    console.log('   ❌ Mock API failed:', error.message);
  }
}

// Configuration examples
function showConfigurationExamples() {
  console.log('\n⚙️  Configuration Examples:');
  
  console.log('\n1. Environment Variables:');
  console.log('   export SHIPSTATION_API_KEY=your_v1_key');
  console.log('   export SHIPSTATION_API_SECRET=your_v1_secret');
  console.log('   export SHIPSTATION_V2_API_KEY=your_v2_key');
  
  console.log('\n2. Manual Configuration:');
  console.log('   const shipstation = new ShipStationLibrary({');
  console.log('     apiKey: "your-v1-key",');
  console.log('     apiSecret: "your-v1-secret",');
  console.log('     v2ApiKey: "your-v2-key",');
  console.log('     timeout: 30000,');
  console.log('     retries: 3,');
  console.log('     mock: false');
  console.log('   });');
  
  console.log('\n3. Mock Mode (for testing):');
  console.log('   const shipstation = new ShipStationLibrary({');
  console.log('     apiKey: "test-key",');
  console.log('     apiSecret: "test-secret",');
  console.log('     mock: true');
  console.log('   });');
}

// Main demo function
async function runDemo() {
  try {
    await demoApiVersions();
    await demoMockMode();
    showConfigurationExamples();
  } catch (error) {
    console.error('Demo failed:', error.message);
  }
}

// Export for use in other files
module.exports = {
  demoApiVersions,
  demoMockMode,
  showConfigurationExamples,
  runDemo
};

// Run demo if this file is executed directly
if (require.main === module) {
  runDemo().catch(console.error);
}
