const mdb = require("../mdb.js");

mdb.connect().then(async ()=>{ 
    let items = await mdb.client().prod.collection("items").find().toArray();
    console.log("Processing items", items.length);
    let count = items.length;
    for (let item of items){
        if((item.inventory.in_stock)!==0){
            
            // Insert into the inventory collection                        
            await mdb.client().prod.collection("inventory").insertOne({
                "org_id":item.org_id,
                "item_id": item._id,
                "account":{
                    "_id":mdb.objectId("6425ef3b67c2b869c563d7c3"),
                    "email":"<EMAIL>"
                },
                "qty": item.inventory.in_stock,
                "details":"Initial stock copy.",
                "ts": new Date()                
            });

           /* await mdb.client().prod.collection("items").updateOne({"_id": item._id}, {
                "$unset":{
                    "inventory.on_order":"",
                    //"inventory.in_stock":""
                }
            }); */                     
        }
        console.log(count);
        count--;  
        
    }
    console.log("DONE");
});