const fs = require('fs');
const csv = require("fast-csv");
const mdb = require("../mdb.js");
let DATA = [];

async function processData(){
    for (let row of DATA){
        // Process each row as needed
        //console.log({"item_id":mdb.objectId(row["_id"].trim()), "details":"Initial stock copy."});  
        //console.log(parseInt(row["qty"].trim()));           

        let updated = await mdb.client().prod.collection("inventory").updateOne(
            {"item_id":mdb.objectId(row["_id"].trim()), "details":"Initial stock copy."},
            {"$set":{
                "qty": parseInt(row["qty"].trim()),
            }}
        )
        
        if(updated.matchedCount === 0){
            console.log(row["_id"].trim(), row["qty"]);
        }
   
    }
    console.log("DONE");
}

mdb.connect().then(async ()=>{ 
  

  fs.createReadStream("./sean.csv")
          .pipe(csv.parse({ headers: true }))
          .on('error', error => console.error(error))
          .on('data', row => DATA.push(row))
          .on('end', rowCount => { 
            processData()
                
          });

});