let Dropbox = require('dropbox');
const accessToken = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************8SMTCWrcKRhupWYBN0V7rjNnxGkJSiLSQISFTXmqbNaXhLNNtlExMY3-O-nLgbIWczw0gl075NqnJjCfeBWY6aB2PrXZrCrt-YKCmTViYOOTibd-wcnWygw1zhdXycEuQY0k4tyUT03mUcs63ulZ35eP6GVFy1VdvPwtlSHMf50KnoTvmsIxGMJajEyoKizc-BwI1BgDUwcK6UXmZ9fTQDk9YivLeh7LWeGvuaTY0zZ5FuVbGgslIh5mtF20e8mSSQmd_rkDOq8GvmfNiVHYvIh2ikn9WG2KwJioCz5XYhcgN_LiQtsGVKm_2VkKy61WgxZokhFWIj-AVZjdQ4X6ierRK8HrKabE7ZJ4-zLdOQ3qmQ0Rm295mPxwTY5yT-zKIwM0bxxaxxzx_sOiN4OfNQluRTtOD5nt10KF8MntChlGVIc3mr3Dc1M3wANFpsG2_tQg3AWPEhLoSYzWwoX7GSEUZ-RmNyMJ4Q3NIhpLJdoDJNZlS5rmxxCHED0CKz03X9gq3sjwo-qZvvJdKEx6Lc3aRcM_Av5DUvSuAvX_yxiYVBcxJxU44kqTzjjT-y2sWmx7eqH53kUldlKRITSxXvLT0EntW4p-aPQKdSEQfZKCErrZYrCkjmiktf36xKU868JOVXdca8FBPA_F3NPIsyITg2NBLKA_bho5ARfIO_zp6EKbxYKibg0eRYrHFw6Ja-3v_FPrnNkoGmp8MJEBfo0Avf7BcFfa_fIUffdTfP71GLpQgTF7_sbqhiFVxmnHlu_CMKeLThrIVbdqSYBjx7gRtY1dw";
const dbx = new Dropbox.Dropbox({ accessToken: accessToken });


dbx.filesListFolder({path: '/chef warren katz'})
  .then(function(response) {
    console.log(response.result.entries);
  })
  .catch(function(error) {
    console.error(error.error || error);
  });




/*
dbx.sharingGetSharedLinkFile({url: "https://www.dropbox.com/scl/fo/3rrpvybdib16iwkjvxgr0/AAo7_62oYihiGT-qep7eOeg/2025%20Lactalis%20Booth?preview=DSC04025.JPG&rlkey=4i7wa8z3uxvmtkdclu970wo8s&subfolder_nav_tracking=1&st=rcc4e7e6&dl=0"})
        .then(function(data) {
          console.log(data);
          //  (data.result.fileBlob)
        })
        .catch(function(error) {
          console.error(error.error || error);
        });
*/

/*
dbx.filesListFolder({path: 'https://www.dropbox.com/scl/fo/3rrpvybdib16iwkjvxgr0/AKaRXAuagPMrWPJO7UFMh1o?dl=0&rlkey=4i7wa8z3uxvmtkdclu970wo8s'})
        .then(function(response) {
          console.log('response', response)
          displayFiles(response.result.entries);
        })
        .catch(function(error) {
          console.error(error.error || error);
        });
*/