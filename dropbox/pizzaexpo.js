require('dotenv').config()
const { Dropbox } = require('dropbox');
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');
const sharp = require('sharp');
const mdb = require("../mdb.js");
const aws = require("../aws.js");
const dayjs = require("dayjs");
const csv = require("fast-csv");
const _ = require("lodash");
const ORGID = "6425eef367c2b869c563d7be";

const sharedLinkUrl = 'https://www.dropbox.com/scl/fo/3rrpvybdib16iwkjvxgr0/AKaRXAuagPMrWPJO7UFMh1o?rlkey=4i7wa8z3uxvmtkdclu970wo8s&e=6&st=qhv7lzph&dl=0';
const baseURL = 'https://www.dropbox.com/scl/fo/3rrpvybdib16iwkjvxgr0/AKaRXAuagPMrWPJO7UFMh1o/'; // ?dl=1
let IMAGES = [];

const accessToken = "sl.u.AFuErFKhecWZLsqrgYSnBooA12tEHsYoonRtlhst2f7pHfWVgcsVxD4xBjpZ3XwpXRc541M2hjPu3A0nnQv2CvXzN9PEUyoKl3DpDC9pfR1s33kqtE0Y9-7q363Q3TCwrfVX_upzjCOBduxLzPRqLygHf0Ulc98V3fRbb23wSqrEvw2L1_8Qke1E1NLfFlPhTeCPMLgHBaS5sIwNT8AX0FPAuLwHEpfkqJW_iShZx5YeoMwzC2w-CaVlmEJx4z5eXHZttwyAVONBE30v-9jb7KBsFeJpbM-UTiRezcAUFuToKUujuYGGpD-bsGN8X9HBwu8_ZPyPCLAwhSILLvwnvUbQxhSe7dOYkoxhToVv6qtqqS6XGjCWuUhfoBKT_LTMSz5LyUHmDx72oSHg0FNY1DwxZQYh95AWZeX-vIyveEHwG0hMkD9djqEgXqaYD7MO014MH1ZiDtxXTsTj4otDbCkzfXfWP7ewWbPilSJb0gENGPzXCPUaOhIULvBRNDGBAn2Zu2f278yedm3kLhC4d38JduvbhLgbz7Byv33RR8zTdmtkvTL-yMiOtiZeknCmk5jz-XTM_InXvaV2y-S2d7PtEYs7CmgL7GOxdVjXbXJlagBu_hySUMX_r6a8edlZxo_66j-l46x4g2zbl1Q6mTvH8p7jql1RLB4Ex-qGRLbdM-2IQFQk9z_mpPDc2Qv3ZpLg0KE3g6yHYeqMkS6JyvonfOSwPNJMn1jyVti3ogFl2oJqq8u-3dkqSizHBnId5Wo54qPyWq3oLR7jP58qJHp-lv3EkOZEPd_x9XfixUCrtbYZqbuOMcR2x4wQRThQF0l_8nhJym9TCsFzYJQXTqbU1GY_8lN5bzgKLplFUU3u3fPAk5ScCTj3l3z32mT5N1qqeKmEkKdkl9Vr-v3LHbI5cc3eNMTmoMqStSNPoLEqsrMbY7jDGmj9O_jv7cEGobBwIqtTcLJG67kovTkAk3lV2G-jR_KBvciCKmMtYNVgmgSUmTFjqtib-VgmVeKJWF63rtQ20endXg6V_RE_vUOiITAdKl5eqNOohbJ_zPIBOkyWuRQlON13ZkSPqDkobb4g4nkt19eUVEsifaAkDfzadUq4mjjsES1FnRMM8Hs-t-3juPIFw47BUfVvL9C7x45d50YLRhCsns1Pf1hCTbTcgVU4oZq-IA8hbdLRyGh0IsBV8OMw374i9xfiLu7ClqXA_quHCIiP0IntzBASvDuOI0y3q5wZIFUuiBk9uOAFcH3SVAeCLaThjV6g1xfm24H91godJN6XXVchcoKX_r-k";
const dbx = new Dropbox({ accessToken, fetch });

function safeFileName(str){
  return str.trim().toLowerCase().replace(/\s/ig, "_").replace(/[^a-z0-9._\/]/ig, "");
}

async function processImages(){
    for(let image of IMAGES){
        let imageURL = baseURL + encodeURIComponent(image.folder) + "/" + image.subfolder + "/" + image.file + "?dl=1";
        //let filePath = path.join(__dirname, "downloads", image.file);
                
        // Download the image
        const fileData = await dbx.sharingGetSharedLinkFile({
            url: imageURL
        });

        let itemNameObj = image.folder.split("-").map((item) => item.trim());
        let itemName = itemNameObj[1] + " - " + itemNameObj[0];
        let s3FileName = safeFileName(itemName)+"_"+image.file

        console.log("Starting to upload",s3FileName);

        try{
                await aws.putS3Object({
                  "key":ORGID+"/images/thumbs/"+s3FileName,
                  "body":await sharp(fileData.result.fileBinary)
                    .rotate()
                    .resize(400,400,{fit:"contain",background:{ r: 255, g: 255, b: 255 }})
                    .toBuffer(),
                  "encoding":"base64",
                  "contentType":"image/jpeg",
                  "acl":"public-read"
                });
                console.log("Uploaded THUMB");
              }catch(e){
                console.log("Error uploading THUMB",s3FileName,e);     
              }
        try{
            await aws.putS3Object({
              "key":ORGID+"/images/"+s3FileName,
              "body":await sharp(fileData.result.fileBinary)
                .rotate()
                .resize(2400)
                .toBuffer(),
              "encoding":"base64",
              "contentType":"image/jpeg",
              "acl":"public-read"
            });
            console.log("Uploaded IMAGE");
          }catch(e){
            console.log("Error uploading IMAGE",s3FileName,e);
          }

          let dataToSave={
                    "org_id":mdb.objectId(ORGID),
                    "name":image.item_name, 
                    "image":s3FileName,
                    "file":null,            
                    "actions":{
                        "link":true,
                        "download":true,
                        "email":true,
                        "order":false
                    },
                    "fields":{
                        "CASEDIM":"",
                        "CASEWEIGHT":"",
                        "COA":"",
                        "CUBE":"",
                        "DESCRIPTION":"Pizza Expo 2025",
                        "FULLNAME":"",
                        "GTIN":"",
                        "ITEMUPC":"",
                        "SKU":"",
                        "SHELFLIFE":"",
                        "TIExHEIGHT":"",
                        "UNITSIZE":"",
                        "WEIGHTMETHOD":"",
                        "PRICETYPE":""
                    },
                    "tags":["TVbf7ce4D5"],
                    "inventory":{
                        "in_stock":0,
                        "available":0,
                        "on_order":0,
                        "restock_level":0,
                        "oos_action":"-"                
                    },
                    "status":1,
                    "meta":{
                        "upload":"pizzaexpo2025",
                    },
                    "created":dayjs().toDate(),
                    "updated":dayjs().toDate()
                };

        // Add item in DB        
        let itemRecord = await mdb.client().prod.collection("items").insertOne(dataToSave);
        if(!itemRecord.insertedId){
            console.log("Error inserting item",itemName);
            fs.appendFileSync("./mongo_errors.csv",`"${image.item_name}"`+"\n");        
        }else{
            console.log("Inserted: ",image.item_name);
            fs.appendFileSync("./log.csv",`"${image.item_name}"`+"\n");
        }
          
    }
    
}


mdb.connect().then(async ()=>{ 



  fs.createReadStream("./subs.csv")
          .pipe(csv.parse({ headers: true }))
          .on('error', error => console.error(error))
          .on('data', row => IMAGES.push(row))
          .on('end', rowCount => { 
            //console.log(IMAGES)
            processImages();
          });

});