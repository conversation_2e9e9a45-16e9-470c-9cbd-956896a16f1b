require('dotenv').config()
const { Dropbox } = require('dropbox');
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');
const sharp = require('sharp');
const mdb = require("../mdb.js");
const aws = require("../aws.js");
const dayjs = require("dayjs");
const csv = require("fast-csv");
const _ = require("lodash");

let IMAGES = [];


const ITEMTAGID = "TVbf7ce4D5";
const ORGID = "6425eef367c2b869c563d7be";

// Replace with your Dropbox access token
const accessToken = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************Uk-qZdPQREs36NgY403yfi-b0nxCvGP4yckhbXt7QfYuSjyra5Qpf_uDjLnjo6EsRbTaReFbkxvuktCHvvejnD6BUWcTAn9sRjxuu6_9YH5gid3Ik0u2jUfclcei4SV6AqnC_O3dFdNHoX7xnJBXYmasbnpu4eMG1FRjgREcM8NYZZqqWx0y0AqIx3b1GWhEQGNXHWBXpGikAH_0h0QKu8DIZcTMTpfY_SlBaG5V9UR5v-qU7GwT4UD6Oo0klPlIF-VwsILQlh8N0G-m339JCp_p9DRKL5zC4F77BEmTQbNeiSDqoYAuqgX9nvvOU_P0Th-F0BTT47BZLmILL_UcXVtwLMVVmfbvOx4JYIGSISs579Wjs0GRT7SEg9i3bEzlowWZS0cGFOrz9nqqi4nzFZXhWvGqq9P4tK6m7_wlrGT1YJEkvgHr5pRbOTX_erywIBFLHhs75Cy14hHC7xtsO4hjSu-GLzMW2BH5-aFs0BjyQXL0tV5igvoxURQlC0Pb4WS2UvcsXz3XYf8A9amzr0Ub-8pzDE-AAMasdpFE3g6S3HKmdIKgtjenVPH1ixBe5OKOVPBz7gjOR3lefvpcL9T1jvNsuZqSVYJnXC920UOg5MG2nzgFTVkHpoPYhrQ928Qoo3mNafViIy8vHIlkzgKcLU0S7v4kOT9of3SXAgCQbaGJP4ymzmK0_vMZoEKtNw1nNFem9_g-GyUQEzyHZo17Vu6fffT6oxTGMWyUJ3r9TDOPzoBxnrh7UL9n0DpJeeRBwxSVlJ6sJgXBBqktHqinsvf2C";
const dbx = new Dropbox({ accessToken, fetch });

// Helper function to handle path encoding
const encodePath = (path) => {
  // For shared links, we need a different approach
  // Don't encode the path for shared links, just ensure it starts with empty or '/'
  return path === '' ? '' : `/${path.replace(/^\/+/, '')}`;
};

function safeFileName(str){
  return str.trim().toLowerCase().replace(/\s/ig, "_").replace(/[^a-z0-9._\/]/ig, "");
}

// Function to check if a file is an image based on its extension
function isImageFile(filename) {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp', '.svg', '.heic', '.raw', '.cr2', '.nef', '.arw', '.dng', '.JPG', '.JPEG', '.PNG'];
  const ext = path.extname(filename).toLowerCase();
  return imageExtensions.includes(ext) || imageExtensions.includes(ext.toUpperCase());
}


/**********************************************************************************
 *  DOWNLOAD FILE
 * This function takes a file entry, a shared link, and a relative path.
 * It constructs the full shared link to the specific file and downloads it.
 * The downloaded file is saved in a local directory structure that mirrors
 * the Dropbox folder structure. It also checks if the file is an image before downloading.
 * If the file is not an image, it skips the download.
 **********************************************************************************/
async function downloadFile(entry, sharedLink, relativePath = '') {
  // Skip if not an image file
  if (!isImageFile(entry.name)) {
    //console.log(`Skipping non-image file: ${entry.name}`);
    return;
  }

  try {
    //console.log(`Downloading: ${entry.name} | ${relativePath}`);
    // Construct the full shared link to the specific file
    const fileSharedLink = `${sharedLink.split('?')[0]}/${encodeURIComponent(relativePath ? `${relativePath}/${entry.name}` : entry.name)}?dl=1`;
    console.log(`\n\nGetting: ${fileSharedLink}`);
    
    const fileData = await dbx.sharingGetSharedLinkFile({
      url: fileSharedLink
    });

    let imageToUpload = _.find(IMAGES, { "folder": relativePath, "file": entry.name }); //.split(".")[0]
    if(imageToUpload){
      console.log("UPLOAD");
      console.log("relativePath: ",relativePath);
      console.log("entry.name: ",entry.name);
      console.log("sharedLink: ",sharedLink.split('?')[0]);

      /*************************************************************************************/
      let itemNameObj = relativePath.split("-").map((item) => item.trim());
      let itemName = itemNameObj[1] + " - " + itemNameObj[0];
      let s3FileName = safeFileName(itemName)+"_"+entry.name
      
      console.log("Starting to upload",s3FileName);

      // Now Upload to S3
      try{
        await aws.putS3Object({
          "key":ORGID+"/images/thumbs/"+s3FileName,
          "body":await sharp(fileData.result.fileBinary)
            .resize(400,400,{fit:"contain",background:{ r: 255, g: 255, b: 255 }})
            .toBuffer(),
          "encoding":"base64",
          "contentType":"image/jpeg",
          "acl":"public-read"
        });
        console.log("Uploaded THUMB");
      }catch(e){
        console.log("Error uploading THUMB",s3FileName,e);     
      }
      

      try{
        await aws.putS3Object({
          "key":ORGID+"/images/"+s3FileName,
          "body":await sharp(fileData.result.fileBinary)
            .resize(2400)
            .toBuffer(),
          "encoding":"base64",
          "contentType":"image/jpeg",
          "acl":"public-read"
        });
        console.log("Uploaded IMAGE");
      }catch(e){
        console.log("Error uploading IMAGE",s3FileName,e);
      }
      

      let dataToSave={
          "org_id":mdb.objectId(ORGID),
          "name":imageToUpload.item_name, 
          "image":s3FileName,
          "file":null,            
          "actions":{
              "link":true,
              "download":true,
              "email":true,
              "order":false
          },
          "fields":{
              "CASEDIM":"",
              "CASEWEIGHT":"",
              "COA":"",
              "CUBE":"",
              "DESCRIPTION":"Pizza Expo 2025",
              "FULLNAME":"",
              "GTIN":"",
              "ITEMUPC":"",
              "SKU":"",
              "SHELFLIFE":"",
              "TIExHEIGHT":"",
              "UNITSIZE":"",
              "WEIGHTMETHOD":"",
              "PRICETYPE":""
          },
          "tags":["TVbf7ce4D5"],
          "inventory":{
              "in_stock":0,
              "available":0,
              "on_order":0,
              "restock_level":0,
              "oos_action":"nothing"                
          },
          "status":1,
          "meta":{
              "upload":"pizzaexpo2025",
          },
          "created":dayjs().toDate(),
          "updated":dayjs().toDate()
      };

      // Add item in DB
      let itemRecord = await mdb.client().prod.collection("items").insertOne(dataToSave);
      if(!itemRecord.insertedId){
        console.log("Error inserting item",itemName);
        fs.appendFileSync("./mongo_errors.csv",`"${relativePath}","${entry.name}"`+"\n");        
      }else{
        console.log("Inserted: ",itemRecord.insertedId);
        fs.appendFileSync("./log.csv",`"${relativePath}","${entry.name}"`+"\n");
      }
      /**************************************************************************************/

    }else{
      fs.appendFileSync("./skiplog.csv",`"${relativePath}","${entry.name}"`+"\n");
      console.log("\n\nSKIP",relativePath, entry.name.split(".")[0]);
    }
    
    /*
      // Create local directory structure
      const localPath = path.join(__dirname, 'downloads', relativePath, entry.name);
      fs.mkdirSync(path.dirname(localPath), { recursive: true });
      
      // Write the file to disk
      fs.writeFileSync(localPath, fileData.result.fileBinary);
  */  
    //console.log(`✅ Downloaded: ${entry.name}`);
  } catch (err) {
    console.error(`❌ Failed to download ${entry.name}:`, err.message);
    console.error('Error details:', err.error || err);
    process.exit(1);
  }
}



/**********************************************************************************
 *  PROCESS FOLDER
 * This function takes a relative path and a shared link, lists the folder contents,
 * and recursively processes each entry. If an entry is a folder, it calls itself
 * with the new relative path. If an entry is a file, it calls the downloadFile function.
 **********************************************************************************/
// Function to recursively list and download folder contents
async function processFolder(relativePath = '', sharedLink, indent = '') {
  try {
    const formattedPath = encodePath(relativePath);
    //console.log(`Listing folder: "${formattedPath}"`);
    
    const result = await dbx.filesListFolder({
      path: formattedPath,
      shared_link: { url: sharedLink }
    });

    // Process entries
    for (const entry of result.result.entries) {
      //console.log(`${indent}- ${entry.name} (${entry['.tag']})`);
      
      if (entry['.tag'] === 'folder') {
        const childPath = relativePath === '' ? entry.name : `${relativePath}/${entry.name}`;
        await processFolder(childPath, sharedLink, indent + '  ');
      } else if (entry['.tag'] === 'file') {
        await downloadFile(entry, sharedLink, relativePath);
      }
    }

    // Handle pagination
    let cursor = result.result.has_more ? result.result.cursor : null;
    while (cursor) {
      const continueResult = await dbx.filesListFolderContinue({ cursor });
      for (const entry of continueResult.result.entries) {        
        if (entry['.tag'] === 'folder') {
          const childPath = relativePath === '' ? entry.name : `${relativePath}/${entry.name}`;
          await processFolder(childPath, sharedLink, indent + '  ');
        } else if (entry['.tag'] === 'file') {
          await downloadFile(entry, sharedLink, relativePath);
        }
      }
      cursor = continueResult.result.has_more ? continueResult.result.cursor : null;
    }
  } catch (error) {
    console.error('Error processing folder:', error);
    console.error('Error details:', error.error || error);
  }
}


/**********************************************************************************
 *  DOWNLOAD FROM SHARED LINK
 *  This function takes a shared link and downloads all files within the folder.
 *  It uses the Dropbox API to list the folder contents and download each file.
 *  The downloaded files are saved in a local directory structure that mirrors
 *  the Dropbox folder structure.
 **********************************************************************************/
async function downloadFromSharedLink(sharedLink) {
  try {
    const metadata = await dbx.sharingGetSharedLinkMetadata({ url: sharedLink });
    
    if (metadata.result['.tag'] !== 'folder') {
      throw new Error('The shared link does not point to a folder.');
    }
    
    //console.log(`\n📂 Downloading contents of: ${metadata.result.name}\n`);
    await processFolder('', sharedLink);
    //console.log('\n✅ Download complete!');
  } catch (error) {
    console.error('Error:', error);
  }
}

/*

*/

mdb.connect().then(async ()=>{ 

  const sharedLinkUrl = 'https://www.dropbox.com/scl/fo/3rrpvybdib16iwkjvxgr0/AKaRXAuagPMrWPJO7UFMh1o?rlkey=4i7wa8z3uxvmtkdclu970wo8s&e=6&st=qhv7lzph&dl=0';

  fs.createReadStream("./images.csv")
          .pipe(csv.parse({ headers: true }))
          .on('error', error => console.error(error))
          .on('data', row => IMAGES.push(row))
          .on('end', rowCount => { 
            //console.log(IMAGES)
            downloadFromSharedLink(sharedLinkUrl);            
          });

});