const { Dropbox } = require('dropbox');
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

const accessToken = "sl.u.AFsV9ZOpSkHMi-4jNGGEi9fu9aePPK1gMzlVqMwb1XNXMI6FXN9DyIQSLm47KK9OVduAO_yHaXcMAzmJjEiNm-KjNIQJ6VhaFWIKgvSrDhEEpy665Yavrrt2SJjN3hxsscGhZpHDo6lbsJ8okqI1e7wLGXkWqVKdq1STPjo20MXz5wpDqSvWQmmQ-8pqSstUElwK9Cp4dNuw7ebDbhglmP9FWW9IkGRs2aQ5N4hVsU6mBvaGI-jiFOaICrj0q1e5N2rcsfjbwbZH56IQPiM3oe0fhQVDTmamqGapkoWyKLUyzlMR3xKD9mGANAACW9ZMeJM5PRCc6HPnVjo8g4VHYKyN5igKFAVta7T5VwR1HCWF_zkX-a8O3UDfxhdsM_7GdZoT4E3UHZ3E-ugKtF3S7jUX3p4F4qUrQToz4vIYOBtQZ6t5d7HVaDhz_YUmp7HnbnaUDO7D7a2wOvtA3L6qPyOJgedz1QocFbZpbncM1Kpk1Da1SMjTryuqMbChzU2mRdqQfE-4XB6F7OFpn5o7gtnRtsm16RxX80FvcSRfPJPNk_6jecfZIFG-FEhubb_z6itTJZkfjfTV9FbyOsGIs_xrBWbVfMDKOEo6OOvUyyxB5bliIFKwPHhmHLqzW5_SpCEUU9wg5X2tMyb9SbPn2c1dqhIJ7aF0GodX-vk0jQ8sx5aFE5yRUycRhkm9Kvij6VnAnRdfsrqmwXq2R8hiif7I1w2Bz_-P90zKW2byL5EkEPF-sq-ibNso8D__cZcpSOzNjKEjx0e5LchccnLy9SYWm2L_iziYV9gjcpmf6O7ro53BSLbqwAy--qiPDbFqpXJTs4bTRtPcnxF4hGHXZtmdRN2g39Q3FiEt0XU6RBDJveyFxhl8nt7Hdw2EWFDaDA98ySxnyA_No9T-mJ6OWp6mJ_HGcmcYN0NDxD_LIjH3G3aotpkLTmvuxWkwLACsQBb1PEkAEMP0C2V-NhpnymRpjfLVVRBu5Eop7Ulz-68pD5cYdQivwYLxZlJDCYuSEwCPF8aj9pjC2y64d8q562aTQdp2_UherBzEC1lw78VfVOXkQnOhNPHGhuK5hVr7O9ACkmr7QOLsa0kOQW8A7HiC5gbqe6-qsfWDhza2GCdRoA1B6RDLtBh6fIVaOO0C7rcQIfXnCeHuFhA3_4OSRvAnQihL6JvkKZa2eVhzFftoQi2KUPvQ849sIVXGQEra_9JOcYSSBPkhOMBMvkGRWYw7Qt7eYsMods2YjBMfz9ibbduvXPWc3erOqGTZnQ0ctvW0-u0adAIBry59cl9p-pTJ";
const dbx = new Dropbox({ "accessToken": accessToken, fetch });
const sharedLinkUrl = 'https://www.dropbox.com/scl/fo/3rrpvybdib16iwkjvxgr0/AKaRXAuagPMrWPJO7UFMh1o?rlkey=4i7wa8z3uxvmtkdclu970wo8s&e=6&st=qhv7lzph&dl=0';


// Helper function to handle path encoding
const encodePath = (path) => {
    return encodeURIComponent(path).replace(/%2F/g, '/'); // Ensure '/' is correctly encoded
  };
  
  async function downloadFile(entry, sharedLink, relativePath = '') {
    try {
      // Construct file path using the relativePath
      const filePath = (relativePath ? `${relativePath}/${entry.name}` : entry.name).replace(/^\/+/, '');
  
      console.log(filePath)
      return
      // Get temporary link for the file
      const tempLinkRes = await dbx.filesGetTemporaryLink({
        path: filePath,
        shared_link: { url: sharedLink },
      });
  
      

      const downloadUrl = tempLinkRes.result.link;
  
      // Build local file path
      const localPath = path.join(__dirname, 'downloads', filePath);
      fs.mkdirSync(path.dirname(localPath), { recursive: true });
  
      const res = await fetch(downloadUrl);
      if (!res.ok) throw new Error(`Failed to fetch file: ${res.statusText}`);
  
      const fileStream = fs.createWriteStream(localPath);
      await new Promise((resolve, reject) => {
        res.body.pipe(fileStream);
        res.body.on('error', reject);
        fileStream.on('finish', resolve);
      });
  
      console.log(`✅ Downloaded: ${filePath}`);
    } catch (err) {
      console.error(`❌ Failed to download ${entry.name}:`, err.message);
    }
  }
  
  // Function to list and download folder contents
  async function listFolderRecursive(relativePath = '', sharedLink, indent = '') {
    try {
      const encodedPath = encodePath(relativePath); // URL-encode the relative path
      const result = await dbx.filesListFolder({
        path: encodedPath,  // Path here should be encoded
        shared_link: { url: sharedLink }
      });
  
      for (const entry of result.result.entries) {
        console.log(`${indent}- ${entry.name} (${entry['.tag']})`);
  
        if (entry['.tag'] === 'folder') {
          const childPath = relativePath === '' ? entry.name : `${relativePath}/${entry.name}`;
          await listFolderRecursive(childPath, sharedLink, indent + '  ');
        } else if (entry['.tag'] === 'file') {
          await downloadFile(entry, sharedLink, relativePath); // Pass relativePath to downloadFile
        }
      }
  
      // Handle pagination for more entries
      let cursor = result.result.has_more ? result.result.cursor : null;
      while (cursor) {
        const continueResult = await dbx.filesListFolderContinue({ cursor });
        for (const entry of continueResult.result.entries) {
          console.log(`${indent}- ${entry.name} (${entry['.tag']})`);
          if (entry['.tag'] === 'folder') {
            const childPath = relativePath === '' ? entry.name : `${relativePath}/${entry.name}`;
            await listFolderRecursive(childPath, sharedLink, indent + '  ');
          } else if (entry['.tag'] === 'file') {
            await downloadFile(entry, sharedLink, relativePath); // Pass relativePath to downloadFile
          }
        }
        cursor = continueResult.result.has_more ? continueResult.result.cursor : null;
      }
  
    } catch (error) {
      console.error('Error listing folder:', error);
    }
  }
  
  async function listSharedFolder(link) {
    try {
      const metadata = await dbx.sharingGetSharedLinkMetadata({ url: link });
  
      if (metadata.result['.tag'] !== 'folder') {
        throw new Error('The shared link does not point to a folder.');
      }
  
      console.log(`\n📂 Listing and downloading contents of: ${metadata.result.name}\n`);
      await listFolderRecursive('', link);  // Start from root of shared folder
    } catch (error) {
      console.error('Error:', error);
    }
  }
  
  listSharedFolder(sharedLinkUrl);
  