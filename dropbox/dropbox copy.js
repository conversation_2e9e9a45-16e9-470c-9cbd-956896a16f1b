const { Dropbox } = require('dropbox');
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

// Replace with your Dropbox access token
const accessToken = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************hzU2mRdqQfE-4XB6F7OFpn5o7gtnRtsm16RxX80FvcSRfPJPNk_6jecfZIFG-FEhubb_z6itTJZkfjfTV9FbyOsGIs_xrBWbVfMDKOEo6OOvUyyxB5bliIFKwPHhmHLqzW5_SpCEUU9wg5X2tMyb9SbPn2c1dqhIJ7aF0GodX-vk0jQ8sx5aFE5yRUycRhkm9Kvij6VnAnRdfsrqmwXq2R8hiif7I1w2Bz_-P90zKW2byL5EkEPF-sq-ibNso8D__cZcpSOzNjKEjx0e5LchccnLy9SYWm2L_iziYV9gjcpmf6O7ro53BSLbqwAy--qiPDbFqpXJTs4bTRtPcnxF4hGHXZtmdRN2g39Q3FiEt0XU6RBDJveyFxhl8nt7Hdw2EWFDaDA98ySxnyA_No9T-mJ6OWp6mJ_HGcmcYN0NDxD_LIjH3G3aotpkLTmvuxWkwLACsQBb1PEkAEMP0C2V-NhpnymRpjfLVVRBu5Eop7Ulz-68pD5cYdQivwYLxZlJDCYuSEwCPF8aj9pjC2y64d8q562aTQdp2_UherBzEC1lw78VfVOXkQnOhNPHGhuK5hVr7O9ACkmr7QOLsa0kOQW8A7HiC5gbqe6-qsfWDhza2GCdRoA1B6RDLtBh6fIVaOO0C7rcQIfXnCeHuFhA3_4OSRvAnQihL6JvkKZa2eVhzFftoQi2KUPvQ849sIVXGQEra_9JOcYSSBPkhOMBMvkGRWYw7Qt7eYsMods2YjBMfz9ibbduvXPWc3erOqGTZnQ0ctvW0-u0adAIBry59cl9p-pTJ";
const dbx = new Dropbox({ accessToken, fetch });

// Helper function to handle path encoding
const encodePath = (path) => {
  // For shared links, we need a different approach
  // Don't encode the path for shared links, just ensure it starts with empty or '/'
  return path === '' ? '' : `/${path.replace(/^\/+/, '')}`;
};

async function downloadFile(entry, sharedLink, relativePath = '') {
  try {
    console.log(`Downloading: ${entry.name} | ${relativePath}`);
    
    // Construct the full shared link to the specific file
    const fileSharedLink = `${sharedLink.split('?')[0]}/${encodeURIComponent(relativePath ? `${relativePath}/${entry.name}` : entry.name)}?dl=1`;
    console.log(`Using shared link: ${fileSharedLink}`);
    
    const fileData = await dbx.sharingGetSharedLinkFile({
      url: fileSharedLink
    });
    
    // Create local directory structure
    const localPath = path.join(__dirname, 'downloads', relativePath, entry.name);
    fs.mkdirSync(path.dirname(localPath), { recursive: true });
    
    // Write the file to disk
    fs.writeFileSync(localPath, fileData.result.fileBinary);
    
    console.log(`✅ Downloaded: ${entry.name}`);
  } catch (err) {
    console.error(`❌ Failed to download ${entry.name}:`, err.message);
    console.error('Error details:', err.error || err);
  }
}

// Function to recursively list and download folder contents
async function processFolder(relativePath = '', sharedLink, indent = '') {
  try {
    const formattedPath = encodePath(relativePath);
    console.log(`Listing folder: "${formattedPath}"`);
    
    const result = await dbx.filesListFolder({
      path: formattedPath,
      shared_link: { url: sharedLink }
    });

    // Process entries
    for (const entry of result.result.entries) {
      console.log(`${indent}- ${entry.name} (${entry['.tag']})`);
      
      if (entry['.tag'] === 'folder') {
        const childPath = relativePath === '' ? entry.name : `${relativePath}/${entry.name}`;
        await processFolder(childPath, sharedLink, indent + '  ');
      } else if (entry['.tag'] === 'file') {
        await downloadFile(entry, sharedLink, relativePath);
      }
    }

    // Handle pagination
    let cursor = result.result.has_more ? result.result.cursor : null;
    while (cursor) {
      const continueResult = await dbx.filesListFolderContinue({ cursor });
      for (const entry of continueResult.result.entries) {
        console.log(`${indent}- ${entry.name} (${entry['.tag']})`);
        if (entry['.tag'] === 'folder') {
          const childPath = relativePath === '' ? entry.name : `${relativePath}/${entry.name}`;
          await processFolder(childPath, sharedLink, indent + '  ');
        } else if (entry['.tag'] === 'file') {
          await downloadFile(entry, sharedLink, relativePath);
        }
      }
      cursor = continueResult.result.has_more ? continueResult.result.cursor : null;
    }
  } catch (error) {
    console.error('Error processing folder:', error);
    console.error('Error details:', error.error || error);
  }
}

async function downloadFromSharedLink(sharedLink) {
  try {
    const metadata = await dbx.sharingGetSharedLinkMetadata({ url: sharedLink });
    
    if (metadata.result['.tag'] !== 'folder') {
      throw new Error('The shared link does not point to a folder.');
    }
    
    console.log(`\n📂 Downloading contents of: ${metadata.result.name}\n`);
    await processFolder('', sharedLink);
    console.log('\n✅ Download complete!');
  } catch (error) {
    console.error('Error:', error);
  }
}

// Usage
const sharedLinkUrl = 'https://www.dropbox.com/scl/fo/3rrpvybdib16iwkjvxgr0/AKaRXAuagPMrWPJO7UFMh1o?rlkey=4i7wa8z3uxvmtkdclu970wo8s&e=6&st=qhv7lzph&dl=0';
downloadFromSharedLink(sharedLinkUrl);
