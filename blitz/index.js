const fs = require('fs');
const csv = require("fast-csv");
const mdb = require("../mdb.js");
const _ = require("lodash");
let CONTACTS = [], DATA=[], OUTPUT=[], FOUND=[];

async function processData(){
    //console.log(OUTPUT)
    let i=0;
    for(let O of DATA){
        if(!O["First Name"] || O["First Name"]==""){
            let availableContacts = _.filter(CONTACTS,{"Company Name":O["Company"]});
            if(availableContacts.length>0){                
                i++;
                availableContacts.forEach(C=>{
                    FOUND.push({
                        "State Sort": O["State Sort"],
                        "Company": O["Company"],
                        "Account Assignment": O["Account Assignment"],
                        "Parent Company/Owner": O["Parent Company/Owner"],
                        "Website": O["Website"],
                        "Chain Count": O["Chain Count"],
                        "Total 2024 US Units": O["Total 2024 US Units"],
                        "First Name":C["First Name"],
                        "Last Name":C["Last Name"],
                        "Job Title":C["Title"],
                        "Email":C["Email"],
                        "Stream":O["Stream"]                        
                    });
                });

                
          } else {
                console.log("No contacts found for company: " + O["Company"]);
                FOUND.push(O)
            }
            
        }else{
            FOUND.push(O)
        }
    }

    console.log("Found " + FOUND.length);

csv.writeToPath("./woot.csv", FOUND, { headers: true })
  .on('error', err => console.error('Error writing CSV:', err))
  .on('finish', () => console.log('CSV file written'));

    
}
  

  fs.createReadStream("./contacts.csv")
          .pipe(csv.parse({ headers: true }))
          .on('error', error => console.error(error))
          .on('data', row => CONTACTS.push(row))
          .on('end', rowCount => { 
            fs.createReadStream("./mmhealy.csv")
                .pipe(csv.parse({ headers: true }))
                .on('error', error => console.error(error))
                .on('data', row => DATA.push(row))
                .on('end', rowCount => { 
                    processData()
                        
                });
                    
          });

